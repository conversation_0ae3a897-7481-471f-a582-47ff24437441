package com.facishare.crm.fmcg.fesco.model.fesco;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public interface CreateAsyncTask {

    @Data
    @ToString
    final class Arg implements Serializable {

        private int templateId;

        private String thirdTaskId;

        private String thirdProjectName;

        private String checkMode;

        private List<ChildTaskArg> childTaskList;
    }

    @Data
    @ToString
    final class ChildTaskArg implements Serializable {

        private String name;

        private String idCard;

        private BigDecimal money;

        private String cardNo;

        private String bankId;

        private String content;

        private String thirdSubId;

        private String phone;

        private String signFile;
    }

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    final class Result extends FescoCommonResult<TaskResultData> {

    }

    @Data
    @ToString
    final class TaskResultData implements Serializable {

        private int templateId;

        private String thirdTaskId;

    }

}
