package com.facishare.crm.fmcg.fesco.model.apl;

import com.facishare.crm.fmcg.fesco.model.fesco.GetUserAuth;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface AplCancelUser {

    @Data
    @ToString
    final class Arg implements Serializable {

        private String id;

        private String reason;
    }
}
