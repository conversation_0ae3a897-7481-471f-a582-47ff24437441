package com.facishare.crm.fmcg.fesco.model.fesco;

import com.fmcg.framework.http.contract.fesco.FescoResult;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class FescoCommonResult<T extends Serializable> implements Serializable {

    private int code;

    private boolean success;

    private String message;

    private T data;

    public static <T extends Serializable> FescoCommonResult<T> success(T data) {
        FescoCommonResult<T> result = new FescoCommonResult<>();
        result.setSuccess(true);
        result.setData(data);
        result.setMessage("success");
        result.setCode(0);
        return result;
    }

    public static <T extends Serializable> FescoCommonResult<T> failed(FescoResult.Result remoteResult) {
        FescoCommonResult<T> result = new FescoCommonResult<>();
        result.setSuccess(remoteResult.isSuccess());
        result.setMessage(remoteResult.getMessage());
        result.setCode(remoteResult.getCode());
        return result;
    }

    public static <T extends Serializable> FescoCommonResult<T> failed(int code, String message) {
        FescoCommonResult<T> result = new FescoCommonResult<>();
        result.setSuccess(false);
        result.setMessage(message);
        result.setCode(code);
        return result;
    }

    public static <T extends Serializable> FescoCommonResult<T> failed(int code, String message, T data) {
        FescoCommonResult<T> result = new FescoCommonResult<>();
        result.setSuccess(false);
        result.setMessage(message);
        result.setCode(code);
        result.setData(data);
        return result;
    }

    public static <T extends Serializable> FescoCommonResult<T> unknownException(Exception ex) {
        FescoCommonResult<T> result = new FescoCommonResult<>();
        result.setSuccess(false);
        result.setMessage(ex.getMessage());
        result.setCode(500001);
        return result;
    }
}
