{"return_type": "number", "describe_api_name": "TPMStoreWriteOffObj", "auto_adapt_places": false, "is_unique": false, "description": "", "type": "count", "decimal_places": 2, "sub_object_describe_apiname": "TPMStoreWriteOffCashingProductObj", "is_required": false, "wheres": [], "define_type": "package", "is_single": false, "field_api_name": "store_write_off_id", "is_index": true, "default_result": "d_null", "is_active": true, "is_encrypted": false, "count_type": "sum", "count_field_api_name": "total_price", "label": "合计奖励产品费用", "count_to_zero": false, "api_name": "cashing_product_total_price", "count_field_type": "formula", "is_index_field": false, "round_mode": 4, "help_text": "", "status": "new"}