package com.facishare.crm.fmcg.tpm.service.abstraction;

import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.paas.auth.model.RolePojo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/25 11:30
 */
@SuppressWarnings("unused")
public interface IRoleService {

    Map<String, String> queryRoleNames(Integer enterpriseId);

    Map<String, String> queryRoleCodes(Integer enterpriseId);

    List<Integer> queryEmployeeIdsByRoleIds(int enterpriseId, List<String> roleIds);

    List<Integer> queryEmployeeIdsByRoleIds(int enterpriseId, List<String> roleIds, boolean isOnlyMainRole);

    Map<Integer, String> queryRoleByEmployeeIds(Integer enterpriseId, List<Integer> employeeIds);

    List<String> queryRoleByEmployeeId(int enterpriseId, int employeeId);

    List<String> queryRoleCodeListByUserId(String enterpriseId, int employeeId);

    Map<String, List<SimpleDTO>> queryOuterRoleByOuterInfo(String upstreamTenantId, String outerTenantId, List<String> outerUserIds);

    Map<String, String> queryAllOuterRoleCode2RoleName(String upstreamTenantId);

    String getRoleName(String tenantId, String roleCode);

    SimpleDTO getMainRoleByEmployeeId(String tenantId, String employeeId);

    List<RolePojo> queryExistRole(String tenantId, Set<String> roleCodes);
}
