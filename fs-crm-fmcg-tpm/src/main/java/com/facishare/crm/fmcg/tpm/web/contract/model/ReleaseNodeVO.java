package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/28 下午8:11
 */
@Data
@ToString
public class ReleaseNodeVO implements Serializable {

    // 释放对象
    @JSONField(name = "release_api_name")
    @JsonProperty(value = "release_api_name")
    @SerializedName("release_api_name")
    private String releaseApiName;
    // 释放对象的业务类型 / 活动类型
    @JSONField(name = "release_record_type")
    @JsonProperty(value = "release_record_type")
    @SerializedName("release_record_type")
    private String releaseRecordType;

    //触发时机
    @JSONField(name = "trigger_time")
    @JsonProperty(value = "trigger_time")
    @SerializedName("trigger_time")
    private String triggerTime;

    @JSONField(name = "approval_trigger_time")
    @JsonProperty(value = "approval_trigger_time")
    @SerializedName("approval_trigger_time")
    private String approvalTriggerTime;

    @JSONField(name = "change_filed")
    @JsonProperty(value = "change_filed")
    @SerializedName("change_filed")
    private List<String> changeField;

    // 条件code
    @JSONField(name = "condition_code")
    @JsonProperty(value = "condition_code")
    @SerializedName("condition_code")
    private String conditionCode;

    //触发条件
    @JSONField(name = "where_condition")
    @JsonProperty(value = "where_condition")
    @SerializedName("where_condition")
    private List<BudgetWhereConditionVO> whereConditions;

    private String previewString;

    //来源字段
    @JSONField(name = "source_field")
    @JsonProperty(value = "source_field")
    @SerializedName("source_field")
    private String sourceField;

    @JSONField(name = "referenced_field_api_name")
    @JsonProperty(value = "referenced_field_api_name")
    @SerializedName("referenced_field_api_name")
    private String referencedFieldApiName;

}