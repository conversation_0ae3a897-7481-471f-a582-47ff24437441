package com.facishare.crm.fmcg.tpm.web.service;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetNewConsumeRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTemplatePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.MongoPO;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.web.contract.DeleteBudgetData;
import com.facishare.crm.fmcg.tpm.web.manager.BudgetTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IDeleteBudgetDataService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.common.StopWatch;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.mongodb.morphia.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/26 11:33
 */
@Service
@SuppressWarnings("Duplicates")
@Slf4j
public class DeleteBudgetDataService extends BaseService implements IDeleteBudgetDataService {

    protected static final Set<Class<?>> MONGO_PO_CLASS_LIST = Sets.newHashSet();
    protected static final Set<String> OBJECT_API_NAME_LIST = Sets.newHashSet();
    protected static String safeCode = "";

    static {
        ConfigFactory.getConfig("gray-rel-fmcg", con -> safeCode = con.get("FMCG_TPM_DELETE_BUDGET_DATA_SAFE_CODE"));

        MONGO_PO_CLASS_LIST.add(BudgetTemplatePO.class);
        MONGO_PO_CLASS_LIST.add(BudgetTypePO.class);
        MONGO_PO_CLASS_LIST.add(BudgetNewConsumeRulePO.class);

        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCOUNT);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_STATISTIC_TABLE);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_TRANSFER_DETAIL);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_OCCUPATION_DETAIL);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_CARRY_FORWARD);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_CARRY_FORWARD_DETAIL);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_DISASSEMBLY_EXISTS_DETAIL_OBJ);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_DISASSEMBLY_NEW_DETAIL_OBJ);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCRUAL_OBJ);
        OBJECT_API_NAME_LIST.add(ApiNames.TPM_BUDGET_ACCRUAL_DETAIL_OBJ);
    }

    @Resource
    protected DatastoreExt mongoContext;
    @Resource
    private BudgetTypeManager budgetTypeManager;

    @Override
    public DeleteBudgetData.Result delete(DeleteBudgetData.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        if (Strings.isNullOrEmpty(context.getTenantId()) || !TPMGrayUtils.allowDeleteBudgetData(context.getTenantId())) {
            throw new ValidateException("delete action not support.");
        }

        if (!safeCode.equals(arg.getCode())) {
            throw new ValidateException("bad boy.");
        }

        String traceId = String.format("DELETE_BUDGET_DATA.%s.%s", context.getTenantId(), UUID.randomUUID().toString());
        TraceContext traceContext = TraceContext.get();
        traceContext.setTraceId(traceId);
        StopWatch watch = new StopWatch(traceId);

        for (Class<?> clazz : MONGO_PO_CLASS_LIST) {
            watch.start(String.format("delete mongo data : %s", clazz.getName()));
            deleteMongoData(context.getTenantId(), clazz);
            watch.stop();
        }

        for (String apiName : OBJECT_API_NAME_LIST) {
            watch.start(String.format("delete object data : %s", apiName));
            deleteObjectData(context.getTenantId(), apiName);
            watch.stop();
        }

        for (String apiName : OBJECT_API_NAME_LIST) {
            watch.start(String.format("delete invalid object data : %s", apiName));
            deleteInvalidObjectData(context.getTenantId(), apiName);
            watch.stop();
        }

        watch.start("publish sync select one field task");
        budgetTypeManager.publishSyncBudgetTypeFieldTask(context.getTenantId());
        budgetTypeManager.publishSyncBudgetNodeFieldTask(context.getTenantId());
        watch.stop();

        watch.logSlow(10, TimeUnit.SECONDS);

        return new DeleteBudgetData.Result();
    }

    @Override
    public DeleteBudgetData.Result deleteOnlyObjectData(DeleteBudgetData.Arg arg) {
        ApiContext context = ApiContextManager.getContext();
        super.permissionCheckForBudget(context);

        if (Strings.isNullOrEmpty(context.getTenantId()) || !TPMGrayUtils.allowDeleteBudgetObjectData(context.getTenantId())) {
            throw new ValidateException("delete action not support.");
        }

        if (!safeCode.equals(arg.getCode())) {
            throw new ValidateException("bad boy.");
        }

        String traceId = String.format("DELETE_BUDGET_DATA.%s.%s", context.getTenantId(), UUID.randomUUID().toString());
        TraceContext traceContext = TraceContext.get();
        traceContext.setTraceId(traceId);
        StopWatch watch = new StopWatch(traceId);

        for (String apiName : OBJECT_API_NAME_LIST) {
            watch.start(String.format("delete object data : %s", apiName));
            deleteObjectData(context.getTenantId(), apiName);
            watch.stop();
        }

        for (String apiName : OBJECT_API_NAME_LIST) {
            watch.start(String.format("delete invalid object data : %s", apiName));
            deleteInvalidObjectData(context.getTenantId(), apiName);
            watch.stop();
        }

        watch.logSlow(10, TimeUnit.SECONDS);

        return new DeleteBudgetData.Result();
    }

    /**
     * delete data for mongo db
     *
     * @param tenantId tenant id
     * @param clazz    po class
     * @param <T>      po
     */
    private <T> void deleteMongoData(String tenantId, Class<T> clazz) {
        try {
            Query<T> query = mongoContext.createQuery(clazz).field(MongoPO.F_TENANT_ID).equal(tenantId);
            mongoContext.delete(query);
            log.info("delete mongo data {} success.", clazz.getName());
        } catch (Exception ex) {
            log.info(String.format("delete mongo data : %s, cause unknown exception.", clazz.getName()), ex);
        }
    }

    /**
     * delete data for object data
     *
     * @param tenantId tenant id
     * @param apiName  object api name
     */
    private void deleteObjectData(String tenantId, String apiName) {
        try {
            List<IObjectData> data = queryObjectData(tenantId, apiName);
            invalidAndDeleteObjectData(tenantId, data);
            log.info("delete object data {} success.", apiName);
        } catch (Exception ex) {
            log.info(String.format("delete object data : %s, cause unknown exception.", apiName), ex);
        }
    }

    private void deleteInvalidObjectData(String tenantId, String apiName) {
        try {
            List<IObjectData> data = queryInvalidObjectData(tenantId, apiName);
            deleteObjectData(tenantId, data);
            log.info("delete invalid object data {} success.", apiName);
        } catch (Exception ex) {
            log.info(String.format("delete invalid object data : %s, cause unknown exception.", apiName), ex);
        }
    }

    @NotNull
    private List<IObjectData> queryObjectData(String tenantId, String apiName) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("false"));
        stq.setFilters(Lists.newArrayList(delFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private void invalidAndDeleteObjectData(String tenantId, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkInvalidAndDeleteWithSuperPrivilege(partition, sys);
        }
    }

    @NotNull
    private List<IObjectData> queryInvalidObjectData(String tenantId, String apiName) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setLimit(-1);
        stq.setOffset(0);
        stq.setSearchSource("db");
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);

        Filter statusFilter = new Filter();
        statusFilter.setFieldName(CommonFields.LIFE_STATUS);
        statusFilter.setOperator(Operator.EQ);
        statusFilter.setFieldValues(Lists.newArrayList(CommonFields.LIFE_STATUS__INVALID));

        Filter delFilter = new Filter();
        delFilter.setFieldName(CommonFields.IS_DELETED);
        delFilter.setOperator(Operator.EQ);
        delFilter.setFieldValues(Lists.newArrayList("true"));
        stq.setFilters(Lists.newArrayList(statusFilter, delFilter));

        List<String> fields = Lists.newArrayList("_id", "object_describe_api_name", "tenant_id");
        return QueryDataUtil.find(serviceFacade, tenantId, apiName, stq, fields);
    }

    private void deleteObjectData(String tenantId, List<IObjectData> data) {
        List<List<IObjectData>> partitions = Lists.partition(data, 50);
        User sys = User.systemUser(tenantId);
        for (List<IObjectData> partition : partitions) {
            serviceFacade.bulkDelete(partition, sys);
        }
    }
}