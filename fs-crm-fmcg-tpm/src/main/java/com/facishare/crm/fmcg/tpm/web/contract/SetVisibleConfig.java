package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/10 15:57
 */
public interface SetVisibleConfig {

    @Data
    @ToString
    class Arg {

        @JSONField(name = "object_api_name_list")
        @JsonProperty(value = "object_api_name_list")
        @SerializedName("object_api_name_list")
        private List<String> objectApiNameList;

        private Boolean visible;
    }

    @Data
    @ToString
    class Result implements Serializable {

    }
}
