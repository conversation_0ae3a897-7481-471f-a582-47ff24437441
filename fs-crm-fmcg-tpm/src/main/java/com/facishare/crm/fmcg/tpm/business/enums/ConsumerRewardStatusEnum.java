package com.facishare.crm.fmcg.tpm.business.enums;

/**
 * Author: linmj
 * Date: 2024/3/27 16:48
 */
public enum ConsumerRewardStatusEnum {
    SUCCESS("0", "操作成功"),
    HAS_GOT_BY_MYSELF("1", "已领"),
    DAILY_OVERLOAD("2", "超过每日个人领取限制"),
    NO_ACTIVITY("3", "活动不存在"),
    HAS_GOT_BY_OTHER("4", "其他人已领"),
    ACCUMULATED_OVERLOAD("5", "超过累计领取限制"),;

    private String code;

    private String description;

    ConsumerRewardStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String code() {
        return code;
    }
}
