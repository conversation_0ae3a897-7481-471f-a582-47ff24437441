package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.crm.fmcg.tpm.business.dto.CarryForwardDetailDataDocument;
import com.facishare.crm.fmcg.tpm.business.enums.CarryForwardType;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetCarryForwardObjLoadDetailDataController;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Set;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/9/15 14:20
 */
public interface IBudgetCarryForwardService {

    List<CarryForwardDetailDataDocument> loadDetailData(BudgetTypePO type, BudgetTypeNodeEntity node, CarryForwardType carryForwardType, long sourcePeriod, long targetPeriod);
    TPMBudgetCarryForwardObjLoadDetailDataController.ConfirmInfo confirmInfo(BudgetTypePO type, BudgetTypeNodeEntity node, CarryForwardType carryForwardType, long sourcePeriod, long targetPeriod);

    String initDimensionKey(Set<String> dimensionFields, IObjectData data);
}
