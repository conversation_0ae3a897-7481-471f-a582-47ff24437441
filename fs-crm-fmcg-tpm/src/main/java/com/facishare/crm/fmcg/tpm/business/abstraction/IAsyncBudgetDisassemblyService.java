package com.facishare.crm.fmcg.tpm.business.abstraction;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;

public interface IAsyncBudgetDisassemblyService {

    void unFrozen(User user, String dataId);

    void doDisassembly(User user, String dataId, String actionCode);

    void markMasterDisassemblyInfo(String tenantId, IObjectData objectData, String status);
}