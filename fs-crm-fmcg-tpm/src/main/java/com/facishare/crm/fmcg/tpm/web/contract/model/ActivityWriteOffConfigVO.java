package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/6 17:59
 */
@Data
@ToString
public class ActivityWriteOffConfigVO implements Serializable {

    @JSONField(name = "write_off_source_config")
    @JsonProperty(value = "write_off_source_config")
    @SerializedName("write_off_source_config")
    private ActivityWriteOffSourceConfigVO writeOffSourceConfig;

    @JSONField(name = "charge_up_config")
    @JsonProperty(value = "charge_up_config")
    @SerializedName("charge_up_config")
    private ActivityWriteOffChargeUpConfigVO chargeUpConfig;

    @JSONField(name = "enable_over_write_off")
    @JsonProperty(value = "enable_over_write_off")
    @SerializedName("enable_over_write_off")
    private Boolean enableOverWriteOff;

    public static ActivityWriteOffConfigVO fromPO(ActivityWriteOffConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityWriteOffConfigVO vo = new ActivityWriteOffConfigVO();
        vo.setWriteOffSourceConfig(ActivityWriteOffSourceConfigVO.fromPO(po.getWriteOffSourceConfig()));
        vo.setChargeUpConfig(ActivityWriteOffChargeUpConfigVO.fromPO(po.getChargeUpConfig()));
        vo.setEnableOverWriteOff(po.getEnableOverWriteOff());
        return vo;
    }
}
