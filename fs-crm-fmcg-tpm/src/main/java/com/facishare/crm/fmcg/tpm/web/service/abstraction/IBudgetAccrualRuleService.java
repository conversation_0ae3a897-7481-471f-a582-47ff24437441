package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.*;

/**
 * <AUTHOR>
 * @date 2022/10/9 下午2:33
 */
public interface IBudgetAccrualRuleService {

    AddBudgetAccrualRule.Result add(AddBudgetAccrualRule.Arg arg);

    GetBudgetAccrualRule.Result get(GetBudgetAccrualRule.Arg arg);

    ListBudgetAccrualRule.Result list(ListBudgetAccrualRule.Arg arg);

    EditBudgetAccrualRule.Result edit(EditBudgetAccrualRule.Arg arg);

    SetBudgetAccrualRuleStatus.Result setStatus(SetBudgetAccrualRuleStatus.Arg arg);

    DeleteBudgetAccrualRule.Result delete(DeleteBudgetAccrualRule.Arg arg);

    ListBusinessObject.Result listBusinessObject(ListBusinessObject.Arg arg);

    GetAccrualObject.Result getAccrualObjects(GetAccrualObject.Arg arg);
}
