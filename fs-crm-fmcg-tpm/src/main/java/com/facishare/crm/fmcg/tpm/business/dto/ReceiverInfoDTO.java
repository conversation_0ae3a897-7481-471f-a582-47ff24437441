package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * Author: linmj
 * Date: 2023/10/11 19:46
 */
@Data
@ToString
public class ReceiverInfoDTO {

    private String tenantId;

    private String rewardRole;

    private String rewardRoleCode;

    private String storeBossTag;

    private String rewardPerson;

    private String tenantName;

    private String type;

    private String name;

    private String idCard;

    private String phone;

    private String wxAppId;

    private String wxOpenId;

    private String unionId;

    private String rewardPersonId;

    private IObjectData store;

    private BigDecimal amount;

    private String rewardMethod;

    private String distributionType;

    private Long expiredTime;

    private String rewardMethodType;

    private String randomPacketLevel;

    private Boolean randomTopLevel;

    private String rebateUseType;

    private String remarks;
}
