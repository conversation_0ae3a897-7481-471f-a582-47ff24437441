package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.tpm.api.activity.YinLuAgreementEnableFilter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.PreDefineController;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IObjectDataService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.service.impl.ObjectDataServiceImpl;
import com.facishare.paas.metadata.util.ActionContextUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.common.SqlEscaper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 银鹭客开活动方案过滤接口
 */
//IgnoreI18nFile
public class TPMActivityObjYinLuAgreementEnableFilterController extends PreDefineController<YinLuAgreementEnableFilter.Arg, YinLuAgreementEnableFilter.Result> {

    private static final IObjectDataService objectDataService = SpringUtil.getContext().getBean(ObjectDataServiceImpl.class);

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected void before(YinLuAgreementEnableFilter.Arg arg) {
        super.before(arg);
    }

    @Override
    protected YinLuAgreementEnableFilter.Result doService(YinLuAgreementEnableFilter.Arg arg) {
        User su = User.systemUser(controllerContext.getTenantId());

        if (Strings.isNullOrEmpty(arg.getAccountId())) {
            IObjectData employee = serviceFacade.findObjectDataIgnoreAll(su, arg.getUserId(), ApiNames.PERSONNEL_OBJ);

            stopWatch.lap("findEmployee");
            if (Objects.isNull(employee)) {
                return YinLuAgreementEnableFilter.Result.error("员工信息不存在。");
            }

            List<String> dataOwnDepartment = employee.getDataOwnDepartment();
            if (CollectionUtils.isEmpty(dataOwnDepartment)) {
                return YinLuAgreementEnableFilter.Result.error("员工信息的归属部门为空，请联系管理员调整。");
            }

            IObjectData department = serviceFacade.findObjectDataIgnoreAll(su, dataOwnDepartment.get(0), ApiNames.DEPARTMENT_OBJ);
            stopWatch.lap("findDepartment");
            if (Objects.isNull(department)) {
                return YinLuAgreementEnableFilter.Result.error("归属部门信息不存在，请联系管理员调整。");
            }

            Object parentId = department.get("parent_id");
            if (Objects.isNull(parentId)) {
                return YinLuAgreementEnableFilter.Result.error("归属部门的父部门为空，请联系管理员调整。");
            }

            List<String> parentDepartmentIds = CommonUtils.cast(parentId, String.class);
            if (CollectionUtils.isEmpty(parentDepartmentIds)) {
                return YinLuAgreementEnableFilter.Result.error("归属部门的父部门为空，请联系管理员调整。");
            }

            return YinLuAgreementEnableFilter.Result.outRoute(parentDepartmentIds);
        }

        String areaId = findAreaId();
        stopWatch.lap("findAreaId");

        if (Objects.isNull(areaId)) {
            return YinLuAgreementEnableFilter.Result.error("当前员工未分配路线，无法签订协议。");
        }

        IObjectData store = serviceFacade.findObjectDataIgnoreAll(User.systemUser(controllerContext.getTenantId()), arg.getAccountId(), ApiNames.ACCOUNT_OBJ);
        if (Objects.isNull(store)) {
            return YinLuAgreementEnableFilter.Result.error("门店信息不存在。");
        }

        String channelType = store.get("channel__c", String.class);
        String storeLevel = store.get("storelevel__c", String.class);
        List<String> storeOwnDepartment = store.getDataOwnDepartment();
        List<String> supplyIds = querySupplyIds(store.getId());
        stopWatch.lap("findStore");

        List<String> planIds = Lists.newArrayList();
        try {
            String sql = buildupSql(controllerContext.getTenantId(), areaId, storeLevel, channelType, storeOwnDepartment.get(0), supplyIds);
            List<Map> maps = objectDataService.findBySql(controllerContext.getTenantId(), sql);
            for (Map<?, ?> map : maps) {
                planIds.add((String) map.get("id"));
            }
        } catch (MetadataServiceException ex) {
            log.error("custom find by sql error : ", ex);
            return YinLuAgreementEnableFilter.Result.error("查询信息时发生未知异常，请稍后重试。");
        }
        stopWatch.lap("findData");

        return YinLuAgreementEnableFilter.Result.success(planIds);
    }


    public String buildupSql(String tenantId, String areaId, String storeLevel, String channelType, String departmentId, List<String> supplyIds) {
        StringBuilder sql = new StringBuilder();

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.AM_PM, Calendar.AM);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        long time = calendar.getTimeInMillis();

        sql.append("select\n");
        sql.append("    tsp.id\n");
        sql.append("from\n");
        sql.append("    tpm_activity_time_span_plan__c tsp\n");
        sql.append("where\n");
        sql.append("    ").append(SqlEscaper.pg_op_eq("tsp.tenant_id", tenantId)).append("\n");
        sql.append("    and tsp.is_deleted = 0\n");
        sql.append("    and tsp.value20 = 'normal'\n");
        sql.append("    and tsp.value25 in (\n");
        sql.append("        select\n");
        sql.append("            distinct a.id\n");
        sql.append("        from\n");
        sql.append("            tpm_activity_plan__c a\n");
        sql.append("            inner join tpm_area_budget__c g on a.tenant_id = g.tenant_id\n");
        sql.append("            and a.id = g.value4\n");
        sql.append("            and g.is_deleted = 0\n");
        sql.append("            and g.value5 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("g.value14", areaId)).append("\n");
        sql.append("            inner join tpm_activity_plan_store__c b on a.tenant_id = b.tenant_id\n");
        sql.append("            and a.id = b.value4\n");
        sql.append("            and b.is_deleted = 0\n");
        sql.append("            and b.value6 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("b.value3", storeLevel)).append("\n");
        sql.append("            inner join tpm_activity_plan_channel__c c on a.tenant_id = c.tenant_id\n");
        sql.append("            and a.id = c.value4\n");
        sql.append("            and c.is_deleted = 0\n");
        sql.append("            and c.value6 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("c.value3", channelType)).append("\n");
        sql.append("            inner join tpm_business_center_budget__c d on a.tenant_id = d.tenant_id\n");
        sql.append("            and a.id = d.value3\n");
        sql.append("            and d.is_deleted = 0\n");
        sql.append("            and d.value4 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("d.value13", departmentId)).append("\n");

        if (!org.springframework.util.CollectionUtils.isEmpty(supplyIds)) {
            sql.append("            inner join tpm_activity_plan_dealer__c f on a.tenant_id = f.tenant_id\n");
            sql.append("            and a.id = f.value4\n");
            sql.append("            and f.is_deleted = 0\n");
            sql.append("            and f.value5 = 'normal'\n");
            sql.append("            and ").append(SqlEscaper.pg_op_in("f.value2", supplyIds)).append("\n");
        }

        sql.append("            inner join tpm_activity_plan_time_span__c e on a.tenant_id = e.tenant_id\n");
        sql.append("            and a.id = e.value3\n");
        sql.append("            and e.is_deleted = 0\n");
        sql.append("            and e.value4 = 'normal'\n");
        sql.append("            and e.value14 <= ").append(time).append("\n");
        sql.append("            and e.value13 >= ").append(time).append("\n");
        sql.append("        where\n");
        sql.append("            ").append(SqlEscaper.pg_op_eq("a.tenant_id", tenantId)).append("\n");
        sql.append("            and a.is_deleted = 0\n");
        sql.append("            and a.value26 = 'normal'\n");
        sql.append("    )\n");
        sql.append("    and exists (\n");
        sql.append("        select\n");
        sql.append("            h.id\n");
        sql.append("        from\n");
        sql.append("            tpm_activity_time_span_plan_channel__c h\n");
        sql.append("            inner join tpm_activity_plan_channel__c j on h.tenant_id = j.tenant_id\n");
        sql.append("            and j.id = h.value2\n");
        sql.append("            and j.is_deleted = 0\n");
        sql.append("            and j.value6 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("j.value3", channelType)).append("\n");
        sql.append("        where\n");
        sql.append("            h.tenant_id = tsp.tenant_id\n");
        sql.append("            and h.is_deleted = 0\n");
        sql.append("            and h.value4 = 'normal'\n");
        sql.append("            and h.value5 = tsp.id\n");
        sql.append("    )\n");
        sql.append("    and exists (\n");
        sql.append("        select\n");
        sql.append("            k.id\n");
        sql.append("        from\n");
        sql.append("            tpm_activity_time_span_plan_store__c k\n");
        sql.append("            inner join tpm_activity_plan_store__c j on k.tenant_id = j.tenant_id\n");
        sql.append("            and j.id = k.value7\n");
        sql.append("            and j.is_deleted = 0\n");
        sql.append("            and j.value6 = 'normal'\n");
        sql.append("            and ").append(SqlEscaper.pg_op_eq("j.value3", storeLevel)).append("\n");
        sql.append("        where\n");
        sql.append("            k.tenant_id = tsp.tenant_id\n");
        sql.append("            and k.is_deleted = 0\n");
        sql.append("            and k.value3 = 'normal'\n");
        sql.append("            and k.value4 = tsp.id\n");
        sql.append("    );\n");

        log.info("custom sql : {}", sql);

        return sql.toString();
    }


    private String findAreaId() {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setLimit(1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter ownerFilter = new Filter();
        ownerFilter.setFieldName("area_owner__c");
        ownerFilter.setOperator(Operator.EQ);
        ownerFilter.setFieldValues(Lists.newArrayList(arg.getUserId()));

        query.setFilters(Lists.newArrayList(ownerFilter));

        IActionContext context = ActionContextUtil.getNewContext(controllerContext.getTenantId());
        context.setPrivilegeCheck(false);
        context.setUserId("-10000");

        QueryResult<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(context, "AreaManageObj", query, Lists.newArrayList("_id"));
        if (CollectionUtils.isEmpty(result.getData())) {
            return null;
        }

        return result.getData().get(0).getId();
    }

    private List<String> querySupplyIds(String storeId) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter ownerFilter = new Filter();
        ownerFilter.setFieldName("store");
        ownerFilter.setOperator(Operator.EQ);
        ownerFilter.setFieldValues(Lists.newArrayList(storeId));

        query.setFilters(Lists.newArrayList(ownerFilter));

        IActionContext context = ActionContextUtil.getNewContext(controllerContext.getTenantId());
        context.setPrivilegeCheck(false);
        context.setUserId("-10000");

        QueryResult<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(context, "SupplyStoreObj", query, Lists.newArrayList("supply_id"));
        List<String> supplyIds = result.getData().stream().map(m -> m.get("supply_id", String.class)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(supplyIds)) {
            supplyIds.addAll(queryUpperSupplyIds(supplyIds));
        }
        return supplyIds;
    }

    private Collection<String> queryUpperSupplyIds(List<String> supplyIds) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setNeedReturnCountNum(false);
        query.setLimit(200);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter storeFilter = new Filter();
        storeFilter.setFieldName("store");
        storeFilter.setOperator(Operator.IN);
        storeFilter.setFieldValues(Lists.newArrayList(supplyIds));

        query.setFilters(Lists.newArrayList(storeFilter));

        IActionContext context = ActionContextUtil.getNewContext(controllerContext.getTenantId());
        context.setPrivilegeCheck(false);
        context.setUserId("-10000");

        QueryResult<IObjectData> result = serviceFacade.findBySearchTemplateQueryWithFields(context, "DistributorSupplyObj", query, Lists.newArrayList("up_dealer"));
        return result.getData().stream().map(m -> m.get("up_dealer", String.class)).collect(Collectors.toList());
    }

    @Override
    protected YinLuAgreementEnableFilter.Result after(YinLuAgreementEnableFilter.Arg arg, YinLuAgreementEnableFilter.Result result) {
        stopWatch.logSlow(300);
        return super.after(arg, result);
    }
}
