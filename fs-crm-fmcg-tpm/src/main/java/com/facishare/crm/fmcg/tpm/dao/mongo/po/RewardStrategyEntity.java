package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;
import org.mongodb.morphia.annotations.Transient;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/14 16:05
 */

@Data
@ToString
public class RewardStrategyEntity implements Serializable {

    public static final String F_REWARD_METHOD = "reward_method";

    public static final String F_REWARD_QUANTITY = "reward_quantity";

    public static final String F_REWARD_REMARK = "reward_remark";

    public static final String F_REWARD_METHOD_TYPE = "reward_method_type";

    public static final String F_RANDOM_REWARD_LEVELS = "random_reward_levels";

    public static final String F_DISTRIBUTE_METHOD = "distribute_method";

    public static final String F_EXPIRED_DAYS = "expired_days";

    public static final String F_DAILY_REWARD_LIMIT = "daily_reward_limit";

    public static final String F_REWARD_COUNT = "reward_count";

    public static final String F_INDIVIDUAL_REWARD_LIMIT = "individual_reward_limit";

    public static final String F_REWARD_GET_METHOD = "reward_get_method";

    public static final String F_EXCEPTION_STRATEGY = "exception_strategy";

    public static final String F_EXCEPTION_ACTION_ID = "exception_action_id";

    public static final String F_EXCEPTION_TYPES = "exception_types";

    public static final String F_REBATE_USE_TYPE = "rebate_use_type";

    public static final String F_RANDOM_REWARD_MODE = "random_reward_mode";



    @Property(F_REWARD_METHOD)
    private String rewardMethod;

    @Property(F_REWARD_METHOD_TYPE)
    private String rewardMethodType;

    @Property(F_RANDOM_REWARD_MODE)
    private String randomRewardMode;

    @Embedded(F_RANDOM_REWARD_LEVELS)
    private List<RandomRewardLevelEntity> randomRewardLevels;

    @Property(F_REWARD_QUANTITY)
    private String rewardQuantity;

    @Property(F_REWARD_COUNT)
    private Integer rewardCount;

    @Property(F_DISTRIBUTE_METHOD)
    private String distributeMethod;

    @Property(F_EXPIRED_DAYS)
    private Integer expiredDays;

    @Property(F_REWARD_REMARK)
    private String rewardRemark;

    @Property(F_DAILY_REWARD_LIMIT)
    private Integer dailyRewardLimit;

    @Property(F_INDIVIDUAL_REWARD_LIMIT)
    private Integer individualRewardLimit;

    @Property(F_REWARD_GET_METHOD)
    private List<String> rewardGetMethod;

    @Property(F_EXCEPTION_STRATEGY)
    private String exceptionStrategy;

    @Property(F_EXCEPTION_ACTION_ID)
    private String exceptionActionId;

    @Property(F_EXCEPTION_TYPES)
    private List<String> exceptionTypes;

    @Property(F_REBATE_USE_TYPE)
    private String rebateUseType;


    @Transient
    private boolean validate = true;

    @Transient
    private boolean useRandomTopLevel;

    @Override
    public boolean equals(Object o) {
        if (o instanceof RewardStrategyEntity) {
            RewardStrategyEntity strategyEntity = (RewardStrategyEntity) o;
            BigDecimal selfQuantity = strategyEntity.getRewardQuantity() == null ? null : new BigDecimal(strategyEntity.getRewardQuantity()).setScale(2, RoundingMode.DOWN);
            BigDecimal otherQuantity = this.rewardQuantity == null ? null : new BigDecimal(this.rewardQuantity).setScale(2, RoundingMode.DOWN);
            return !strategyEntity.validate || (equals(selfQuantity, otherQuantity) && equals(expiredDays, strategyEntity.expiredDays) && equals(strategyEntity.rewardRemark, rewardRemark) && equals(strategyEntity.getRandomRewardLevels(), this.randomRewardLevels)
                    && equals(strategyEntity.getRewardMethod(), this.rewardMethod) && equals(strategyEntity.getRewardRemark(), this.rewardRemark)
                    && equals(strategyEntity.getRewardMethodType(), this.rewardMethodType)
                    && equals(strategyEntity.getDistributeMethod(), this.distributeMethod)
                    && equals(strategyEntity.getDailyRewardLimit(), this.dailyRewardLimit)
                    && equals(strategyEntity.getIndividualRewardLimit(), this.individualRewardLimit)
                    && equals(strategyEntity.getRewardCount(), this.rewardCount)
                    && equals(strategyEntity.getExceptionStrategy(), this.exceptionStrategy)
                    && equals(strategyEntity.getExceptionActionId(), this.exceptionActionId)
                    && equals(strategyEntity.getExceptionTypes(), this.exceptionTypes)
                    && equals(strategyEntity.getRewardGetMethod(), this.rewardGetMethod)
                    && equals(strategyEntity.getRebateUseType(), this.rebateUseType)
                    && equals(strategyEntity.getRandomRewardMode(), this.randomRewardMode)
            );
        }
        return false;
    }

    private boolean equals(Object o1, Object o2) {
        if (o1 == null || o2 == null) {
            return o1 == o2;
        } else {
            if (o1 instanceof BigDecimal) {
                return ((BigDecimal) o1).compareTo((BigDecimal) o2) == 0;
            }
            if (o1 instanceof List) {
                return (CollectionUtils.isEmpty((Collection<?>) o1) && CollectionUtils.isEmpty((Collection<?>) o2)) || o1.equals(o2);
            }
            return o1.equals(o2);
        }
    }

}
