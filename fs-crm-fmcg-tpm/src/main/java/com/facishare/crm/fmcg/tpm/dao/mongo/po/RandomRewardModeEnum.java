package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 随机奖励模式枚举
 * Author: linmj
 * Date: 2024/6/20
 */
public enum RandomRewardModeEnum {

    COUNT("count", "计数", "fmcg.reward_rule.random_mode.count"),
    PROBABILITY("probability", "概率", "fmcg.reward_rule.random_mode.probability");

    private static final Map<String, RandomRewardModeEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(RandomRewardModeEnum::code, mode -> mode));

    private final String code;
    private final String desc;
    private final String i18nKey;

    RandomRewardModeEnum(String code, String desc, String i18nKey) {
        this.code = code;
        this.desc = desc;
        this.i18nKey = i18nKey;
    }

    public String code() {
        return code;
    }

    public String desc() {
        return desc;
    }

    public String i18nDesc() {
        String text;
        if (this.i18nKey == null) {
            text = this.desc;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.desc;
            }
        }
        return text;
    }

    public static RandomRewardModeEnum get(String code) {
        return CODE_MAP.get(code);
    }
}