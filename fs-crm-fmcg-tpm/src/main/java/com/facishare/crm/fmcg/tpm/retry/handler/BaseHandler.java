package com.facishare.crm.fmcg.tpm.retry.handler;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.RetryTaskPO;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/8/10 11:51
 */
@Slf4j
public abstract class BaseHandler implements RetryHandler {

    @Resource
    protected ServiceFacade serviceFacade;

    @Override
    public final void handle(RetryTaskPO retryTaskPO) {
        try {
            mainDo(retryTaskPO);
        } catch (Exception e) {
            handleException(retryTaskPO, e);
        } finally {
            finallyHandle(retryTaskPO);
        }
    }

    @Override
    public void mainDo(RetryTaskPO retryTaskPO) {
        log.info("deal task,taskId:{}", retryTaskPO.getId().toString());
    }

    @Override
    public void finallyHandle(RetryTaskPO retryTaskPO) {
    }

    @Override
    public void handleException(RetryTaskPO retryTaskPO, Exception e) {
        log.info("retry task:{} error", retryTaskPO, e);
    }
}
