package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Author: linmj
 * Date: 2023/9/14 17:39
 */
public enum RewardTargetEnum {

    SCAN_EXECUTOR("scan_executor", "扫码执行人","fmcg.reward_rule.target.scan_executor"),
    STORE_OWNER("store_owner", "门店负责人","fmcg.reward_rule.target.store_owner"),
    ENTERPRISE_BOSS("enterprise_boss", "企业老板","fmcg.reward_rule.target.enterprise_boss"),
    STORE_BOSS("store_boss", "门店老板","fmcg.reward_rule.target.store_boss"),;

    RewardTargetEnum(String code, String describe,String i18nKey) {
        this.code = code;
        this.describe = describe;
        this.i18nKey = i18nKey;
    }

    private static final Map<String, RewardTargetEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(RewardTargetEnum::code, rewardTypeEnum -> rewardTypeEnum));
    private String code;

    private String describe;

    private String i18nKey;

    public String code() {
        return this.code;
    }

    public String describe() {
        return this.describe;
    }

    public String i18nDescribe() {
        String text;
        if (this.i18nKey == null) {
            text = this.describe;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.describe;
            }
        }
        return text;
    }

    public static RewardTargetEnum get(String code) {
        return CODE_MAP.get(code);
    }
}
