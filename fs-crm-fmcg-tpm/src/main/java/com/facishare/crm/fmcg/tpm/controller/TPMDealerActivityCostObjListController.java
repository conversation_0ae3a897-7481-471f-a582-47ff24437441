package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListController;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * author: wuyx
 * description:
 * createTime: 2022/8/10 18:26
 */
public class TPMDealerActivityCostObjListController extends StandardListController {

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);


    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        fillDealerActivityCost(result);
        realButton(arg, result);
        return super.after(arg, result);
    }

    private void fillDealerActivityCost(Result result) {

        for (ObjectDataDocument activity : result.getDataList()) {
            Long beginDate = (Long) activity.get(TPMDealerActivityCostFields.BEGIN_DATE);
            Long endDate = (Long) activity.get(TPMDealerActivityCostFields.END_DATE);

            if (beginDate <= TimeUtils.MIN_DATE) {
                activity.put(TPMDealerActivityCostFields.BEGIN_DATE, null);
            }
            if (endDate >= TimeUtils.MAX_DATE) {
                activity.put(TPMDealerActivityCostFields.END_DATE, null);
            }
        }
    }


    private void realButton(Arg arg, Result result) {
        if (arg.isIncludeButtonInfo()) {
            if (result.getButtonInfo().getButtonMap() != null) {
                Map<String, ActivityTypeExt> activityTypeExtMap = new HashMap<>();
                for (ObjectDataDocument cost : result.getDataList()) {
                    String activityType = (String) cost.get(TPMDealerActivityCostFields.ACTIVITY_TYPE);
                    String activityId = (String) cost.get(TPMDealerActivityCostFields.ACTIVITY_ID);
                    String lifeStatus = (String) cost.get(CommonFields.LIFE_STATUS);
                    ActivityTypeExt activityTypeExt = getActivityTypeExt(activityType, activityId, activityTypeExtMap);
                    if (activityTypeExt == null) {
                        log.info("activity type not found.type:{},activity:{}", activityType, activityId);
                    }
                    if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus) || activityTypeExt == null || (activityTypeExt.writeOffChargeUpConfig() != null &&
                            Boolean.FALSE.equals(activityTypeExt.writeOffChargeUpConfig().getChargeUpAccountStatus()))) {
                        if (result.getButtonInfo().getButtonMap().containsKey(cost.getId())) {
                            result.getButtonInfo().getButtonMap().get(cost.getId()).remove(ObjectAction.ENTER_ACCOUNT.getButtonApiName());
                        }
                    }
                }
            }
        }
    }

    private ActivityTypeExt getActivityTypeExt(String activityType, String activityId, Map<String, ActivityTypeExt> activityTypeExtMap) {
        ActivityTypeExt activityTypeExt = activityTypeExtMap.get(activityType);
        try{
            if (activityTypeExt == null) {
                activityTypeExt = activityTypeManager.findByActivityId(controllerContext.getTenantId(), activityId);
                activityTypeExtMap.put(activityType, activityTypeExt);
            }
        } catch (MetaDataBusinessException exception){
            log.info("activity type not found.type:{},activity:{}", activityType, activityId);
        }
        return activityTypeExt;
    }
}
