package com.facishare.crm.fmcg.tpm.web.manager.dto;

import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/9 15:41
 */
@Data
@ToString
@Builder
public class CustomConsumeItem implements Serializable {

    private IBudgetOperator operator;

    private BigDecimal amount;
}