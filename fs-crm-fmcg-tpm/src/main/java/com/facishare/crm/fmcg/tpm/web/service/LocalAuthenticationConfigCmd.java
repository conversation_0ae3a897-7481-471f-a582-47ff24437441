package com.facishare.crm.fmcg.tpm.web.service;

import com.github.autoconf.ConfigFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class LocalAuthenticationConfigCmd {

    private int effectiveDuration;
    private String secret;

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("fs-fmcg-framework-config", conf -> {
            effectiveDuration = conf.getInt("meng-niu-wechat-local-authentication-effective-duration", 1800);
            secret = conf.get("meng-niu-wechat-local-authentication-secret", "");
        });
    }

    public String loadSecret(String appId) {
        String appSecretKey = "meng-niu-wechat-local-authentication-secret-" + appId;
        String appSecret = ConfigFactory.getConfig("fs-fmcg-framework-config").get(appSecretKey);

        return StringUtils.isEmpty(appSecret) ? secret : appSecret;
    }

    public int loadEffectiveDuration() {
        return effectiveDuration;
    }
}