package com.facishare.crm.fmcg.tpm.business.dto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * Author: linmj
 * Date: 2024/3/4 16:42
 */

@Data
@ToString
public class ContinuesSkuFacingCountRuleDTO {

    private Integer focusSkuCount;

    private List<SkuFacingRatioDTO> skuFacingRatios;


    @Data
    @ToString
    public static class SkuFacingRatioDTO {

        private List<SimpleObjectDTO> skuRanges;

        private Double count;
    }

}
