package com.facishare.crm.fmcg.tpm.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.fmcg.framework.http.PaasReferenceProxy;
import com.fmcg.framework.http.contract.reference.*;
import com.github.jedis.support.MergeJedisCmd;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: wuyx
 * description:
 * createTime: 2022/6/29 11:56
 */
@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class PaasReferenceService {
    @Resource
    private PaasReferenceProxy paasReferenceProxy;

    @Resource
    private MergeJedisCmd redisCmd;

    private static final String TPM_ACTIVITY_TYPE_REFERENCE_CACHE_KEY = "FMCG_TPM:REFERENCE:%s_%s_%s";
    private static final long TPM_ACTIVITY_TYPE_REFERENCE_CACHE_EXPIRE_TIME = 24 * 60 * 60 * 1000L;

    public void createReference(String tenantId, PaasReferenceCreate.Arg arg) {
        log.info("init createReference arg ={} ", JSON.toJSONString(arg));

        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() -> {

            if (Objects.isNull(arg) || CollectionUtils.isEmpty(arg.getItems())) {
                return;
            }
            Map<String, PaasReferenceCommonArg> referenceCommonArgMap = arg.getItems().stream().collect(Collectors.toMap(PaasReferenceCommonArg::getSourceValue, o -> o, (oldValue, newValue) -> newValue));

            List<PaasReferenceCommonArg> referenceCommonArgs = Lists.newArrayList();

            for (Map.Entry<String, PaasReferenceCommonArg> entry : referenceCommonArgMap.entrySet()) {
                String sourceValue = entry.getKey();
                String sourceType = entry.getValue().getSourceType();

                if (redisCmd.get(getSourceValueKey(tenantId, sourceType, sourceValue)) != null) {
                    continue;
                }

                PaasReferenceQueryBySource.Result result = paasReferenceProxy.queryBySource(tenantId, sourceType, sourceValue);

                if (result.getCode() == 200 && CollectionUtils.isEmpty(result.getValues())) {
                    referenceCommonArgs.add(entry.getValue());
                } else if (result.getCode() == 200 && CollectionUtils.isNotEmpty(result.getValues())) {
                    setRedisCache(tenantId, sourceType, sourceValue);
                }
            }

            if (CollectionUtils.isNotEmpty(referenceCommonArgs)) {
                PaasReferenceCreate.Arg args = new PaasReferenceCreate.Arg();
                args.setItems(referenceCommonArgs);
                PaasReferenceResult createResult = paasReferenceProxy.create(tenantId, args);
                if (createResult.getCode() != 200) {
                    log.error("create  paasReference errror , message ={}", createResult.getMessage());
                    return;
                }

                for (PaasReferenceCommonArg commonArg : referenceCommonArgs) {
                    setRedisCache(tenantId, commonArg.getSourceType(), commonArg.getSourceValue());
                }
            }

        })).run();
    }

    public void createReferenceAll(String tenantId, PaasReferenceCreate.Arg arg) {
        log.info("init createReferenceAll arg ={} ", JSON.toJSONString(arg));

        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() -> {
            if (Objects.isNull(arg) || CollectionUtils.isEmpty(arg.getItems())) {
                return;
            }

            List<PaasReferenceCommonArg> referenceCommonArgs = arg.getItems();

            if (CollectionUtils.isNotEmpty(referenceCommonArgs)) {
                PaasReferenceCreate.Arg args = new PaasReferenceCreate.Arg();
                args.setItems(referenceCommonArgs);
                PaasReferenceResult createResult = paasReferenceProxy.create(tenantId, args);
                if (createResult.getCode() != 200) {
                    log.error("create  paasReference errror , message ={}", createResult.getMessage());
                    return;
                }

                for (PaasReferenceCommonArg commonArg : referenceCommonArgs) {
                    setRedisCache(tenantId, commonArg.getSourceType(), commonArg.getSourceValue());
                }
            }

        })).run();
    }

    public void deleteReference(String tenantId, List<PaasReferenceBatchDelete.BatchDeleteArg> arg) {
        log.info("init deleteReference arg ={} ", JSON.toJSONString(arg));
        if (CollectionUtils.isEmpty(arg)) {
            return;
        }

        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() -> {
            PaasReferenceBatchDelete.Arg delArg = new PaasReferenceBatchDelete.Arg();
            delArg.setItems(arg);
            PaasReferenceResult batchDelete = paasReferenceProxy.batchDelete(tenantId, delArg);
            if (batchDelete.getCode() != 200) {
                log.error("delete  paasReference errror , message ={}", batchDelete.getMessage());
                return;
            }

            for (PaasReferenceBatchDelete.BatchDeleteArg batchDeleteArg : arg) {
                delRedisCache(tenantId, batchDeleteArg.getSourceType(), batchDeleteArg.getSourceValue());
            }
        })).run();
    }


    public void deleteReferenceAll(String tenantId, List<PaasReferenceBatchDelete.BatchDeleteArg> arg) {
        log.info("init deleteReferenceAll arg ={} ", JSON.toJSONString(arg));
        if (CollectionUtils.isEmpty(arg)) {
            return;
        }

        ParallelUtils.createBackgroundTask().submit(MonitorTaskWrapper.wrap(() -> {
            PaasReferenceBatchDelete.Arg delArg = new PaasReferenceBatchDelete.Arg();
            delArg.setItems(arg);
            PaasReferenceResult batchDelete = paasReferenceProxy.batchDelete(tenantId, delArg);
            if (batchDelete.getCode() != 200) {
                log.error("delete  paasReference errror , message ={}", batchDelete.getMessage());
                return;
            }

            for (PaasReferenceBatchDelete.BatchDeleteArg batchDeleteArg : arg) {
                delRedisCache(tenantId, batchDeleteArg.getTargetType(), batchDeleteArg.getTargetValue());
            }
        })).run();
    }

    private String getSourceValueKey(String tenantId, String sourceType, String sourceValue) {
        return String.format(TPM_ACTIVITY_TYPE_REFERENCE_CACHE_KEY, tenantId, sourceType, sourceValue);
    }

    private void setRedisCache(String tenantId, String sourceType, String sourceValue) {
        String key = getSourceValueKey(tenantId, sourceType, sourceValue);
        redisCmd.set(key, sourceValue);
        redisCmd.pexpire(key, TPM_ACTIVITY_TYPE_REFERENCE_CACHE_EXPIRE_TIME);
    }

    private void delRedisCache(String tenantId, String sourceType, String sourceValue) {
        String key = getSourceValueKey(tenantId, sourceType, sourceValue);
        redisCmd.del(key, sourceValue);
    }

}
