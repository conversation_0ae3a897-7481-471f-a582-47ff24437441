package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.api.scan.*;
import com.facishare.paas.metadata.api.IObjectData;

public interface IPhysicalRewardService {

    ConsumerRewardList.Result consumerRewardList(ConsumerRewardList.Arg arg);

    GetPhysicalItemInfo.Result getPhysicalItemInfo(GetPhysicalItemInfo.Arg arg);

    QueryWriteOffQrCodeStatus.Result queryWriteOffQrCodeStatus(QueryWriteOffQrCodeStatus.Arg arg);

    PhysicalItemWriteOff.Result physicalItemWriteOff(PhysicalItemWriteOff.Arg arg);

    FillMailInfoForPhysicalItem.Result fillMailInfoForPhysicalItem(FillMailInfoForPhysicalItem.Arg arg);

    void returnValueForPhysicalItem(String tenantId, IObjectData record);


    PhysicalRewardDefault.Result getDefaultPhysicalReward(PhysicalRewardDefault.Arg arg);
}
