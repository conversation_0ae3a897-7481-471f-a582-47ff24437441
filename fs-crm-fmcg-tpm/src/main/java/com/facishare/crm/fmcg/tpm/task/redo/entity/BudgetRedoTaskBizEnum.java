package com.facishare.crm.fmcg.tpm.task.redo.entity;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2022/7/26 下午5:40
 */
public enum  BudgetRedoTaskBizEnum {

    TRANSFER("调整","transfer"),
    CONSUME("消费","consume");

    BudgetRedoTaskBizEnum(String label, String value){
        this.label = label;
        this.value = value;
    }

    private static Map<String, BudgetRedoTaskBizEnum> value2EnumMap = Stream.of(BudgetRedoTaskBizEnum.values()).collect(Collectors.toMap(BudgetRedoTaskBizEnum::value, v->v,(before, after)->before));
    private String value;
    private String label;

    public String value(){
        return this.value;
    }

    public static BudgetRedoTaskBizEnum of(String value){
        return value2EnumMap.get(value);
    }
}
