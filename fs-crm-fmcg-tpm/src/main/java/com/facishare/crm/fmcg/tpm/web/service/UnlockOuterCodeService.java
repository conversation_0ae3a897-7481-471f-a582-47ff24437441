package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.appserver.checkins.api.model.GetQrCode;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.converter.EIEAConverter;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.FMCGSerialNumberStatusFields;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.MengNiuManufacturerInformation;
import com.facishare.crm.fmcg.tpm.business.FmcgSerialNumberService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.restful.client.exception.FRestClientException;
import com.facishare.stone.sdk.StoneProxyApi;
import com.facishare.stone.sdk.request.StoneFileDownloadRequest;
import com.github.autoconf.ConfigFactory;
import com.github.jedis.support.MergeJedisCmd;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class UnlockOuterCodeService {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private StoneProxyApi stoneProxyApi;
    @Resource
    private ShopMMService shopMMService;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource(name = "redisCmd")
    private MergeJedisCmd redisCmd;
    @Resource
    private TenantHierarchyService tenantHierarchyService;

    @Resource
    private FmcgSerialNumberService fmcgSerialNumberService;

    public boolean isOuterCodeLocked(String tenantId, String snId) {
        if (Boolean.FALSE.equals(serviceFacade.isExistObjectByApiName(tenantId, "unlock_outer_code_record__c"))) {
            return true;
        }

        IFilter snIdFilter = new Filter();
        snIdFilter.setFieldName("serial_number_id__c");
        snIdFilter.setOperator(Operator.EQ);
        snIdFilter.setFieldValues(Lists.newArrayList(snId));

        SearchTemplateQuery query = QueryDataUtil.minimumQuery(snIdFilter);
        query.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, "unlock_outer_code_record__c", query, Lists.newArrayList("_id"));
        return CollectionUtils.isEmpty(data);
    }

    public String loadParametersFromQrCode(String id) {
        String key = initShortQrCodeKey(id);
        String value = redisCmd.get(key);
        if (Strings.isNullOrEmpty(value)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_UNLOCK_OUTER_CODE_SERVICE_0));
        }
        return value;
    }

    public String initQrCodeWithParameters(String tenantId, String code) {
        int intTenantId = Integer.parseInt(tenantId);
        String tenantAccount = eieaConverter.enterpriseIdToAccount(intTenantId);

        GetQrCode.Args arg = new GetQrCode.Args();
        arg.setTenantId(Integer.parseInt(tenantId));
        arg.setWxAppId(ConfigFactory.getConfig("gray-rel-fmcg").get("mengniu_outer_code_unlock_app_id", "wxf70ac4798732b66f"));
        arg.setPage(ConfigFactory.getConfig("gray-rel-fmcg").get("mengniu_outer_code_unlock_page", "pages/external_code_decode/index"));
        MengNiuManufacturerInformation manufacturerInformation = tenantHierarchyService.findManufacturerByEnvironment("default");
        if (Objects.equals(manufacturerInformation.getTenantId(), tenantId)) {
            arg.setWxVersion("formal");
        } else {
            arg.setWxVersion("trial");
        }
        arg.setPath(code);

        log.info("qr code arg : {}", JSON.toJSONString(arg));

        GetQrCode.Result result = shopMMService.getQrCode(arg);
        log.info("qr code result : {}", JSON.toJSONString(result));

        redisCmd.setex(initShortQrCodeKey(result.getId()), 900L, code);

        StoneFileDownloadRequest req = new StoneFileDownloadRequest();

        req.setEa(tenantAccount);
        req.setEmployeeId(-10000);
        req.setFileType("jpg");
        req.setCancelRemoteThumb(false);
        req.setPath(result.getNPath());
        req.setSecurityGroup("");
        req.setBusiness("FS-FMCG-TPM");

        try (InputStream stream = stoneProxyApi.downloadStream(req); ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[102400];
            int temp;
            while (-1 != (temp = stream.read(buffer))) {
                output.write(buffer, 0, temp);
            }

            log.info("finish download image : tenant account : {}, path : {}, size : {}.", tenantAccount, result.getNPath(), output.size());

            return Base64.getEncoder().encodeToString(output.toByteArray());
        } catch (IOException | FRestClientException ex) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_UNLOCK_OUTER_CODE_SERVICE_1));
        }
    }

    private String initShortQrCodeKey(String id) {
        return String.format("FMCG:MENG_NIU:UNLOCK_OUTER_CODE:PARAMETERS:%s", id);
    }

    public boolean isUnlockStatusNotExists(String tenantId, String snId) {
        String actionId = fmcgSerialNumberService.getActionIdByActionUniqueId(tenantId, "UNLOCK_OUTER_CODE_RECORD");

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(FMCGSerialNumberStatusFields.ACTION_ID, Operator.EQ, Lists.newArrayList(actionId)),
                SearchQueryUtil.filter(FMCGSerialNumberStatusFields.FMCG_SERIAL_NUMBER_ID, Operator.EQ, Lists.newArrayList(snId))
        ));
        return CollectionUtils.isEmpty(CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.FMCG_SERIAL_NUMBER_STATUS_OBJ, query, Lists.newArrayList(CommonFields.ID)));
    }
}
