package com.facishare.crm.fmcg.tpm.facade;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.tpm.api.consume.PreValidateConsumeRuleAmount;
import com.facishare.crm.fmcg.tpm.api.consume.Reconsume;
import com.facishare.crm.fmcg.tpm.api.plugin.DealApprovalAction;
import com.facishare.crm.fmcg.tpm.api.plugin.DealTriggerAction;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetConsumeV2Service;
import com.facishare.crm.fmcg.tpm.retry.setter.YqslDeductSetter;
import com.facishare.paas.appframework.core.annotation.ServiceMethod;
import com.facishare.paas.appframework.core.annotation.ServiceModule;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.domain.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/10 上午11:35
 */

@Service
@Slf4j
@ServiceModule("budget_consume_new_mode")
public class BudgetConsumePluginV2Service {
    private static final Set<String> APPROVAL_TRIGGER_TYPE = Sets.newHashSet("1", "2");

    @Resource
    private IBudgetConsumeV2Service budgetConsumeV2Service;


    @Resource
    private YqslDeductSetter yqslDeductSetter;

    @ServiceMethod("add_before")
    public AddActionDomainPlugin.Result before(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("Add")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerBefore(beforeArg);
        AddActionDomainPlugin.Result result = new AddActionDomainPlugin.Result();
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__UNCLOSED);
        updateMap.put(TPMActivityFields.CLOSE_TIME, null);
        result.setObjectDataToUpdate(ObjectDataDocument.of(updateMap));
        result.setCustomCallbackData(beforeArg.getCallbackMap());
        log.info("add_before end");
        return result;
    }

    @ServiceMethod("add_after")
    public AddActionDomainPlugin.Result after(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg afterArg = DealTriggerAction.Arg.builder()
                .actionCode("Add")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerAfter(afterArg);
        log.info("add_after end");
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("add_finally_do")
    public AddActionDomainPlugin.Result finallyDo(AddActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg finallyDoArg = DealTriggerAction.Arg.builder()
                .actionCode("Add")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionFinallyDo(finallyDoArg);
        log.info("add_finally_do end");
        return new AddActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_before")
    public FlowCompletedActionDomainPlugin.Result before(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        String random = UUID.randomUUID().toString();
        log.info("flow_completed_before start." + random);
        long now = System.currentTimeMillis();
        DealApprovalAction.Arg beforeArg = DealApprovalAction.Arg.builder()
                .approvalStatus(arg.getStatus())
                .approvalType(arg.getTriggerType())
                .callbackMap(arg.getCallbackData())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbData().toObjectData())
                .detailChangeMap(arg.getDetailChange())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealFlowCompletedBefore(beforeArg);
        log.info("flow_completed_before end. time:{}." + random, System.currentTimeMillis() - now);
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_after")
    public FlowCompletedActionDomainPlugin.Result after(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        String random = UUID.randomUUID().toString();
        log.info("flow_completed_after start." + random);
        DealApprovalAction.Arg afterArg = DealApprovalAction.Arg.builder()
                .approvalStatus(arg.getStatus())
                .approvalType(arg.getTriggerType())
                .callbackMap(arg.getCallbackData())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbData().toObjectData())
                .detailChangeMap(arg.getDetailChange())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealFlowCompletedAfter(afterArg);
        log.info("flow_completed_finally_do end." + random);
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @ServiceMethod("flow_completed_finally_do")
    public FlowCompletedActionDomainPlugin.Result finallyDo(FlowCompletedActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        String random = UUID.randomUUID().toString();
        log.info("flow_completed_finally_do start." + random);
        DealApprovalAction.Arg finallyDoArg = DealApprovalAction.Arg.builder()
                .approvalStatus(arg.getStatus())
                .approvalType(arg.getTriggerType())
                .callbackMap(arg.getCallbackData())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbData().toObjectData())
                .detailChangeMap(arg.getDetailChange())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealFlowCompletedFinallyDo(finallyDoArg);
        log.info("flow_completed_finally_do end." + random);
        return new FlowCompletedActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_before")
    public EditActionDomainPlugin.Result before(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg editArg = DealTriggerAction.Arg.builder()
                .actionCode("Edit")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbMasterData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .dbDetailMap(new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerBefore(editArg);
        EditActionDomainPlugin.Result result = new EditActionDomainPlugin.Result();
        result.setCustomCallbackData(editArg.getCallbackMap());
        log.info("edit_before end");
        return result;
    }

    @ServiceMethod("edit_after")
    public EditActionDomainPlugin.Result after(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg editArg = DealTriggerAction.Arg.builder()
                .actionCode("Edit")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbMasterData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .dbDetailMap(new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerAfter(editArg);
        log.info("edit_after end");
        return new EditActionDomainPlugin.Result();
    }

    @ServiceMethod("edit_finally_do")
    public EditActionDomainPlugin.Result finallyDo(EditActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("Edit")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .dbMasterData(arg.getDbMasterData().toObjectData())
                .detailMap(MapUtils.isNotEmpty(arg.getDetailObjectData()) ? transfer(arg.getDetailObjectData()) : new HashMap<>())
                .dbDetailMap(new HashMap<>())
                .isApprovalStartSuccess(arg.getIsApprovalFlowStartSuccess())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionFinallyDo(beforeArg);
        EditActionDomainPlugin.Result result = new EditActionDomainPlugin.Result();
        result.setCustomCallbackData(beforeArg.getCallbackMap());
        log.info("edit_finally_do end");
        return result;
    }

    @ServiceMethod("increment_update_before")
    public IncrementUpdateActionDomainPlugin.Result before(IncrementUpdateActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("IncrementUpdate")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(new HashMap<>())
                .useSnapshotForApproval(arg.isUseSnapshotForApproval())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerBefore(beforeArg);
        return new IncrementUpdateActionDomainPlugin.Result();
    }


    @ServiceMethod("invalid_before")
    public InvalidActionDomainPlugin.Result before(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("Invalid")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(new HashMap<>())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerBefore(beforeArg);
        return new InvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("invalid_after")
    public InvalidActionDomainPlugin.Result after(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("Invalid")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(new HashMap<>())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerAfter(beforeArg);
        return new InvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("invalid_finally_do")
    public InvalidActionDomainPlugin.Result finallyDo(InvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg beforeArg = DealTriggerAction.Arg.builder()
                .actionCode("Invalid")
                .callbackMap(new HashMap<>())
                .masterData(arg.getObjectData().toObjectData())
                .detailMap(new HashMap<>())
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionFinallyDo(beforeArg);
        return new InvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("bulk_invalid_before")
    public BulkInvalidActionDomainPlugin.Result before(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg before = DealTriggerAction.Arg.builder()
                .actionCode("BulkInvalid")
                .callbackMap(new HashMap<>())
                .objectList(arg.getObjectDataList().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()))
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerBefore(before);
        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("bulk_invalid_after")
    public BulkInvalidActionDomainPlugin.Result after(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg after = DealTriggerAction.Arg.builder()
                .actionCode("BulkInvalid")
                .callbackMap(new HashMap<>())
                .objectList(arg.getObjectDataList().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()))
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionTriggerAfter(after);
        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("bulk_invalid_finally_do")
    public BulkInvalidActionDomainPlugin.Result finallyDo(BulkInvalidActionDomainPlugin.Arg arg, ServiceContext serviceContext) {
        DealTriggerAction.Arg finallyDoArg = DealTriggerAction.Arg.builder()
                .actionCode("BulkInvalid")
                .callbackMap(new HashMap<>())
                .objectList(arg.getObjectDataList().stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()))
                .user(serviceContext.getUser())
                .build();
        budgetConsumeV2Service.dealActionFinallyDo(finallyDoArg);
        return new BulkInvalidActionDomainPlugin.Result();
    }

    @ServiceMethod("end_consume")
    public EndConsume.Result endConsume(EndConsume.Arg arg, ServiceContext serviceContext) {
        return budgetConsumeV2Service.endConsume(serviceContext.getUser(), arg.getDescribeApiName(), arg.getDataId(), arg.isNeedForceEnd());
    }


    @ServiceMethod("reconsume")
    public Reconsume.Result reconsume(Reconsume.Arg arg, ServiceContext serviceContext) {
        return budgetConsumeV2Service.reconsume(serviceContext.getUser(), arg.getDescribeApiName(), arg.getDataId(), arg.getAction());
    }

    @ServiceMethod("preValidateConsumeRuleAmount")
    public PreValidateConsumeRuleAmount.Result preValidateConsumeRuleAmount(PreValidateConsumeRuleAmount.Arg arg, ServiceContext serviceContext) {
        return budgetConsumeV2Service.preValidateConsumeRuleAmount(serviceContext.getUser(), arg);
    }

    @ServiceMethod("middleRelease")
    public JSONObject middleRelease(JSONObject arg, ServiceContext serviceContext) {
        budgetConsumeV2Service.middleRelease(serviceContext.getUser(), arg.getString("describeApiName"), arg.getString("dataId"), arg.getString("action"));
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result;
    }

    @ServiceMethod("asyncDeduct")
    public JSONObject asyncDeduct(JSONObject arg, ServiceContext serviceContext) {
        String describeApiName = arg.getString("describe_api_name");
        String dataId = arg.getString("data_id");
        String action = arg.getString("action");
        yqslDeductSetter.setUpdateStatusTask(serviceContext.getTenantId(), serviceContext.getUser().getUserId(), describeApiName, dataId, action, System.currentTimeMillis() + 1000);
        JSONObject result = new JSONObject();
        result.put("status", "to_be_executed");
        return result;
    }

    private Map<String, List<IObjectData>> transfer(Map<String, List<ObjectDataDocument>> sourceMap) {
        Map<String, List<IObjectData>> targetMap = new HashMap<>();
        if (MapUtils.isNotEmpty(sourceMap)) {
            sourceMap.forEach((key, list) -> {
                if (list != null) {
                    targetMap.put(key, list.stream().map(ObjectDataDocument::toObjectData).collect(Collectors.toList()));
                }
            });
        }
        return targetMap;
    }
}
