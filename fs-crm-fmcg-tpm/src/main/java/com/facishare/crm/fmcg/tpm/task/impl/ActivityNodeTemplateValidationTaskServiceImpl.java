package com.facishare.crm.fmcg.tpm.task.impl;

import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDBRouterService;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityNodeTemplatePO;
import com.facishare.crm.fmcg.tpm.service.abstraction.TPMEnterpriseService;
import com.facishare.crm.fmcg.tpm.session.SessionSendService;
import com.facishare.crm.fmcg.tpm.session.model.SessionContentSyncErrorActivity;
import com.facishare.crm.fmcg.tpm.task.ActivityNodeTemplateValidationTaskService;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityTask;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityNodeTemplateManager;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/5 19:34
 */
@Slf4j
@Component
public class ActivityNodeTemplateValidationTaskServiceImpl implements ActivityNodeTemplateValidationTaskService, InitializingBean {

    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;
    @Resource
    private SessionSendService sessionSendService;
    @Resource
    private IActivityNodeTemplateManager activityNodeTemplateManager;
    @Resource
    private TPM2Service tpm2Service;
    @Resource
    private TPMEnterpriseService tpmEnterpriseService;
    @Resource
    private IDBRouterService routerService;
    @Resource
    private RedissonClient redissonCmd;

    public Map<Integer, Boolean> isActiveEnterprise = Maps.newConcurrentMap();

    public static final int MAX = 10000;
    public static final int LIMIT = 100;

    private RRateLimiter validationTaskRateLimiter;

    @Override
    public void afterPropertiesSet() {
        validationTaskRateLimiter = redissonCmd.getRateLimiter("FMCG_TPM.ACTIVITY_NODE_VALIDATION.LIMITER");

        ConfigFactory.getConfig("fs-fmcg-tpm-config", iConfig -> {
            int rate = iConfig.getInt("fmcg_tpm.activity_node_validation.limiter_rate", 60);
            validationTaskRateLimiter.setRate(RateType.OVERALL, rate, 1, RateIntervalUnit.MINUTES);
        });
    }

    @Override
    public void activityNodeTemplateListValidation(ActivityTask.Arg arg) {
        int index = 0;
        int offset = 0;

        Map<Integer, Boolean> isActiveTenantCache = Maps.newConcurrentMap();
        Map<Integer, Boolean> existsRouteTenantCache = Maps.newConcurrentMap();
        Map<Integer, Boolean> existsLicenseTenantCache = Maps.newConcurrentMap();

        while (index < MAX) {
            List<ActivityNodeTemplatePO> activityNodes = activityNodeTemplateDAO.query(LIMIT, offset);
            for (ActivityNodeTemplatePO activityNode : activityNodes) {
                validationTaskRateLimiter.acquire();
                singleActivityNodeValidation(activityNode, isActiveTenantCache, existsRouteTenantCache, existsLicenseTenantCache);
            }

            if (activityNodes.size() < LIMIT) {
                break;
            } else {
                index++;
                offset += LIMIT;
            }
        }
    }

    private String validate(String tenantId, ActivityNodeTemplatePO datum) {
        String message;
        try {
            message = activityNodeTemplateManager.checkValidation(tenantId, datum);
        } catch (ValidateException ex) {
            message = ex.getMessage();
        } catch (Exception ex) {
            log.error("巡检活动节点异常 tenantId={},nodeTemplateId={}", tenantId, datum.getId(), ex);
            return null;
        }
        return message;
    }

    private void updateActivityNodeErrorInformation(ActivityNodeTemplatePO nodeTemplate, String tenantId) {
        Integer exceptionCount = nodeTemplate.getExceptionCount();
        nodeTemplate.setExceptionCount(exceptionCount == null ? 1 : exceptionCount + 1);
        activityNodeTemplateDAO.editExceptionStatus(tenantId, nodeTemplate.getId().toHexString(), nodeTemplate);
    }

    private void sendAdminNotify(String tenantId, ActivityNodeTemplatePO nodeTemplate, String errorMessage) {
        String nodeName = nodeTemplate.getName();
        String objectDisplayName = nodeTemplate.getObjectApiName();
        String referenceFieldApiName = nodeTemplate.getReferenceFieldApiName();

        SessionContentSyncErrorActivity sessionContentSyncErrorActivity = new SessionContentSyncErrorActivity(
                nodeName, objectDisplayName, referenceFieldApiName, errorMessage, false, null);
        sessionSendService.doSendSessionToSystemAdmins(tenantId, sessionContentSyncErrorActivity, 3);
    }

    private void singleActivityNodeValidation(ActivityNodeTemplatePO datum,
                                              Map<Integer, Boolean> isActiveTenantCache,
                                              Map<Integer, Boolean> existsRouteTenantCache,
                                              Map<Integer, Boolean> existsLicenseTenantCache) {
        int intTenantId = Integer.parseInt(datum.getTenantId());
        String tenantId = datum.getTenantId();

        if (!isActive(isActiveTenantCache, intTenantId)) {
            return;
        }

        if (!existsRoute(existsRouteTenantCache, intTenantId)) {
            return;
        }

        if (!existsLicense(existsLicenseTenantCache, intTenantId)) {
            return;
        }
        String message = validate(tenantId, datum);
        if (Strings.isNullOrEmpty(message)) {
            return;
        }

        updateActivityNodeErrorInformation(datum, tenantId);
        sendAdminNotify(tenantId, datum, message);
    }

    private boolean existsLicense(Map<Integer, Boolean> existsLicenseTenantCache, int tenantId) {
        boolean flag;
        if (existsLicenseTenantCache.containsKey(tenantId)) {
            flag = existsLicenseTenantCache.get(tenantId);
        } else {
            flag = tpm2Service.existTPMLicenseTenant(tenantId);
            existsLicenseTenantCache.put(tenantId, flag);
        }
        return flag;
    }

    private boolean existsRoute(Map<Integer, Boolean> existsRouteTenantCache, int tenantId) {
        boolean flag;
        if (existsRouteTenantCache.containsKey(tenantId)) {
            flag = existsRouteTenantCache.get(tenantId);
        } else {
            flag = routerService.existsPGDBRouter(String.valueOf(tenantId));
            existsRouteTenantCache.put(tenantId, flag);
        }
        return flag;
    }

    private boolean isActive(Map<Integer, Boolean> isActiveTenantCache, int tenantId) {
        boolean flag;
        if (isActiveTenantCache.containsKey(tenantId)) {
            flag = isActiveTenantCache.get(tenantId);
        } else {
            flag = tpmEnterpriseService.isActiveEnterprise(tenantId);
            isActiveTenantCache.put(tenantId, flag);
        }
        return flag;
    }
}
