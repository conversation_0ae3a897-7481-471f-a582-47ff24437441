package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTableNodeVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/26 下午6:36
 */
@Data
@ToString
public class BudgetTableNodeEntity implements Serializable {

    public static final String F_NODE_ID = "node_id";
    public static final String F_FIELD_RELATION = "field_relation";
    public static final String F_RATIO = "ratio";

    @Property(F_NODE_ID)
    private String nodeId;

    @Embedded(F_FIELD_RELATION)
    private List<BudgetFieldRelationEntity> fieldRelation;

    //比例
    @Property(F_RATIO)
    private String ratio;

    public static BudgetTableNodeEntity fromVO(BudgetTableNodeVO vo) {
        if (vo == null) {
            return null;
        }
        BudgetTableNodeEntity budgetTableNodeEntity = new BudgetTableNodeEntity();
        budgetTableNodeEntity.setNodeId(vo.getNodeId());
        if (CollectionUtils.isEmpty(vo.getFieldRelation())) {
            budgetTableNodeEntity.setFieldRelation(new ArrayList<>());
        } else {
            budgetTableNodeEntity.setFieldRelation(vo.getFieldRelation().stream().map(BudgetFieldRelationEntity::fromVO).collect(Collectors.toList()));
        }

        budgetTableNodeEntity.setRatio(vo.getRatio());

        return budgetTableNodeEntity;
    }

    public static BudgetTableNodeVO toVO(BudgetTableNodeEntity entity) {
        if (entity == null) {
            return null;
        }
        BudgetTableNodeVO budgetTableNodeVO = new BudgetTableNodeVO();
        budgetTableNodeVO.setNodeId(entity.getNodeId());
        if (CollectionUtils.isEmpty(entity.getFieldRelation())) {
            budgetTableNodeVO.setFieldRelation(new ArrayList<>());
        } else {
            budgetTableNodeVO.setFieldRelation(entity.getFieldRelation().stream().map(BudgetFieldRelationEntity::toVO).collect(Collectors.toList()));
        }

        budgetTableNodeVO.setRatio(entity.getRatio());

        return budgetTableNodeVO;


    }
}
