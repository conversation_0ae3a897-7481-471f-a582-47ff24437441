package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;

/**
 * author: wuyx
 * description:
 * createTime: 2022/7/14 15:22
 */
public class TPMBudgetTransferDetailObjInvalidAction extends StandardInvalidAction {

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        validateBudgetTransferStatus();
    }

    private void validateBudgetTransferStatus() {
        IObjectData data = this.dataList.get(0);
        String lifeStatus = data.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__INEFFECTIVE.equals(lifeStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_TRANSFER_DETAIL_OBJ_INVALID_ACTION_0));
        }
    }
}
