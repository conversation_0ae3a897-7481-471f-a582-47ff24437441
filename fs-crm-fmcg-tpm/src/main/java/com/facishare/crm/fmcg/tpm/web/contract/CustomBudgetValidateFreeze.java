package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.FreezeItemVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/8 16:17
 */
public interface CustomBudgetValidateFreeze {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private List<FreezeItemVO> data;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "business_trace_id")
        @JsonProperty(value = "business_trace_id")
        @SerializedName("business_trace_id")
        private String businessTraceId;

        @JSONField(name = "approval_trace_id")
        @JsonProperty(value = "approval_trace_id")
        @SerializedName("approval_trace_id")
        private String approvalTraceId;
    }
}
