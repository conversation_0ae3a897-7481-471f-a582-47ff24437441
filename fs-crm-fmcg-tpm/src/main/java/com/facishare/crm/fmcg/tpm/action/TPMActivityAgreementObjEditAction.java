package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.TPMAllowEditFieldsService;
import com.facishare.crm.fmcg.tpm.business.CrmAuditLogService;
import com.facishare.crm.fmcg.tpm.business.DescribeCacheService;
import com.facishare.crm.fmcg.tpm.business.StoreBusiness;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPM2Service;
import com.facishare.crm.fmcg.tpm.business.abstraction.ITPMDisplayReportService;
import com.facishare.crm.fmcg.tpm.business.dto.CrmAuditLogDTO;
import com.facishare.crm.fmcg.tpm.service.PackTransactionProxyImpl;
import com.facishare.crm.fmcg.tpm.service.abstraction.PackTransactionProxy;
import com.facishare.crm.fmcg.tpm.service.abstraction.TransactionService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.ITenantDevService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardEditAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fxiaoke.crmrestapi.common.contants.LifeStatusEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 3:46 PM
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class TPMActivityAgreementObjEditAction extends StandardEditAction implements TransactionService<StandardEditAction.Arg, StandardEditAction.Result> {

    private final ITPM2Service tpm2Service = SpringUtil.getContext().getBean(ITPM2Service.class);
    private final StoreBusiness storeBusiness = SpringUtil.getContext().getBean(StoreBusiness.class);
    private final CrmAuditLogService crmAuditLogService = SpringUtil.getContext().getBean(CrmAuditLogService.class);
    private final ITenantDevService tenantDevService = SpringUtil.getContext().getBean(ITenantDevService.class);

    private static DescribeCacheService describeCacheService = SpringUtil.getContext().getBean(DescribeCacheService.class);
    public final TPMAllowEditFieldsService tpmAllowEditFieldsService = SpringUtil.getContext().getBean(TPMAllowEditFieldsService.class);
    private final ITPMDisplayReportService tpmDisplayReportService = SpringUtil.getContext().getBean(ITPMDisplayReportService.class);
    private final PackTransactionProxy packTransactionProxy = SpringUtil.getContext().getBean(PackTransactionProxyImpl.class);


    private boolean enableEditCustomField = false;
    private boolean enableEditPreField = false;

    @Override
    protected void before(Arg arg) {
        //日志
        sendActivityObjAuditLog(arg);
        super.before(arg);

        log.info("action arg : {}", arg);
        validateEnableEditCustomField();
        if (!enableEditCustomField) {
            if (!enableEditPreField) {
                validateProof(arg);
            }
            validateEndDate(arg);
            validateActivity(actionContext, arg);
            validateAgreementStatus(arg);
            validateCashingProduct();
            validateDisplayReport(arg);
            log.info("after validate : {}", arg);
        }
    }

    private void validateDisplayReport(Arg arg) {
        try {
            tpmDisplayReportService.validateDisplayReport(arg);
        } catch (ValidateException ex) {
            throw ex;
        } catch (Exception e) {
            throw new ValidateException(e.getMessage() != null ? e.getMessage() : "System Validate Error.");
        }
    }

    private void validateEnableEditCustomField() {
        Map<String, List<String>> needUpdateFieldOBJFieldsMap = Maps.newHashMap();
        if (!this.updatedFieldMap.isEmpty()) {
            needUpdateFieldOBJFieldsMap.put(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, Lists.newArrayList(this.updatedFieldMap.keySet()));
        }
        if (!this.detailChangeMap.isEmpty()) {
            this.detailChangeMap.forEach((apiName, operateObjectMap) -> {
                if (operateObjectMap.containsKey("Edit")) {
                    Map<String, Object> editMap = (Map<String, Object>) operateObjectMap.get("Edit");
                    if (!editMap.isEmpty()) {
                        List<String> detailUpdateFields = Lists.newArrayList();
                        editMap.values().forEach(o -> {
                            Map<String, Object> detailFilesMap = (Map<String, Object>) (o);
                            detailUpdateFields.addAll(detailFilesMap.keySet());
                        });

                        if (CollectionUtils.isNotEmpty(detailUpdateFields)) {
                            needUpdateFieldOBJFieldsMap.put(apiName, detailUpdateFields.stream().distinct().collect(Collectors.toList()));
                        }
                    }
                }
            });
        }
        if (!needUpdateFieldOBJFieldsMap.isEmpty()) {
            log.info("needUpdateFieldOBJFieldsMap is {}", needUpdateFieldOBJFieldsMap);
            needUpdateFieldOBJFieldsMap.forEach((apiName, needUpdateFieldOBJFields) -> {
                List<String> preFields = needUpdateFieldOBJFields.stream().filter(s -> !s.contains("CALLBACK") && !s.contains("KEY") && !s.endsWith("__c")).collect(Collectors.toList());
                enableEditCustomField = preFields.isEmpty();
                List<String> enableEditPreFields = preFields.stream().filter(s -> !tpmAllowEditFieldsService.getTPMAllowedEditFields().contains(s)).collect(Collectors.toList());
                enableEditPreField = enableEditPreFields.isEmpty();
            });
        }
    }

    private void validateCashingProduct() {
        String actualTotalAmountStr = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTUAL_TOTAL_AMOUNT);
        if (StringUtils.isEmpty(actualTotalAmountStr)) {
            actualTotalAmountStr = "0";
        }
        BigDecimal actualTotalAmount = new BigDecimal(actualTotalAmountStr);
        List<ObjectDataDocument> cashingProductDetails = arg.getDetails().get(ApiNames.TPM_ACTIVITY_AGREEMENT_CASHING_PRODUCT_OBJ);
        String agreementCashingType = (String) arg.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE);
        if (Objects.equals(agreementCashingType, TPMActivityCashingProductFields.GOODS)) {
            // 860 协议兑付产品非必填
            if (CollectionUtils.isEmpty(cashingProductDetails)) {
                return;
            }
            BigDecimal totalPrice = new BigDecimal("0");
            for (ObjectDataDocument cashingProductDetail : cashingProductDetails) {
                BigDecimal price = new BigDecimal((String) cashingProductDetail.get(TPMActivityAgreementCashingProductFields.PRICE));
                BigDecimal quantity = new BigDecimal((String) cashingProductDetail.get(TPMActivityAgreementCashingProductFields.QUANTITY));
                totalPrice = totalPrice.add(price.multiply(quantity));
            }
            if (actualTotalAmount.compareTo(totalPrice) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_EDIT_ACTION_0));
            }
        }


    }

    private void sendActivityObjAuditLog(Arg arg) {
        crmAuditLogService.sendLog(CrmAuditLogDTO.builder()
                .tenantId(actionContext.getTenantId())
                .userId(String.valueOf(User.systemUser(actionContext.getTenantId())))
                .action("Edit")
                .objectApiNames(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ)
                .message("编辑活动协议")//ignorei18n
                .parameters(JSONObject.toJSONString(arg))
                .build());
    }

    @Override
    protected Result after(Arg arg, Result result) {
        String lifeStatus = (String) result.getObjectData().get(CommonFields.LIFE_STATUS);
        String agreementStatus = (String) result.getObjectData().get(TPMActivityAgreementFields.AGREEMENT_STATUS);
        Result finalResult = super.after(arg, result);

        if (!LifeStatusEnum.Normal.getValue().equals(lifeStatus) && TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {

            Map<String, Object> updater = new HashMap<>();
            updater.put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
            serviceFacade.updateWithMap(User.systemUser(actionContext.getTenantId()), result.getObjectData().toObjectData(), updater);

            result.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE);
        }

        if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(agreementStatus)) {
            storeBusiness.updateStoreLabel(actionContext.getTenantId(), Lists.newArrayList((String) finalResult.getObjectData().get(TPMActivityAgreementFields.STORE_ID)), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 0);
        } else if (TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS.equals(agreementStatus)) {
            storeBusiness.updateStoreLabel(actionContext.getTenantId(), Lists.newArrayList((String) finalResult.getObjectData().get(TPMActivityAgreementFields.STORE_ID)), AccountFields.CUSTOMER_LABEL_AGREEMENT_STORE, 1);
        }

        return finalResult;
    }

    private void validateProof(Arg arg) {
        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(10);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityProofFields.ACTIVITY_AGREEMENT_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(arg.getObjectData().getId()));

        query.setFilters(Lists.newArrayList(activityFilter));
        List<IObjectData> list = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_OBJ, query).getData();
        if (!list.isEmpty()) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_WHICH_HAS_RELATED_BY_PROOF_CAN_NOT_EDIT));
        }
    }

    private void validateAgreementStatus(Arg arg) {
        IObjectData old = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectData().getId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        String oldStatus = old.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(oldStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_AGREEMENT_OBJ_EDIT_ACTION_1));
        }
        if (TPMGrayUtils.isYinLu(actionContext.getTenantId())) {
            return;
        }
        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = (long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE);

        long now = System.currentTimeMillis();
        String status;
        if (now < begin) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__SCHEDULE;
        } else if (now < end) {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__IN_PROGRESS;
        } else {
            status = TPMActivityAgreementFields.AGREEMENT_STATUS__END;
        }
        arg.getObjectData().put(TPMActivityAgreementFields.AGREEMENT_STATUS, status);
    }

    private void validateActivity(ActionContext actionContext, Arg arg) {
        String activityId = (String) arg.getObjectData().get(TPMActivityAgreementFields.ACTIVITY_ID);

        if (TPMGrayUtils.agreementNotRelatedToActivity(actionContext.getTenantId()) && Strings.isNullOrEmpty(activityId)) {
            return;
        }

        IObjectData activity = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), activityId, ApiNames.TPM_ACTIVITY_OBJ);

        String closedStatus = (String) activity.get(TPMActivityFields.CLOSED_STATUS);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closedStatus)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_ENDED_CAN_NOT_CREATE_AGREEMENT));
        }

        boolean needAgreement = tpm2Service.isNeedAgreement(Integer.valueOf(actionContext.getTenantId()), activity);
        if (!needAgreement) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_WHICH_IS_NOT_AGREEMENT_ACTIVITY_CAN_NOT_CREATE_AGREEMENT));
        }

        long activityBegin = activity.get(TPMActivityFields.BEGIN_DATE, Long.class);
        long activityEnd = activity.get(TPMActivityFields.END_DATE, Long.class);
        long agreementBegin = arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.BEGIN_DATE, Long.class);
        long agreementEnd = arg.getObjectData().toObjectData().get(TPMActivityAgreementFields.END_DATE, Long.class);

        log.info("activity - begin time : {}, end time : {}, agreement - begin time : {}, end time : {}", activityBegin, activityEnd, agreementBegin, agreementEnd);

        if (agreementBegin < activityBegin || agreementEnd > activityEnd) {
            throw new ValidateException(I18N.text(I18NKeys.AGREEMENT_TIME_OUT_OF_RANGE_ERROR));
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setLimit(-1);
        query.setOffset(0);
        query.setSearchSource("db");

        Filter activityFilter = new Filter();
        activityFilter.setFieldName(TPMActivityAgreementFields.ACTIVITY_ID);
        activityFilter.setOperator(Operator.EQ);
        activityFilter.setFieldValues(Lists.newArrayList(activityId));

        Filter accountFilter = new Filter();
        accountFilter.setFieldName(TPMActivityAgreementFields.STORE_ID);
        accountFilter.setOperator(Operator.EQ);
        accountFilter.setFieldValues(Lists.newArrayList((String) arg.getObjectData().get(TPMActivityAgreementFields.STORE_ID)));

        query.setFilters(Lists.newArrayList(activityFilter, accountFilter));

        if (describeCacheService.isExistField(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, TPMActivityAgreementFields.RIO_PROTOCOL_STATUS)) {
            Filter invalidFilter = new Filter();
            invalidFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
            invalidFilter.setOperator(Operator.NEQ);
            invalidFilter.setFieldValues(Lists.newArrayList("void"));
            query.getFilters().add(invalidFilter);

            Filter invalidEmptyFilter = new Filter();
            invalidEmptyFilter.setFieldName(TPMActivityAgreementFields.RIO_PROTOCOL_STATUS);
            invalidEmptyFilter.setOperator(Operator.IS);
            invalidEmptyFilter.setFieldValues(Lists.newArrayList());
            query.getFilters().add(invalidEmptyFilter);
            query.setPattern("1 and 2 and (3 or 4)");
        }

        List<IObjectData> agreements = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ, query);

        log.info("query store under the same activity have agreement : {}, query : {}", agreements, query);

        for (IObjectData agreement : agreements) {
            if (agreement.getId().equals(arg.getObjectData().getId())) {
                continue;
            }

            long begin = (long) agreement.get(TPMActivityAgreementFields.BEGIN_DATE);
            long end = (long) agreement.get(TPMActivityAgreementFields.END_DATE);

            log.info("start compare - begin : {}, end : {}, agreement begin : {}, agreement end : {}", begin, end, agreementBegin, agreementEnd);

            if (TimeUtils.isIntervalOverlap(begin, end, agreementBegin, agreementEnd)) {
                throw new ValidateException(I18N.text(I18NKeys.TIME_OVERLAP_IN_ACTIVITY_AND_STORE_ERROR));
            }
        }
    }

    private void validateEndDate(Arg arg) {
        long begin = (long) arg.getObjectData().get(TPMActivityAgreementFields.BEGIN_DATE);
        long end = TimeUtils.convertToDayEndIfTimeWasDayBegin((long) arg.getObjectData().get(TPMActivityAgreementFields.END_DATE));
        arg.getObjectData().put(TPMActivityAgreementFields.END_DATE, end);

        if (end <= begin) {
            throw new ValidateException(I18N.text(I18NKeys.ADD_AGREEMENT_DATE_ERROR));
        }
    }

    @Override
    protected Result doAct(Arg arg) {
        return packTransactionProxy.packAct(this, arg);
    }

    @Override
    public Result doActTransaction(Arg arg) {
        Result result = super.doAct(arg);
        editProofPeriodTime(result);
        return result;
    }

    private void editProofPeriodTime(Result result) {
        tpmDisplayReportService.editProofPeriodTime(actionContext.getUser().getUpstreamOwnerIdOrUserId(), result);
    }

}
