package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityProofAiConfigVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * AI display recognition configuration entity
 * <p>
 * Created for AI display recognition feature
 */
@Data
@ToString
public class ActivityProofAiConfigEntity implements Serializable {

    /**
     * Enable/disable AI display recognition
     */
    @Property("enable_ai_display_recognition")
    private Boolean enableAiDisplayRecognition;

    /**
     * Display recognition model
     */
    @Property("display_recognition_model")
    private String displayRecognitionModel;

    /**
     * Adaptation rules
     */
    @Property("adaptation_rule")
    private String adaptationRule;

    public static ActivityProofAiConfigEntity fromVO(ActivityProofAiConfigVO vo) {
        if (vo == null) {
            return null;
        }
        ActivityProofAiConfigEntity entity = new ActivityProofAiConfigEntity();
        entity.setEnableAiDisplayRecognition(vo.getEnableAiDisplayRecognition());
        entity.setDisplayRecognitionModel(vo.getDisplayRecognitionModel());
        entity.setAdaptationRule(vo.getAdaptationRule());
        return entity;
    }
}