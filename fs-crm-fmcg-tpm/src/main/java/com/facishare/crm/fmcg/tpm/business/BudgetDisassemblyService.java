package com.facishare.crm.fmcg.tpm.business;


import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.controller.TPMBudgetDisassemblyObjDescribeLayoutController;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetDimensionEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypeNodeEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.BudgetTypePO;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.UdefButton;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.util.MD5Util;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 类说明
 *
 * <AUTHOR>
 * @date 2022/8/12
 */
@Service
@SuppressWarnings("all")
@Slf4j
public class BudgetDisassemblyService implements IBudgetDisassemblyService {

    @Resource
    private IBudgetAccountService budgetAccountService;
    @Resource
    private ServiceFacade serviceFacade;
    @Resource
    private BudgetTypeDAO budgetTypeDAO;
    @Resource
    private IBudgetCompareService budgetCompareService;
    @Resource
    private IFiscalTimeService fiscalTimeService;
    @Resource
    private FuncClient funcClient;

    public static List<String> SYSTEM_FIELDS = new ArrayList<>();
    public static final String BUDGET_DISASSEMBLY_RETRY_ACTION_CODE = "DisassemblyRetry";
    public static final String BUDGET_DISASSEMBLY_RETRY_BUTTON_API_NAME = "BudgetDisassemblyRetry_button_default";

    static {
        SYSTEM_FIELDS.add("tenant_id");
        SYSTEM_FIELDS.add("name");
        SYSTEM_FIELDS.add("owner");
        SYSTEM_FIELDS.add("lock_status");
        SYSTEM_FIELDS.add("life_status");
        SYSTEM_FIELDS.add("record_type");
        SYSTEM_FIELDS.add("created_by");
        SYSTEM_FIELDS.add("create_time");
        SYSTEM_FIELDS.add("last_modified_by");
        SYSTEM_FIELDS.add("last_modified_time");
        SYSTEM_FIELDS.add("extend_obj_data_id");
        SYSTEM_FIELDS.add("package");
        SYSTEM_FIELDS.add("object_describe_id");
        SYSTEM_FIELDS.add("object_describe_api_name");
        SYSTEM_FIELDS.add("version");
        SYSTEM_FIELDS.add("lock_user");
        SYSTEM_FIELDS.add("lock_rule");
        SYSTEM_FIELDS.add("life_status_before_invalid");
        SYSTEM_FIELDS.add("is_deleted");
        SYSTEM_FIELDS.add("order_by");
        SYSTEM_FIELDS.add("out_tenant_id");
        SYSTEM_FIELDS.add("out_owner");
        SYSTEM_FIELDS.add("data_own_department");
        SYSTEM_FIELDS.add("active_status");
        SYSTEM_FIELDS.add("relevant_team");
        SYSTEM_FIELDS.add("_id");
        SYSTEM_FIELDS.add("owner_department");
        SYSTEM_FIELDS.add("__tbIndex");
        SYSTEM_FIELDS.add("amount");
        SYSTEM_FIELDS.add("take_apart_in_amount");
        SYSTEM_FIELDS.add("_data_index");
        SYSTEM_FIELDS.add("dataIndex");
        SYSTEM_FIELDS.add("_PK_ID");
    }

    @Override
    public Tuple2<String, Long> getPeriodApiNameAndPeriod(String tenantId, String timeDimension, IObjectData masterData) {
        String periodApiName = "";
        Long period = 0L;
        switch (timeDimension) {
            case "quarter":
                periodApiName = TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER;
                Long budgetPeriodQuarter = masterData.get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_PERIOD_QUARTER, Long.class);
                if (budgetPeriodQuarter == null) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_0));
                }
                period = fiscalTimeService.correctPeriodTime(tenantId, timeDimension, budgetPeriodQuarter);
                break;
            case "month":
                periodApiName = TPMBudgetAccountFields.BUDGET_PERIOD_MONTH;
                Long budgetPeriodMonth = masterData.get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_PERIOD_MONTH, Long.class);
                if (budgetPeriodMonth == null) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_1));
                }
                period = fiscalTimeService.correctPeriodTime(tenantId, timeDimension, budgetPeriodMonth);
                break;
            case "year":
                periodApiName = TPMBudgetAccountFields.BUDGET_PERIOD_YEAR;
                Long budgetPeriodYear = masterData.get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_PERIOD_YEAR, Long.class);
                if (budgetPeriodYear == null) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_2));
                }
                period = fiscalTimeService.correctPeriodTime(tenantId, timeDimension, budgetPeriodYear);
                break;
            default:
        }
        return new Tuple2<>(periodApiName, period);
    }

    @Override
    public IObjectData buildDataForCreateBudgetAccount(String tenantId, IObjectData newDetail, IObjectData masterData, BudgetTypeNodeEntity targetNode) {

        String name = newDetail.get(TPMBudgetAccountFields.NAME, String.class);
        List<String> departments = (List<String>) newDetail.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT);

        List<String> owners = (List<String>) newDetail.get(CommonFields.OWNER);

        String fasterAccountTableId = masterData.getId();
        String targetTypeId = masterData.get(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, String.class);
        String controlTimeDimension = targetNode.getControlTimeDimension();
        String targetNodeId = newDetail.get(TPMBudgetDisassemblyNewDetailsFields.BUDGET_NODE_ID, String.class);
        Tuple2<String, Long> periodApiNameAndPeriod = getPeriodApiNameAndPeriod(tenantId, targetNode.getTimeDimension(), newDetail);
        String budgetSubjectId = newDetail.get(TPMBudgetAccountFields.BUDGET_SUBJECT_ID, String.class);
        String productCategoryId = newDetail.get(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID, String.class);
        String productId = newDetail.get(TPMBudgetAccountFields.PRODUCT_ID, String.class);
        String dealerId = newDetail.get(TPMBudgetAccountFields.DEALER_ID, String.class);

        BigDecimal amount = new BigDecimal(newDetail.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT, String.class));


        IObjectData data = budgetAccountService.buildDefaultValForCreate();
        data.setRecordType(targetNode.getRecordType());
        data.setTenantId(tenantId);
        data.setOwner(owners);
        data.setCreatedBy(CollectionUtils.isEmpty(owners) ? "-10000" : owners.get(0));
        data.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT);
        data.set(TPMBudgetAccountFields.NAME, name);
        data.set(TPMBudgetAccountFields.BUDGET_DEPARTMENT, departments);
        data.set(TPMBudgetAccountFields.PARENT_ID, fasterAccountTableId);
        data.set(TPMBudgetAccountFields.BUDGET_TYPE_ID, targetTypeId);
        data.set(TPMBudgetAccountFields.BUDGET_NODE_ID, targetNodeId);
        data.set(TPMBudgetAccountFields.EFFECTIVE_PERIOD, targetNode.getTimeDimension());
        if (!StringUtils.isEmpty(controlTimeDimension)) {
            data.set(TPMBudgetAccountFields.CONTROL_PERIOD, controlTimeDimension);
        }

        data.set(periodApiNameAndPeriod.getFirst(), periodApiNameAndPeriod.getSecond());

        if (!StringUtils.isEmpty(budgetSubjectId)) {
            data.set(TPMBudgetAccountFields.BUDGET_SUBJECT_ID, budgetSubjectId);
        }
        if (!StringUtils.isEmpty(productCategoryId)) {
            data.set(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID, productCategoryId);
        }
        if (!StringUtils.isEmpty(productId)) {
            data.set(TPMBudgetAccountFields.PRODUCT_ID, productId);
        }
        if (!StringUtils.isEmpty(dealerId)) {
            data.set(TPMBudgetAccountFields.DEALER_ID, dealerId);
        }
        for (BudgetDimensionEntity dimension : targetNode.getDimensions()) {
            if (!TPMBudgetDisassemblyObjDescribeLayoutController.DIMENSION_FIELD_API_NAME.contains(dimension.getApiName())) {
                data.set(dimension.getApiName(), newDetail.get(dimension.getApiName()));
            }
        }

        data.set(TPMBudgetAccountFields.BASE_AMOUNT, 0);
        data.set(TPMBudgetAccountFields.TOTAL_AMOUNT, amount);
        data.set(TPMBudgetAccountFields.AVAILABLE_AMOUNT, amount);
        data.set(TPMBudgetAccountFields.STATISTIC_DEPARTMENT_AMOUNT, amount);

        Boolean automaticEnableAfterDisassembly = targetNode.getAutomaticEnableAfterDisassembly();
        data.set(TPMBudgetAccountFields.BUDGET_STATUS, Boolean.TRUE.equals(automaticEnableAfterDisassembly) ?
                TPMBudgetAccountFields.BUDGET_STATUS__ENABLE : TPMBudgetAccountFields.BUDGET_STATUS__DISABLE);

        return data;
    }

    @Override
    public void validateMasterData(String tenantId, IObjectData data) {
        String sourceAccountId = data.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
        IObjectData sourceAccount = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), sourceAccountId, ApiNames.TPM_BUDGET_ACCOUNT);
        if (Objects.isNull(sourceAccount)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_3));
        }
        if (!Objects.equals(sourceAccount.get(CommonFields.LIFE_STATUS, String.class), CommonFields.LIFE_STATUS__NORMAL)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_4));
        }
        if (!Objects.equals(sourceAccount.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class), TPMBudgetAccountFields.BUDGET_STATUS__ENABLE)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_5));
        }
        //校验金额是否大于0
        String availableAmount = sourceAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class);
        if (TPMGrayUtils.disassemblyAllowZero(tenantId)) {
            if (new BigDecimal(availableAmount).compareTo(BigDecimal.ZERO) < 0) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_6));
            }
        } else {
            if (new BigDecimal(availableAmount).compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_7));
            }
        }


        String budgetTypeId = sourceAccount.get(TPMBudgetAccountFields.BUDGET_TYPE_ID, String.class);
        BudgetTypePO budgetType = budgetTypeDAO.get(tenantId, budgetTypeId);
        if (Objects.isNull(budgetType)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_8));
        }

        data.set(TPMBudgetDisassemblyFields.BUDGET_TYPE_ID, budgetTypeId);


        String sourceNodeId = sourceAccount.get(TPMBudgetAccountFields.BUDGET_NODE_ID, String.class);
        BudgetTypeNodeEntity sourceNode = budgetType.getNodes().stream().filter(f -> f.getNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(sourceNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_9));
        }
        data.set(TPMBudgetDisassemblyFields.SOURCE_BUDGET_NODE_ID, sourceNodeId);


        BudgetTypeNodeEntity targetNode = budgetType.getNodes().stream().filter(f -> f.getParentNodeId().equals(sourceNodeId)).findFirst().orElse(null);
        if (Objects.isNull(targetNode)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_10));
        }
        data.set(TPMBudgetDisassemblyFields.TARGET_BUDGET_NODE_ID, targetNode.getNodeId());
    }

    @Override
    public void correctNewDetailsValue(List<ObjectDataDocument> newDetailsDocuments, BudgetTypeNodeEntity targetNode) {
        if (CollectionUtils.isEmpty(newDetailsDocuments)) {
            return;
        }
        String timeDimension = targetNode.getTimeDimension();
        String periodApiNameFromTemplate = String.format("budget_period_%s", timeDimension);
        List<String> dimensionsApiNames = targetNode.getDimensions().stream().map(BudgetDimensionEntity::getApiName).collect(Collectors.toList());


        for (ObjectDataDocument newDetailsDocument : newDetailsDocuments) {
            newDetailsDocument.put(TPMBudgetDisassemblyNewDetailsFields.TAKE_APART_IN_AMOUNT, newDetailsDocument.get(TPMBudgetDisassemblyNewDetailsFields.AMOUNT));

            for (String periodApiName : TPMBudgetDisassemblyObjDescribeLayoutController.TIME_DIMENSION_FIELD_API_NAME) {
                if (Objects.equals(periodApiNameFromTemplate, periodApiName)) {
                    try {
                        IObjectData data = newDetailsDocument.toObjectData();
                        Long time = data.get(periodApiNameFromTemplate, Long.class);
                        newDetailsDocument.put(periodApiName, fiscalTimeService.correctPeriodTime(data.getTenantId(), timeDimension, time));
                    } catch (Exception ex) {
                        log.error("correctNewDetailsValue err", ex);
                    }
                    continue;
                }
                newDetailsDocument.put(periodApiName, null);
            }

            for (String dimensionApiName : TPMBudgetDisassemblyObjDescribeLayoutController.DIMENSION_FIELD_API_NAME) {
                if (dimensionsApiNames.contains(dimensionApiName)) {
                    continue;
                }
                newDetailsDocument.put(dimensionApiName, null);
            }
        }
    }

    @Override
    public void correctExsitsDetailsValue(ObjectDataDocument existDetail, BudgetTypeNodeEntity targetNode, IObjectData targetAccount) {
        if (existDetail == null) {
            return;
        }

        if (targetAccount == null) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_11));
        }
        String availableAmountStr = targetAccount.get(TPMBudgetAccountFields.AVAILABLE_AMOUNT, String.class);
        if (StringUtils.isEmpty(availableAmountStr)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_12));
        }
        BigDecimal availableAmount = new BigDecimal(availableAmountStr);
        existDetail.put(TPMBudgetDisassemblyExistsDetailsFields.AVAILABLE_AMOUNT, availableAmount);

        String amount = (String) existDetail.get(TPMBudgetDisassemblyExistsDetailsFields.AMOUNT);
        existDetail.put(TPMBudgetDisassemblyExistsDetailsFields.AMOUNT_AFTER_OPERATION, availableAmount.add(new BigDecimal(amount)));
    }

    @Override
    public IObjectData findData(String tenantId, String id, String apiName) {
        SearchTemplateQuery queryMaster = new SearchTemplateQuery();
        queryMaster.setLimit(1);
        queryMaster.setOffset(0);

        IFilter idFilter = new Filter();
        idFilter.setFieldName("_id");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(id));

        queryMaster.setFilters(Lists.newArrayList(idFilter));
        queryMaster.setSearchSource("db");
        queryMaster.setNeedReturnCountNum(false);
        queryMaster.setNeedReturnQuote(false);

        QueryResult<IObjectData> masterDataQueryResult =
                serviceFacade.findBySearchQuery(User.systemUser(tenantId), apiName, queryMaster);
        List<IObjectData> data = masterDataQueryResult.getData();

        if (CollectionUtils.isNotEmpty(data)) {
            return data.get(0);
        }
        return null;
    }

    @Override
    public void existsDetailDuplicateValidate(List<IObjectData> existsDetails) {
        List<String> accountIdsList = com.beust.jcommander.internal.Lists.newArrayList();
        Set<String> accountIdsSet = Sets.newHashSet();
        for (IObjectData existsDetail : existsDetails) {
            String accountId = existsDetail.get(TPMBudgetDisassemblyExistsDetailsFields.BUDGET_ACCOUNT_ID, String.class);
            accountIdsList.add(accountId);
            accountIdsSet.add(accountId);
        }
        if (accountIdsList.size() != accountIdsSet.size()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_13));
        }
    }

    public boolean accountExist(String tenantId, String name, String apiName) {
        SearchTemplateQuery queryMaster = new SearchTemplateQuery();
        queryMaster.setLimit(1);
        queryMaster.setOffset(0);

        IFilter idFilter = new Filter();
        idFilter.setFieldName("name");
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(name));

        queryMaster.setFilters(Lists.newArrayList(idFilter));
        queryMaster.setSearchSource("db");
        queryMaster.setNeedReturnCountNum(false);
        queryMaster.setNeedReturnQuote(false);

        QueryResult<IObjectData> masterDataQueryResult =
                serviceFacade.findBySearchQuery(User.systemUser(tenantId), apiName, queryMaster);
        List<IObjectData> data = masterDataQueryResult.getData();
        return CollectionUtils.isNotEmpty(data);
    }

    @Override
    public void newDetailDuplicateValidateV2(String tenantId, List<IObjectData> newDetails, BudgetTypeNodeEntity node) {
        Set<String> nameSet = Sets.newHashSet();
        Set<String> md5Set = Sets.newHashSet();
        for (IObjectData budget : newDetails) {
            String name = budget.getName();
            if (accountExist(tenantId, name, ApiNames.TPM_BUDGET_ACCOUNT)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_14), name));
            }
            if (nameSet.contains(name)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_15), name));
            } else {
                nameSet.add(name);
            }

            StringBuilder sb = new StringBuilder();


            List<String> department = CommonUtils.cast(budget.get(TPMBudgetAccountFields.BUDGET_DEPARTMENT), String.class);
            sb.append(JSON.toJSONString(department)).append(",");


            Tuple2<String, Long> periodApiNameAndPeriod = getPeriodApiNameAndPeriod(tenantId, node.getTimeDimension(), budget);
            sb.append(periodApiNameAndPeriod.getSecond()).append(",");


            List<BudgetDimensionEntity> dimensions = node.getDimensions();
            if (!CollectionUtils.isEmpty(dimensions)) {
                for (BudgetDimensionEntity dimensionEntity : dimensions) {
                    Filter dimensionFilter = new Filter();
                    String value = budget.get(dimensionEntity.getApiName(), String.class);
                    if (Strings.isNullOrEmpty(value)) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_0) + dimensionEntity.getApiName() + I18N.text(I18NKeys.BUDGET_ACCOUNT_SERVICE_1));
                    }
                    sb.append(value).append(",");

                }
            }

            String objStr = sb.toString();
            log.info("objStr:{}", objStr);
            String md5 = MD5Util.getMD5(objStr.length() > 2 ? objStr.substring(2, objStr.length()) : objStr);
            if (md5Set.contains(md5)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_DISASSEMBLY_SERVICE_16));
            } else {
                md5Set.add(md5);
            }
        }
    }


    @Override
    public boolean isOnlyEditCustomFields(Set<String> updateFieldsSet) {
        for (String field : updateFieldsSet) {
            if (!field.endsWith("__c")) {
                return false;
            }
        }
        return true;
    }

    @Override
    public IBudgetOperator initSourceAccountOperator(User user, String dataId) {
        IObjectData master = serviceFacade.findObjectDataIgnoreAll(User.systemUser(user.getTenantId()), dataId, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
        if (master == null) {
            log.error("find source account empty,dataId:{}", dataId);
            return null;
        }
        String traceId = master.getId().toUpperCase(Locale.ROOT);
        String sourceAccountId = master.get(TPMBudgetDisassemblyFields.SOURCE_BUDGET_ACCOUNT_ID, String.class);
        IBudgetOperator sourceAccountOperator = BudgetOperatorFactory.initOperator(BizType.TAKE_APART_OUT, user, sourceAccountId, traceId, traceId, master);
        if (sourceAccountOperator.tryLock(60)) {
            return sourceAccountOperator;
        } else {
            log.error("try lock source budget account failed,please try again later.tenantId:{},dataId:{}", user.getTenantId(), dataId);
            return null;
        }
    }

    @Override
    public void initFailedRetryButton(String tenantId) {
        IUdefButton old = serviceFacade.findButtonByApiName(User.systemUser(tenantId), BUDGET_DISASSEMBLY_RETRY_BUTTON_API_NAME, ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
        if (Objects.isNull(old)) {
            IUdefButton button = new UdefButton();
            button.setTenantId(tenantId);
            button.setDescribeApiName(ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ);
            button.setApiName(BUDGET_DISASSEMBLY_RETRY_BUTTON_API_NAME);
            button.setLabel("拆解重试");//ignorei18n
            button.setDefineType("system");
            button.setButtonType("common");
            button.setParamForm(Lists.newArrayList());
            button.setJumpUrl("");
            button.setLockDataShowButton(false);

            Wheres wheres = new Wheres();

            IFilter failedStatusFilter = new Filter();
            failedStatusFilter.setFieldName(TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS);
            failedStatusFilter.setOperator(Operator.IN);
            failedStatusFilter.setFieldValues(Lists.newArrayList(
                    TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__UNFROZEN_FAILED,
                    TPMBudgetDisassemblyFields.DISASSEMBLY_STATUS__FAILED
            ));

            wheres.setFilters(Lists.newArrayList(failedStatusFilter));
            button.setWheres(Lists.newArrayList(wheres));

            button.setIsActive(true);
            button.setDeleted(false);
            button.setUsePages(Lists.newArrayList("detail"));
            serviceFacade.createCustomButton(User.systemUser(tenantId), button);

            AuthContext authContext = AuthContext.builder().userId("-10000").tenantId(tenantId).appId("CRM").build();
            String functionCode = String.format("%s||%s", ApiNames.TPM_BUDGET_DISASSEMBLY_OBJ, BUDGET_DISASSEMBLY_RETRY_ACTION_CODE);
            FunctionPojo function = new FunctionPojo();
            function.setAppId("CRM");
            function.setParentCode("00000000000000000000000000000000");
            function.setTenantId(tenantId);
            function.setFuncName("拆解重试");//ignorei18n
            function.setFuncCode(functionCode);
            function.setFuncType(0);
            function.setIsEnabled(true);
            funcClient.addFunc(authContext, Lists.newArrayList(function));

            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());
        }
    }
}
