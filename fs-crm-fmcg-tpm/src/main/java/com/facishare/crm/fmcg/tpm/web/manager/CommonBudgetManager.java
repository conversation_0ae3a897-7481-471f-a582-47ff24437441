package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.ICommonBudgetManager;
import com.facishare.crm.fmcg.tpm.web.manager.dto.CommonConsumeItem;
import com.facishare.crm.fmcg.tpm.web.manager.dto.CommonWriteOffItem;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.dao.pg.config.MetadataTransactional;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.OrderBy;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/11/15 14:26
 */
//IgnoreI18nFile
@Service
@SuppressWarnings("Duplicates")
@Slf4j
public class CommonBudgetManager implements ICommonBudgetManager {

    @Resource
    private ServiceFacade serviceFacade;
    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;

    @Override
    @MetadataTransactional
    public void freeze(String tenantId, IObjectData bizObj, List<CommonConsumeItem> data) {
        StringBuilder message = new StringBuilder("预算冻结成功，冻结明细：");
        if (CollectionUtils.isNotEmpty(data)) {
            for (CommonConsumeItem datum : data) {
                datum.getOperator().freeze(datum.getAmount());
                datum.getOperator().recalculate();
                message.append("[").append(datum.getOperator().getAccount().getName()).append(":").append(datum.getAmount()).append("]");
            }
        } else {
            message.append("无");
        }
        message.append("。");

        Map<String, Object> updateArg = new HashMap<>();
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, CommonBudgetBizObjectFields.BUDGET_CODE_FREEZE_SUCCESS);
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateArg);

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizObj, message.toString());
    }

    @Override
    public void appendFreeze(String tenantId, IObjectData bizObj, List<CommonConsumeItem> data) {
        StringBuilder message = new StringBuilder("追加预算冻结成功，冻结明细：");
        if (CollectionUtils.isNotEmpty(data)) {
            for (CommonConsumeItem datum : data) {
                datum.getOperator().freeze(datum.getAmount());
                datum.getOperator().recalculate();
                message.append("[").append(datum.getOperator().getAccount().getName()).append(":").append(datum.getAmount()).append("]");
            }
        } else {
            message.append("无");
        }
        message.append("。");

        Map<String, Object> updateArg = new HashMap<>();
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, CommonBudgetBizObjectFields.BUDGET_CODE_FREEZE_SUCCESS);
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateArg);

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizObj, message.toString());
    }

    @Override
    @MetadataTransactional
    public void saveError(String tenantId, IObjectData bizObj, String message) {
        Map<String, Object> updateArg = new HashMap<>();
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message);
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateArg);

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizObj, message);
    }

    @Override
    public List<IObjectData> queryFrozenDetails(String tenantId, IObjectData bizObj, String businessTraceId, String approvalTraceId) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setOffset(0);
        stq.setLimit(-1);
        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setNeedRightJoin(false);

        Filter mainTypeFiler = new Filter();
        mainTypeFiler.setFieldName(TPMBudgetAccountDetailFields.MAIN_TYPE);
        mainTypeFiler.setOperator(Operator.EQ);
        mainTypeFiler.setFieldValues(Lists.newArrayList(MainType.FREEZE.value()));

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(bizObj.getDescribeApiName()));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(bizObj.getId()));

        Filter businessTraceIdFilter = new Filter();
        businessTraceIdFilter.setFieldName(TPMBudgetAccountDetailFields.BIZ_TRACE_ID);
        businessTraceIdFilter.setOperator(Operator.EQ);
        businessTraceIdFilter.setFieldValues(Lists.newArrayList(businessTraceId));

        Filter approvalTraceIdFilter = new Filter();
        approvalTraceIdFilter.setFieldName(TPMBudgetAccountDetailFields.APPROVAL_TRACE_ID);
        approvalTraceIdFilter.setOperator(Operator.EQ);
        approvalTraceIdFilter.setFieldValues(Lists.newArrayList(approvalTraceId));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        Filter operateMarkEmptyFilter = new Filter();
        operateMarkEmptyFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkEmptyFilter.setOperator(Operator.IS);
        operateMarkEmptyFilter.setFieldValues(Lists.newArrayList());

        Filter operateMarkNeqFilter = new Filter();
        operateMarkNeqFilter.setFieldName(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL);
        operateMarkNeqFilter.setOperator(Operator.NEQ);
        operateMarkNeqFilter.setFieldValues(Lists.newArrayList(BudgetDetailOperateMark.COMPLETED_UNFREEZE.value()));

        stq.setFilters(Lists.newArrayList(mainTypeFiler, apiNameFilter, idFilter, businessTraceIdFilter, approvalTraceIdFilter, detailStatusFilter, operateMarkEmptyFilter, operateMarkNeqFilter));
        stq.setPattern("1 and 2 and 3 and 4 and 5 and 6 and ( 7 or 8 )");

        OrderBy order = new OrderBy();
        order.setFieldName("_id");
        order.setIsAsc(true);
        stq.setOrders(Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList("_id", "name", TPMBudgetAccountDetailFields.MAIN_TYPE, TPMBudgetAccountDetailFields.AMOUNT, TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID));
    }

    @Override
    public List<IObjectData> queryRelatedDetails(String tenantId, IObjectData bizObj) {
        SearchTemplateQuery stq = new SearchTemplateQuery();

        stq.setOffset(0);
        stq.setLimit(-1);

        stq.setNeedReturnCountNum(false);
        stq.setNeedReturnQuote(false);
        stq.setNeedRightJoin(false);

        Filter apiNameFilter = new Filter();
        apiNameFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME);
        apiNameFilter.setOperator(Operator.EQ);
        apiNameFilter.setFieldValues(Lists.newArrayList(bizObj.getDescribeApiName()));

        Filter idFilter = new Filter();
        idFilter.setFieldName(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(bizObj.getId()));

        Filter detailStatusFilter = new Filter();
        detailStatusFilter.setFieldName(TPMBudgetAccountDetailFields.DETAIL_STATUS);
        detailStatusFilter.setOperator(Operator.EQ);
        detailStatusFilter.setFieldValues(Lists.newArrayList(TPMBudgetAccountDetailFields.DetailStatus.INCLUDE));

        stq.setFilters(Lists.newArrayList(apiNameFilter, idFilter, detailStatusFilter));

        OrderBy order = new OrderBy();
        order.setFieldName("_id");
        order.setIsAsc(true);
        stq.setOrders(Lists.newArrayList(order));

        return QueryDataUtil.find(serviceFacade, tenantId, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, stq, Lists.newArrayList("_id", "name", TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID));
    }

    @Override
    @MetadataTransactional
    public void unfreeze(String tenantId, IObjectData bizObj, List<IBudgetOperator> operators) {
        StringBuilder message = new StringBuilder("预算解冻成功，解冻明细：");
        if (CollectionUtils.isNotEmpty(operators)) {
            for (IBudgetOperator operator : operators) {
                BigDecimal amount = operator.unfreeze(BizType.RELEASE);
                operator.recalculate();
                message.append("[").append(operator.getAccount().getName()).append(":").append(amount).append("]");
            }
        } else {
            message.append("无");
        }
        message.append("。");

        Map<String, Object> updateArg = new HashMap<>();
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateArg);

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizObj, message.toString());
    }

    @Override
    @MetadataTransactional
    public void writeOff(String tenantId, IObjectData bizObj, IObjectData writeOffObj, List<CommonWriteOffItem> data) {
        StringBuilder message = new StringBuilder("预算核销成功，核销明细：");
        if (CollectionUtils.isNotEmpty(data)) {
            for (CommonWriteOffItem datum : data) {
                if (datum.isFrozen()) {
                    if (TPMGrayUtils.enableOverLimitWriteOff(tenantId)) {
                        BigDecimal frozenAmount = datum.getOperator().frozenAmount();
                        if (datum.getAmount().compareTo(frozenAmount) > 0) {
                            datum.getOperator().unfreeze(BizType.CONSUME, frozenAmount);
                        } else {
                            datum.getOperator().unfreeze(BizType.CONSUME, datum.getAmount());
                        }
                    } else {
                        datum.getOperator().unfreeze(BizType.CONSUME, datum.getAmount());
                    }
                }

                datum.getOperator().expenditure(datum.getAmount());
                datum.getOperator().recalculate();

                message.append("[").append(datum.getOperator().getAccount().getName()).append(":").append(datum.getAmount()).append("]");
            }
        } else {
            message.append("无");
        }
        message.append("。");

        Map<String, Object> updateBizObjArg = new HashMap<>();
        updateBizObjArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateBizObjArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateBizObjArg);

        Map<String, Object> updateWriteOffObjArg = new HashMap<>();
        updateWriteOffObjArg.put(CommonBudgetWriteOffObjectFields.BUDGET_OPERATION_CODE, CommonBudgetWriteOffObjectFields.BUDGET_CODE_WRITE_OFF_SUCCESS);
        updateWriteOffObjArg.put(CommonBudgetWriteOffObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateWriteOffObjArg.put(CommonBudgetWriteOffObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), writeOffObj, updateWriteOffObjArg);

        IObjectDescribe writeOffDescribe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, writeOffDescribe, writeOffObj, message.toString());

        IObjectDescribe bizDescribe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, bizDescribe, bizObj, message.toString());
    }

    @Override
    @MetadataTransactional
    public void close(String tenantId, String operator, IObjectData bizObj, List<IBudgetOperator> operators) {
        StringBuilder message = new StringBuilder();

        String name = "";
        try {
            int tenantIdInt = Integer.parseInt(tenantId);
            int operatorInt = Integer.parseInt(operator);

            EmployeeDto employee = organizationService.getEmployee(tenantIdInt, operatorInt);

            if (Objects.nonNull(employee)) {
                name = employee.getName();
            }
        } catch (Exception ex) {
            log.warn("convert operator name error : ", ex);
        }

        if (Strings.isNullOrEmpty(name)) {
            message.append("结案成功，结案解冻明细：");
        } else {
            message.append("结案成功，结案人：").append(name).append("(").append(operator.trim()).append(")，结案明细：");
        }

        if (CollectionUtils.isNotEmpty(operators)) {
            for (IBudgetOperator innerOperator : operators) {
                BigDecimal amount = innerOperator.unfreeze(BizType.RELEASE);
                innerOperator.recalculate();
                message.append("[").append(innerOperator.getAccount().getName()).append(":").append(amount).append("]");
            }
        } else {
            message.append("无");
        }
        message.append("。");

        Map<String, Object> updateArg = new HashMap<>();
        if (bizObj.getDescribeApiName().equals(ApiNames.TPM_ACTIVITY_OBJ)) {
            updateArg.put(TPMActivityFields.CLOSED_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
            updateArg.put(TPMActivityFields.CLOSE_TIME, System.currentTimeMillis());
        }
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_CODE, CommonBudgetBizObjectFields.BUDGET_CODE_CLOSE);
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_MESSAGE, message.toString());
        updateArg.put(CommonBudgetBizObjectFields.BUDGET_OPERATION_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(User.systemUser(tenantId), bizObj, updateArg);

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizObj.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizObj, message.toString());
    }

    @Override
    @MetadataTransactional
    public void cancel(String tenantId, IObjectData bizData, List<IObjectData> details, List<IBudgetOperator> operators) {
        Map<String, Object> change = new HashMap<>();
        change.put(TPMBudgetAccountDetailFields.DETAIL_STATUS, TPMBudgetAccountDetailFields.DetailStatus.EXCLUDE);
        serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), details, change);

        for (IBudgetOperator operator : operators) {
            operator.recalculate();
        }

        IObjectDescribe describe = serviceFacade.findObject(tenantId, bizData.getDescribeApiName());
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, bizData, "取消所有预算数据成功。");
    }
}