package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetAccrualRuleNodeVO;
import lombok.Data;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/10/9 上午11:47
 */
@Data
@ToString
public class BudgetAccrualRuleNodeEntity implements Serializable {

    public static final String F_WHERE_CONDITION = "whereCondition";
    public static final String F_CONDITION_CODE = "condition_code";
    public static final String F_PREVIEW = "preview";

    public static final String F_NODE_ID = "node_id";
    public static final String F_FIELD_RELATION = "field_relation";
    public static final String F_RATIO = "ratio";
    public static final String F_BUDGET_TYPE = "budget_type";

    // 条件code
    @Property(F_CONDITION_CODE)
    private String conditionCode;

    //触发条件
    @Embedded(F_WHERE_CONDITION)
    private List<BudgetWhereConditionEntity> whereConditions;

    @Property(F_PREVIEW)
    private String previewString;

    //计提比例
    @Property(F_RATIO)
    private String ratio;

    //预算类型
    @Property(F_BUDGET_TYPE)
    private String budgetType;

    //预算节点
    @Property(F_NODE_ID)
    private String nodeId;

    //预算表对象映射字段
    @Embedded(F_FIELD_RELATION)
    private List<BudgetFieldRelationEntity> fieldRelation;


    public static BudgetAccrualRuleNodeEntity fromVO(BudgetAccrualRuleNodeVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }
        BudgetAccrualRuleNodeEntity entity = new BudgetAccrualRuleNodeEntity();
        entity.setConditionCode(vo.getConditionCode());
        entity.setBudgetType(vo.getBudgetType());
        entity.setNodeId(vo.getNodeId());
        entity.setRatio(vo.getRatio());
        entity.setPreviewString(vo.getPreviewString());
        if (CollectionUtils.isEmpty(vo.getWhereConditions())) {
            entity.setWhereConditions(new ArrayList<>());
        } else {
            entity.setWhereConditions(vo.getWhereConditions().stream().map(BudgetWhereConditionEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getFieldRelation())) {
            entity.setFieldRelation(new ArrayList<>());
        } else {
            entity.setFieldRelation(vo.getFieldRelation().stream().map(BudgetFieldRelationEntity::fromVO).collect(Collectors.toList()));
        }
        return entity;
    }

    public static BudgetAccrualRuleNodeVO toVO(BudgetAccrualRuleNodeEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        BudgetAccrualRuleNodeVO vo = new BudgetAccrualRuleNodeVO();
        vo.setConditionCode(entity.getConditionCode());
        vo.setBudgetType(entity.getBudgetType());
        vo.setNodeId(entity.getNodeId());
        vo.setRatio(entity.getRatio());
        vo.setPreviewString(entity.getPreviewString());
        if (CollectionUtils.isEmpty(entity.getWhereConditions())) {
            vo.setWhereConditions(new ArrayList<>());
        } else {
            vo.setWhereConditions(entity.getWhereConditions().stream().map(BudgetWhereConditionEntity::toVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(entity.getFieldRelation())) {
            vo.setFieldRelation(new ArrayList<>());
        } else {
            vo.setFieldRelation(entity.getFieldRelation().stream().map(BudgetFieldRelationEntity::toVO).collect(Collectors.toList()));
        }


        return vo;
    }
}
