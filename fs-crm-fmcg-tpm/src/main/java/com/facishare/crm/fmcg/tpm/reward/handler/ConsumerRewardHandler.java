package com.facishare.crm.fmcg.tpm.reward.handler;

import com.facishare.crm.fmcg.common.adapter.abstraction.IFMCGTokenService;
import com.facishare.crm.fmcg.common.adapter.dto.token.GetWXOpenIdAndPhone;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.pojo.SnCode;
import com.facishare.crm.fmcg.common.utils.EncryptionService;
import com.facishare.crm.fmcg.common.utils.RandomUtil;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.api.scan.RecordTokenInfoDTO;
import com.facishare.crm.fmcg.tpm.business.abstraction.IRedPacketService;
import com.facishare.crm.fmcg.tpm.business.dto.GetRewardDetailDTO;
import com.facishare.crm.fmcg.tpm.business.dto.PayerInfoDTO;
import com.facishare.crm.fmcg.tpm.business.dto.ReceiverInfoDTO;
import com.facishare.crm.fmcg.tpm.business.enums.ConsumerRewardStatusEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityRewardRulePO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDetailEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDimensionEnum;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardMethodEnum;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardBase;
import com.facishare.crm.fmcg.tpm.reward.abstraction.ActivityRewardHandler;
import com.facishare.crm.fmcg.tpm.reward.dto.*;
import com.facishare.crm.fmcg.tpm.reward.service.AdvancedRewardLimitService;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NEnums;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.annotation.WeChatSecurityApi;
import com.facishare.crm.fmcg.tpm.web.service.UnlockOuterCodeService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fs.fmcg.sdk.ai.adapter.file.FileStoneAdapter;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Author: linmj
 * Date: 2024/3/27 19:28
 */
//IgnoreI18nFile
@Slf4j
@Service
public class ConsumerRewardHandler extends ActivityRewardBase implements ActivityRewardHandler<ConsumerReward.Arg, ConsumerReward.Result> {

    @Resource
    private EncryptionService encryptionService;
    @Resource
    private IFMCGTokenService fmcgTokenService;
    @Resource
    private UnlockOuterCodeService unlockOuterCodeService;

    @Resource
    private IRedPacketService redPacketService;

    @Resource
    private FileStoneAdapter fileStoneAdapter;

    @Resource
    private AdvancedRewardLimitService advancedRewardLimitService;

    public static final String CONSUMER = "消费者";

    public static final String CONSUMER_IDENTITY_TEMPLATE = "consumerIdentityTemplate:%s:%s";

    public static final ThreadLocal<SimpleDateFormat> SIMPLE_DATE_FORMAT_THREAD_LOCAL = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    public static final String BUSINESS_PREFIX = "consumer-scan-reward-";

    @Override
    @WeChatSecurityApi
    public ConsumerReward.Result handle(ConsumerReward.Arg arg) {
        SnCode code;
        try {
            if (arg.getCode().startsWith("NEW#")) {
                arg.setCode(arg.getCode().substring(4));
            }
            code = encryptionService.verify(arg.getCode());
        } catch (Exception ex) {
            throw new MetaDataBusinessException(I18N.text(I18NEnums.PLEASE_SCAN_CORRECT_QR_CODE.getCode()));
        }
        ConsumerReward.Result result;

        String tenantId = code.getManufacturerTenantId();
        String snId = code.getSnId();
        String storeId = code.getStoreId();
        String activityId = code.getActivityId();

        IObjectData snObj = serviceFacade.findObjectData(User.systemUser(tenantId), snId, ApiNames.FMCG_SERIAL_NUMBER_OBJ);

        if (TPMGrayUtils.enableUnlockStatusCheck(tenantId)) {
            if (SnCode.OUTER_TYPE.equals(code.getType()) && (unlockOuterCodeService.isOuterCodeLocked(tenantId, code.getSnId()) || unlockOuterCodeService.isUnlockStatusNotExists(tenantId, code.getSnId()))) {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_REWARD_HANDLER_0));
            }
        } else {
            if (SnCode.OUTER_TYPE.equals(code.getType()) && unlockOuterCodeService.isOuterCodeLocked(tenantId, code.getSnId())) {
                throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_CONSUMER_REWARD_HANDLER_1));
            }
        }

        IObjectData store = serviceFacade.findObjectData(User.systemUser(tenantId), storeId, ApiNames.ACCOUNT_OBJ);
        ReceiverInfoDTO consumerInfo = getConsumerInfo(tenantId, arg.getAppId(), arg.getToken(), arg.getOpenId(), arg.getUnionId(), store, snId);
        ActivityRewardRulePO rewardRulePO = activityRewardRuleDAO.getByRelatedObject(tenantId, ApiNames.TPM_ACTIVITY_OBJ, activityId);
        RewardDetailEntity consumerReward = rewardRulePO.getRewardDetails().stream().filter(detail -> RewardDimensionEnum.CONSUMER.code().equals(detail.getRewardNode().getRewardDimension())).findFirst().orElse(null);

        RLock uniqueLock = lockNow(String.format("consumerScan:%s:%s", tenantId, snId));
        try {
            if (uniqueLock != null && uniqueLock.isLocked()) {
                SnInformation snInformation = formSnInformation(tenantId, snObj, consumerReward);
                result = dealConsumerRewardInformation(tenantId, activityId, snObj, consumerReward, consumerInfo, snInformation);
            } else {
                throw new ValidateException(I18N.text(I18NEnums.SCANNING_BY_OTHERS.getCode()));
            }
        } finally {
            unlock(uniqueLock);
        }
        return result;
    }


    private boolean overIndividualRewardLimit(String tenantId, RewardDetailEntity consumerReward, String activityId, ReceiverInfoDTO consumerInfo) {
        if (consumerReward == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_CONSUMER_REWARD_HANDLER_0));
        }
        if (consumerReward.getRewardStrategy().getIndividualRewardLimit() != null) {
            int rewardCount = getActivityRewardDetailCount(tenantId, activityId, consumerInfo.getRewardPersonId(), 0);
            return rewardCount >= consumerReward.getRewardStrategy().getIndividualRewardLimit();
        }
        return false;
    }

    private ConsumerReward.Result dealConsumerRewardInformation(String tenantId, String activityId, IObjectData snObj, RewardDetailEntity consumerReward, ReceiverInfoDTO consumerInfo, SnInformation snInformation) {
        String businessId = BUSINESS_PREFIX + snObj.getId();
        IObjectData activityRewardDetail = findActivityRewardByUniqueId(tenantId, businessId);
        ConsumerReward.Result result = new ConsumerReward.Result();
        if (activityRewardDetail == null) {
            return reward(tenantId, activityId, snObj, consumerReward, consumerInfo, businessId);
        } else {
            String rewardPersonId = activityRewardDetail.get(TPMActivityRewardDetailFields.REWARD_PERSON_ID, String.class);
            String rewardType = activityRewardDetail.get(TPMActivityRewardDetailFields.REWARD_TYPE, String.class);
            if (!Strings.isNullOrEmpty(rewardPersonId) && (rewardPersonId.equals(consumerInfo.getRewardPersonId()) || rewardPersonId.equals(consumerInfo.getWxOpenId()) || rewardPersonId.equals(consumerInfo.getWxAppId() + "." + consumerInfo.getWxOpenId()))) {
                result.setStatus(ConsumerRewardStatusEnum.HAS_GOT_BY_MYSELF.code());
                if (TPMActivityRewardDetailFields.RewardType.RED_PACKET.equals(rewardType)) {
                    result.setRedPacketInformation(findRedPacketInformation(activityRewardDetail, consumerReward));
                } else if (TPMActivityRewardDetailFields.RewardType.PHYSICAL_ITEM.equals(rewardType)) {
                    result.setPhysicalRewardInformation(findPhysicalRewardInformation(activityRewardDetail, consumerReward, consumerInfo.getWxAppId()));
                }
                result.setSnInformation(snInformation);
            } else {
                result.setStatus(ConsumerRewardStatusEnum.HAS_GOT_BY_OTHER.code());
                result.setSnInformation(snInformation);
            }
            return result;
        }
    }


    public void dealOuterRewardInformation(String tenantId, String activityId, IObjectData snObj, SerialNumberData code, IObjectData store) {
        String businessId = BUSINESS_PREFIX + snObj.getId();
        IObjectData activityRewardDetail = findActivityRewardByUniqueId(tenantId, businessId);
        if (activityRewardDetail == null) {
            reward(tenantId, activityId, snObj, businessId, code, store);
        } else {
            log.info("Outer reward has reward, businessId:{}", businessId);
        }
    }

    private IObjectData findActivityRewardByUniqueId(String tenantId, String businessId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1,
                Lists.newArrayList(SearchQueryUtil.filter(TPMActivityRewardDetailFields.BUSINESS_ID, Operator.EQ, Lists.newArrayList(businessId)),
                        SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_PART, Operator.EQ, Lists.newArrayList(CONSUMER))));
        query.setNeedReturnCountNum(false);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query).getData();

        return dataList.isEmpty() ? null : dataList.get(0);
    }

    private void reward(String tenantId, String activityId, IObjectData snObj, String businessId, SerialNumberData code, IObjectData store) {
        List<GetRewardDetailDTO> allDetails = getRewardDetails(tenantId, activityId, snObj.getId(), businessId, store.getId());
        RLock lock = tryLock(String.format(ScanCodeActionConstants.CONSUMER_SCAN_LOCK_KEY, activityId));
        try {
            List<IObjectData> rewardDetails = new ArrayList<>();
            Map<IObjectData, List<IObjectData>> redPacketDetails = new HashMap<>();
            Map<String, List<IObjectData>> othersSaveObjs = new HashMap<>();
            //组装激励明细
            assembleRewardObject(allDetails, redPacketDetails, rewardDetails, othersSaveObjs);
            validateActivityAmount(tenantId, activityId, redPacketDetails.keySet());
            saveRewardData(tenantId, businessId, rewardDetails, redPacketDetails, null, othersSaveObjs, null);
        } finally {
            unlock(lock);
        }
    }


    private ConsumerReward.Result reward(String tenantId, String activityId, IObjectData snObj, RewardDetailEntity consumerDetail, ReceiverInfoDTO consumerInfo, String businessId) {
        List<GetRewardDetailDTO> allDetails = getRewardDetails(tenantId, activityId, snObj.getId(), businessId, consumerInfo.getStore().getId());
        RLock lock = tryLock(String.format(ScanCodeActionConstants.CONSUMER_SCAN_LOCK_KEY, activityId));
        if (advancedRewardLimitService.overlimit(tenantId, activityId, consumerInfo.getStore().get(AccountFields.CHANNEL, String.class), consumerInfo.getStore().getId(), snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class))) {
            allDetails.clear();
            log.info("受限不进行后续激励。tenantId:{},activityId:{},storeId:{}", tenantId, activityId, consumerInfo.getStore().getId());
        }
        IObjectData activityRewardDetail;
        ConsumerReward.Result result = null;
        try {
            result = rewardLimitValidate(tenantId, activityId, consumerDetail, consumerInfo);
            if (!Strings.isNullOrEmpty(result.getStatus())) {
                return result;
            }

            // 开启了首次激励必中最大设置且当前消费者是第一次中奖则 randomTopLevel = true
            if ((TPMGrayUtils.isFirstRewardTopLevel(tenantId, activityId) || TPMGrayUtils.isFirstRewardTopLevel(tenantId)) && isFirstReward(tenantId, activityId, consumerInfo)) {
                log.info("force random top level reward");
                consumerInfo.setRandomTopLevel(true);
            }

            GetRewardDetailDTO consumerRewardDetail = getConsumerRewardDetail(tenantId, businessId, activityId, snObj, consumerDetail, consumerInfo);
            if (consumerRewardDetail == null) {
                throw new RewardFmcgException("90021", I18N.text(I18NEnums.ACTIVITY_REWARD_HAS_SENT_ALL.getCode()));
            }
            activityRewardDetail = consumerRewardDetail.getActivityRewardDetails().get(0);
            allDetails.add(consumerRewardDetail);
            List<IObjectData> rewardDetails = new ArrayList<>();
            Map<IObjectData, List<IObjectData>> redPacketDetails = new HashMap<>();
            Map<String, List<IObjectData>> othersSaveObjs = new HashMap<>();
            //组装激励明细
            assembleRewardObject(allDetails, redPacketDetails, rewardDetails, othersSaveObjs);
            validateActivityAmount(tenantId, activityId, redPacketDetails.keySet());
            saveRewardData(tenantId, businessId, rewardDetails, redPacketDetails, consumerRewardDetail.getPhysicalItem(), othersSaveObjs, null);
            if (TPMActivityRewardDetailFields.RewardType.RED_PACKET.equals(activityRewardDetail.get(TPMActivityRewardDetailFields.REWARD_TYPE, String.class))) {
                result.setRedPacketInformation(findRedPacketInformation(activityRewardDetail, consumerDetail));
            } else {
                result.setPhysicalRewardInformation(findPhysicalRewardInformation(activityRewardDetail, consumerDetail, consumerInfo.getWxAppId()));
            }
        } finally {
            unlock(lock);
        }
        result.setStatus(ConsumerRewardStatusEnum.SUCCESS.code());
        result.setSnInformation(formSnInformation(tenantId, snObj, consumerDetail));
        return result;
    }

    private void assembleRewardObject(List<GetRewardDetailDTO> allDetails, Map<IObjectData, List<IObjectData>> redPacketDetails, List<IObjectData> rewardDetails, Map<String, List<IObjectData>> othersSaveObjs) {
        if (CollectionUtils.isNotEmpty(allDetails)) {
            allDetails.forEach(detail -> {
                if (CollectionUtils.isNotEmpty(detail.getActivityRewardDetails())) {
                    rewardDetails.addAll(detail.getActivityRewardDetails());
                }
                if (Objects.nonNull(detail.getRedPacket())) {
                    redPacketDetails.put(detail.getRedPacket(), detail.getRedPacketDetails());
                }
                if (Objects.isNull(detail.getRebate())) {
                    List<IObjectData> details = othersSaveObjs.getOrDefault(ApiNames.REBATE_OBJ, Lists.newArrayList());
                    details.add(detail.getRebate());
                    othersSaveObjs.putIfAbsent(ApiNames.REBATE_OBJ, details);
                }
            });
        }
    }

    private boolean isFirstReward(String tenantId, String activityId, ReceiverInfoDTO consumerInfo) {
        return getActivityRewardDetailCount(tenantId, activityId, consumerInfo.getRewardPersonId(), 0) <= 0;
    }

    private ConsumerReward.Result rewardLimitValidate(String tenantId, String activityId, RewardDetailEntity consumerDetail, ReceiverInfoDTO consumerInfo) {
        ConsumerReward.Result result = new ConsumerReward.Result();
        if (overDailyRewardLimit(tenantId, consumerDetail, activityId, consumerInfo)) {
            result.setStatus(ConsumerRewardStatusEnum.DAILY_OVERLOAD.code());
            result.setPhysicalRewardInformation(PhysicalRewardInformation.builder().limit(consumerDetail.getRewardStrategy().getDailyRewardLimit()).build());
            result.setRedPacketInformation(RedPacketInformation.builder().limit(consumerDetail.getRewardStrategy().getDailyRewardLimit()).build());
            log.info("个人每日领取上限。{}", result);
            return result;
        }
        if (overIndividualRewardLimit(tenantId, consumerDetail, activityId, consumerInfo)) {
            result.setStatus(ConsumerRewardStatusEnum.ACCUMULATED_OVERLOAD.code());
            result.setRedPacketInformation(RedPacketInformation.builder().limit(consumerDetail.getRewardStrategy().getIndividualRewardLimit()).build());
            result.setPhysicalRewardInformation(PhysicalRewardInformation.builder().limit(consumerDetail.getRewardStrategy().getIndividualRewardLimit()).build());
            log.info("个人累计领取上限。{}", result);
            return result;
        }
        return result;
    }

    private RedPacketInformation findRedPacketInformation(IObjectData activityRewardDetail, RewardDetailEntity rewardDetail) {
        return RedPacketInformation.builder()
                .amount(activityRewardDetail.get(TPMActivityRewardDetailFields.REWARD_VALUE, BigDecimal.class))
                .date(SIMPLE_DATE_FORMAT_THREAD_LOCAL.get().format(new Date(activityRewardDetail.get(CommonFields.CREATE_TIME, Long.class))))
                .limit(rewardDetail.getRewardStrategy().getDailyRewardLimit() == null ? 9999 : rewardDetail.getRewardStrategy().getDailyRewardLimit())
                .build();
    }

    private PhysicalRewardInformation findPhysicalRewardInformation(IObjectData activityRewardDetail, RewardDetailEntity rewardDetail, String appId) {
        User user = User.systemUser(activityRewardDetail.getTenantId());
        IObjectData relatedObjectData = serviceFacade.findObjectData(user, activityRewardDetail.get(TPMActivityRewardDetailFields.RELATED_OBJECT_DATA_ID, String.class), activityRewardDetail.get(TPMActivityRewardDetailFields.RELATED_OBJECT_API_NAME, String.class));
        int type = 0;
        String goodsId = relatedObjectData.get(PointsExchangeRecordFields.PRODUCT_ID, String.class);
        String goodType = relatedObjectData.get(PointsExchangeRecordFields.GOODS_TYPE, String.class);
        if (ScanCodeActionConstants.THANKS_YOU_GOODS_ID.equals(goodsId) || PointsGoodsFields.CommodityType.TANK_YOU_PATRONIZE.equals(goodType)) {
            type = 2;
        } else if (PointsGoodsFields.CommodityType.RED_PACKET.equals(goodType)) {
            type = 1;
        }
        List<Map> imageNpath = relatedObjectData.get(PointsExchangeRecordFields.PRODUCT_MAIN_GRAPH, List.class);
        String imageUrl = CollectionUtils.isEmpty(imageNpath) ? null : fileStoneAdapter.createShareFile(serviceFacade.getEAByEI(activityRewardDetail.getTenantId()), 1000, Lists.newArrayList(imageNpath.get(0).get("path").toString())).get(0).getValue();
        IObjectData prizeDetail = serviceFacade.findObjectData(user, activityRewardDetail.get(TPMActivityRewardDetailFields.PRIZE_NAME, String.class), ApiNames.TPM_ACTIVITY_PRIZES_OBJ);
        return PhysicalRewardInformation.builder()
                .rewardGetMethod(rewardDetail.getRewardStrategy().getRewardGetMethod())
                .physicalItemId(prizeDetail.get(TPMActivityPrizesFields.PRIZE_NAME, String.class))
                .rewardDetailId(activityRewardDetail.getId())
                .name(prizeDetail.get(TPMActivityPrizesFields.DISPLAY_NAME, String.class, "未知奖品"))
                .date(SIMPLE_DATE_FORMAT_THREAD_LOCAL.get().format(new Date(activityRewardDetail.get(CommonFields.CREATE_TIME, Long.class))))
                .limit(rewardDetail.getRewardStrategy().getDailyRewardLimit() == null ? 9999 : rewardDetail.getRewardStrategy().getDailyRewardLimit())
                .recordToken(formRecordToken(relatedObjectData.getTenantId(), relatedObjectData.getId(), appId))
                .amount(relatedObjectData.get(PointsExchangeRecordFields.GOODS_VALUE, BigDecimal.class))
                .type(type)
                .goodsId(relatedObjectData.get(PointsExchangeRecordFields.PRODUCT_ID, String.class))
                .imageUrl(imageUrl)
                .build();
    }

    public String formRecordToken(String tenantId, String id, String appId) {
        RecordTokenInfoDTO recordTokenInfoDTO = new RecordTokenInfoDTO();
        recordTokenInfoDTO.setRecordId(id);
        recordTokenInfoDTO.setTenantId(tenantId);
        recordTokenInfoDTO.setTenantCode(redPacketService.getTenantCode(tenantId));
        recordTokenInfoDTO.setAppId(appId);
        return encryptionService.sign(recordTokenInfoDTO);
    }


    private SnInformation formSnInformation(String tenantId, IObjectData snObj, RewardDetailEntity rewardDetail) {
        IObjectData skuObj = serviceFacade.findObjectData(User.systemUser(tenantId), snObj.get(FMCGSerialNumberFields.PRODUCT_ID, String.class), ApiNames.PRODUCT_OBJ);
        String manufactureDate = "--";
        Long manufactureDateTime = snObj.get(FMCGSerialNumberFields.MANUFACTURE_DATE, Long.class);
        if (Objects.nonNull(manufactureDateTime)) {
            manufactureDate = SIMPLE_DATE_FORMAT_THREAD_LOCAL.get().format(new Date(manufactureDateTime));
        }
        return SnInformation.builder()
                .productName(skuObj.getName())
                .productCode(skuObj.get(ProductFields.PRODUCT_CODE, String.class))
                .date(manufactureDate)
                .rewardMethod(rewardDetail.getRewardStrategy().getRewardMethod())
                .sold(true)
                .build();
    }

    private IObjectData findPacketByUniqueId(String tenantId, String activityId, String snId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1,
                Lists.newArrayList(SearchQueryUtil.filter(RedPacketRecordObjFields.RECORD_IDENTITY, Operator.EQ, Lists.newArrayList(consumerIdentity(activityId, snId)))));
        query.setNeedReturnCountNum(false);
        List<IObjectData> dataList = serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.RED_PACKET_RECORD_OBJ, query).getData();

        return dataList.isEmpty() ? null : dataList.get(0);
    }

    private String consumerIdentity(String activityId, String snId) {
        return String.format(CONSUMER_IDENTITY_TEMPLATE, activityId, snId);
    }

    private String consumerPhysicalRedPacketIdentity(String activityId, String snId) {
        return String.format("consumerIdentityPhysicalRedPacketTemplate:%s:%s", activityId, snId);
    }

    private boolean overDailyRewardLimit(String tenantId, RewardDetailEntity consumerReward, String activityId, ReceiverInfoDTO consumerInfo) {
        if (consumerReward == null) {
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_CONSUMER_REWARD_HANDLER_1));
        }
        if (consumerReward.getRewardStrategy().getDailyRewardLimit() != null) {
            int dailyRewardCount = getActivityRewardDetailCount(tenantId, activityId, consumerInfo.getRewardPersonId(), 1);
            return dailyRewardCount >= consumerReward.getRewardStrategy().getDailyRewardLimit();
        }
        return false;
    }

    private ReceiverInfoDTO getConsumerInfo(String tenantId, String appId, String token, String openId, String unionId, IObjectData store, String snCodeId) {
        ReceiverInfoDTO receiverInfoDTO = new ReceiverInfoDTO();
        IObjectData storeSignCodeStatusObj = fmcgSerialNumberService.getStoreSignSerialNumberStatusObj(tenantId, snCodeId, true);
        String currentTenantId = storeSignCodeStatusObj.get(FMCGSerialNumberStatusFields.CURRENT_TENANT_ID, String.class);
        receiverInfoDTO.setTenantId(currentTenantId);
        receiverInfoDTO.setTenantName(getTenantName(currentTenantId));
        receiverInfoDTO.setStore(store);
        GetWXOpenIdAndPhone.Arg openIdAg = new GetWXOpenIdAndPhone.Arg();
        if (Strings.isNullOrEmpty(openId)) {
            openIdAg.setOpenIdToken(token);
            openIdAg.setAppId(appId);
            openIdAg.setTenantId(tenantId);
            GetWXOpenIdAndPhone.Result tokenResult = fmcgTokenService.getWXOpenIdAndPhone(openIdAg);
            receiverInfoDTO.setUnionId(tokenResult.getUnionId());
            receiverInfoDTO.setPhone(tokenResult.getPhone());
            receiverInfoDTO.setWxOpenId(tokenResult.getOpenId());
        } else {
            receiverInfoDTO.setUnionId(unionId);
            receiverInfoDTO.setPhone(null);
            receiverInfoDTO.setWxOpenId(openId);
        }
        receiverInfoDTO.setWxAppId(appId);
        receiverInfoDTO.setName(CONSUMER);
        receiverInfoDTO.setRewardRole(CONSUMER);
        receiverInfoDTO.setRewardMethod(RewardMethodEnum.RED_PACKET.code());
        receiverInfoDTO.setRewardPerson(CONSUMER);
        receiverInfoDTO.setRewardPersonId(receiverInfoDTO.getUnionId());
        receiverInfoDTO.setType(RedPacketRecordObjFields.TransfereeAccountType.WECHAT);
        return receiverInfoDTO;
    }

    // 0 不限制  1 每日
    private int getActivityRewardDetailCount(String tenantId, String activityId, String userId, int type) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1,
                Lists.newArrayList(SearchQueryUtil.filter(TPMActivityRewardDetailFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                        SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_PERSON_ID, Operator.EQ, Lists.newArrayList(userId))
                )
        );
        if (type == 1) {
            LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(System.currentTimeMillis()), ZoneId.systemDefault()).withHour(0).withMinute(0).withSecond(0).withNano(0);
            long startTime = time.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            long endTIme = time.plusDays(1).toInstant(ZoneOffset.of("+8")).toEpochMilli() - 1;
            query.getFilters().add(SearchQueryUtil.filter(TPMActivityRewardDetailFields.REWARD_TIME, Operator.BETWEEN, Lists.newArrayList(String.valueOf(startTime), String.valueOf(endTIme))));
        }
        query.setSearchSource("db");
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(query);
        queryExt.onlyQueryTotalNumIgnorePermission();
        return serviceFacade.findBySearchQuery(User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, queryExt.toSearchTemplateQuery()).getTotalNumber();
    }

    private GetRewardDetailDTO getConsumerRewardDetail(String tenantId, String businessId, String activityId, IObjectData snObj, RewardDetailEntity rewardDetail, ReceiverInfoDTO receiverInfoDTO) {
        GetRewardDetailDTO getRewardDetailDTO = new GetRewardDetailDTO();
        String productId = snObj.get(FMCGSerialNumberStatusFields.PRODUCT_ID, String.class);
        if (rewardDetail.getRewardStrategy().getRewardMethod().equals(RewardMethodEnum.PHYSICAL_ITEM.code())) {
            IObjectData prizeDetail = getPrizeDetail(tenantId, activityId);
            if (Objects.isNull(prizeDetail)) {
                /*getRewardDetailDTO.setPhysicalItem(formRewardDetail(tenantId, businessId, TPMActivityRewardDetailFields.RewardType.NONE, new BigDecimal(1),
                        activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson(), snObj.getId(), productId, null, null, receiverInfoDTO.getRewardPersonId(), null));
                return getRewardDetailDTO;*/
                return null;
            }
            IObjectData physicalItemObj = formPhysicalItem(tenantId, businessId, activityId, receiverInfoDTO, snObj.getId(), prizeDetail, rewardDetail.getRewardStrategy().getRewardGetMethod());
            String status = PointsExchangeRecordFields.OrderState.DELIVERED.equals(physicalItemObj.get(PointsExchangeRecordFields.ORDER_STATE, String.class)) ? TPMActivityRewardDetailFields.Status.DONE : TPMActivityRewardDetailFields.Status.UNDO;
            IObjectData activityRewardDetailObj = formRewardDetail(tenantId, businessId, TPMActivityRewardDetailFields.RewardType.PHYSICAL_ITEM, new BigDecimal(1),
                    activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson(), receiverInfoDTO.getUnionId(), snObj.getId(), productId, physicalItemObj.getTenantId(), physicalItemObj.getDescribeApiName(), physicalItemObj.getId(), receiverInfoDTO.getRewardPersonId(), prizeDetail.getId(), status, null, receiverInfoDTO.getStore().getId(),null);
            getRewardDetailDTO.setActivityRewardDetails(Lists.newArrayList(activityRewardDetailObj));
            getRewardDetailDTO.setPhysicalItem(physicalItemObj);

            //红包得同步生成红包记录
            String goodsType = prizeDetail.get(TPMActivityPrizesFields.PRIZE_TYPE, String.class);
            if (PointsGoodsFields.CommodityType.RED_PACKET.equals(goodsType)) {
                PayerInfoDTO redPacketPayerInfoDTO = getPayerInfo(tenantId, rewardDetail, null);
                IObjectData redPacketObj = formRedPacket(tenantId, businessId, redPacketPayerInfoDTO, receiverInfoDTO, physicalItemObj, activityId, rewardDetail.getDetailId(), null, PHYSICAL_RED_PACKET_TRIGGER_ID, prizeDetail.get(TPMActivityPrizesFields.ACTIVITY_DESCRIBE, String.class, "活动红包奖励"), consumerPhysicalRedPacketIdentity(activityId, snObj.getId()));
                getRewardDetailDTO.setRedPacket(redPacketObj);
                getRewardDetailDTO.setRedPacketDetails(Lists.newArrayList(formRedPacketDetail(tenantId, productId, prizeDetail.get(TPMActivityPrizesFields.PRIZE_AMOUNT, BigDecimal.class), snObj.getId(), businessId)));
            }
        } else {
            PayerInfoDTO payerInfoDTO = getPayerInfo(tenantId, rewardDetail, null);
            receiverInfoDTO.setRewardMethodType(rewardDetail.getRewardStrategy().getRewardMethodType());
            fillReceiverAmount(tenantId, receiverInfoDTO, rewardDetail);
            snObj.setTenantId(receiverInfoDTO.getTenantId());
            IObjectData redPacketObj = formRedPacket(tenantId, businessId, payerInfoDTO, receiverInfoDTO, snObj, activityId,
                    rewardDetail.getDetailId(), null, rewardDetail.getRewardNode().getRewardAction(), rewardDetail.getRewardStrategy().getRewardRemark(), consumerIdentity(activityId, snObj.getId())
            );
            snObj.setTenantId(tenantId);
            IObjectData redPacketDetailObj = formRedPacketDetail(tenantId, productId, receiverInfoDTO.getAmount(), snObj.getId(), businessId);
            IObjectData activityRewardDetailObj = formRewardDetail(tenantId, businessId, TPMActivityRewardDetailFields.RewardType.RED_PACKET, receiverInfoDTO.getAmount(),
                    activityId, receiverInfoDTO.getRewardRole(), receiverInfoDTO.getRewardPerson(), receiverInfoDTO.getUnionId(), snObj.getId(), productId, redPacketDetailObj.getTenantId(), redPacketDetailObj.getDescribeApiName(), redPacketDetailObj.getId(), receiverInfoDTO.getRewardPersonId(), null, TPMActivityRewardDetailFields.Status.UNDO, null, receiverInfoDTO.getStore().getId(),null);
            getRewardDetailDTO.setRedPacket(redPacketObj);
            getRewardDetailDTO.setRedPacketDetails(Lists.newArrayList(redPacketDetailObj));
            getRewardDetailDTO.setActivityRewardDetails(Lists.newArrayList(activityRewardDetailObj));
        }

        return getRewardDetailDTO;
    }

    private IObjectData getPrizeDetail(String tenantId, String activityId) {

        List<IObjectData> prizeDetails = getPrizeDetailsByActivityId(tenantId, activityId);
        IObjectData prize = getRandomPrizeDetail(prizeDetails);
        if (prize == null) {
            return null;
        }
        return prize;
    }

    private IObjectData getRandomPrizeDetail(List<IObjectData> prizeDetails) {
        if (CollectionUtils.isEmpty(prizeDetails)) {
            return null;
        }
        int maxPrizeCount = prizeDetails.stream().map(prize -> prize.get(TPMActivityPrizesFields.REMAINING_DISTRIBUTABLE_QUANTITY, BigDecimal.class, BigDecimal.ZERO)).reduce(BigDecimal.ZERO, BigDecimal::add).intValue();
        int random = RandomUtil.nextInt(maxPrizeCount) + 1;
        int current = 0;
        for (IObjectData prizeDetail : prizeDetails) {
            current += prizeDetail.get(TPMActivityPrizesFields.REMAINING_DISTRIBUTABLE_QUANTITY, BigDecimal.class, BigDecimal.ZERO).intValue();
            if (current >= random) {
                int count = hasDistributeCount(prizeDetail.getTenantId(), prizeDetail.getId());
                int total = prizeDetail.get(TPMActivityPrizesFields.PRIZE_TOTAL, BigDecimal.class, BigDecimal.ZERO).intValue();
                if (count < total) {
                    prizeDetail.set(TPMActivityPrizesFields.REMAINING_DISTRIBUTABLE_QUANTITY, total - count);
                    return prizeDetail;
                } else {
                    prizeDetail.set(TPMActivityPrizesFields.REMAINING_DISTRIBUTABLE_QUANTITY, BigDecimal.ZERO);
                }
                return getRandomPrizeDetail(prizeDetails.stream().filter(v -> v != prizeDetail).collect(Collectors.toList()));
            }
        }
        return null;
    }

    private int hasDistributeCount(String tenantId, String id) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityRewardDetailFields.PRIZE_NAME, Operator.EQ, Lists.newArrayList(id)),
                SearchQueryUtil.filter(CommonFields.IS_DELETED, Operator.EQ, Lists.newArrayList("0"))
        ));
        return serviceFacade.countObjectDataFromDB(tenantId, ApiNames.TPM_ACTIVITY_REWARD_DETAIL_OBJ, query);
    }


    private List<IObjectData> getPrizeDetailsByActivityId(String tenantId, String activityId) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityPrizesFields.ACTIVITY_ID, Operator.EQ, Lists.newArrayList(activityId)),
                SearchQueryUtil.filter(TPMActivityPrizesFields.REMAINING_DISTRIBUTABLE_QUANTITY, Operator.GT, Lists.newArrayList("0"))
        ));

        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), ApiNames.TPM_ACTIVITY_PRIZES_OBJ, query);
    }

    private IObjectData formPhysicalItem(String tenantId, String businessId, String activityId, ReceiverInfoDTO receiverInfoDTO, String snId, IObjectData prizeDetail, List<String> rewardGetType) {
        IObjectData exchangeRecordObj = new ObjectData();
        exchangeRecordObj.setTenantId(tenantId);
        exchangeRecordObj.setId(IdGenerator.get());
        exchangeRecordObj.setOwner(Lists.newArrayList("-10000"));
        exchangeRecordObj.setDescribeApiName(ApiNames.POINTS_EXCHANGE_RECORD_OBJ);
        exchangeRecordObj.setRecordType(PointsExchangeRecordFields.RecordType.PHYSICAL_TYPE);
        exchangeRecordObj.set(PointsExchangeRecordFields.BUSINESS_ID, businessId);
        exchangeRecordObj.set(PointsExchangeRecordFields.ACTIVITY_ID, activityId);
        exchangeRecordObj.set(PointsExchangeRecordFields.ORDER_TIME, System.currentTimeMillis());
        String goodsId = prizeDetail.get(TPMActivityPrizesFields.PRIZE_NAME, String.class);
        String goodsType = prizeDetail.get(TPMActivityPrizesFields.PRIZE_TYPE, String.class);
        exchangeRecordObj.set(PointsExchangeRecordFields.ORDER_TYPE, PointsExchangeRecordFields.OrderType.ACTIVITY_PHYSICAL_ORDER);
        exchangeRecordObj.set(PointsExchangeRecordFields.PRODUCT_ID, goodsId);
        exchangeRecordObj.set(PointsExchangeRecordFields.GOODS_NUMBER, 1);

        if (goodsId.equals(ScanCodeActionConstants.THANKS_YOU_GOODS_ID) || PointsGoodsFields.CommodityType.TANK_YOU_PATRONIZE.equals(goodsType)
                || PointsGoodsFields.CommodityType.RED_PACKET.equals(goodsType)) {
            receiverInfoDTO.setAmount(prizeDetail.get(TPMActivityPrizesFields.PRIZE_AMOUNT, BigDecimal.class));
            exchangeRecordObj.set(PointsExchangeRecordFields.ORDER_STATE, PointsExchangeRecordFields.OrderState.DELIVERED);
            exchangeRecordObj.set(PointsExchangeRecordFields.DISTRIBUTION_TIME, System.currentTimeMillis());
/*            if (rewardGetType.contains(RewardGetMethodEnum.PICK_UP_AT_STORE.code())) {
                exchangeRecordObj.set(PointsExchangeRecordFields.PICKUP_METHOD, PointsExchangeRecordFields.PickUpMethod.PUCK_UP_AT_STORE);
            } else {
                exchangeRecordObj.set(PointsExchangeRecordFields.PICKUP_METHOD, PointsExchangeRecordFields.PickUpMethod.MAIL);
            }*/
        } else {
            exchangeRecordObj.set(PointsExchangeRecordFields.ORDER_STATE, PointsExchangeRecordFields.OrderState.UNDELIVERED);
        }
        exchangeRecordObj.set(PointsExchangeRecordFields.GOODS_TYPE, goodsType);
        if (PointsGoodsFields.CommodityType.ONE_MORE_GOODS.equals(goodsType)) {
            List<String> productIds = prizeDetail.get(TPMActivityPrizesFields.PRIZE_PRODUCT_IDS, List.class, Lists.newArrayList());
            exchangeRecordObj.set(PointsExchangeRecordFields.PRIZE_PRODUCT_ID, productIds.get(RandomUtil.nextInt(productIds.size())));
        }
        exchangeRecordObj.set(PointsExchangeRecordFields.GOODS_VALUE, prizeDetail.get(TPMActivityPrizesFields.PRIZE_AMOUNT, BigDecimal.class));
        exchangeRecordObj.set(PointsExchangeRecordFields.PRIZE_AMOUNT_VERIFIED_BY_STORE, prizeDetail.get(TPMActivityPrizesFields.PRIZE_AMOUNT_VERIFIED_BY_STORE));
        exchangeRecordObj.set(PointsExchangeRecordFields.REWARD_ROLE, receiverInfoDTO.getRewardRole());
        exchangeRecordObj.set(PointsExchangeRecordFields.CONSUMER_OPEN_ID, receiverInfoDTO.getRewardPersonId());
        exchangeRecordObj.set(PointsExchangeRecordFields.ACTIVITY_ID, activityId);
        exchangeRecordObj.set(PointsExchangeRecordFields.PRODUCT_BARCODE, snId);
        exchangeRecordObj.set(PointsExchangeRecordFields.SALES_STORE, receiverInfoDTO.getStore().getId());
        exchangeRecordObj.set(PointsExchangeRecordFields.STORE_CODE, receiverInfoDTO.getStore().get(AccountFields.ACCOUNT_NO, String.class));
        String dealerId = enterpriseConnectionService.getStoreId(tenantId, receiverInfoDTO.getTenantId());
        if (!Strings.isNullOrEmpty(dealerId)) {
            IObjectData dealer = serviceFacade.findObjectData(User.systemUser(tenantId), dealerId, ApiNames.ACCOUNT_OBJ);
            exchangeRecordObj.set(PointsExchangeRecordFields.DEALER_ID, dealer.getId());
            exchangeRecordObj.set(PointsExchangeRecordFields.STORE_SALES_DEALER_DEPARTMENT, dealer.getDataOwnDepartment());
            exchangeRecordObj.set(PointsExchangeRecordFields.STORE_SALES_AREA, dealer.get("sales_area__c"));
        } else {
            log.info("dealerId is null or empty.upperTenantId:{},downloadTenantId:{}", receiverInfoDTO.getTenantId(), receiverInfoDTO.getTenantId());
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_CONSUMER_REWARD_HANDLER_2));
        }

        return exchangeRecordObj;
    }


    RLock lockNow(String key) {
        RLock uniqueLock = redissonCmd.getLock(key);
        try {
            if (uniqueLock.tryLock(4000, 30000, TimeUnit.MILLISECONDS)) {
                log.info("lock success.key:{},isLock:{}", key, uniqueLock.isLocked());
                return uniqueLock;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ValidateException(I18N.text(I18NKeys.VALIDATE_CONSUMER_REWARD_HANDLER_3));
        }
        return null;
    }
}
