package com.facishare.crm.fmcg.tpm.api.activity;

import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/9/1 10:57
 */
public interface TPMActivityScript {

    @Data
    @ToString
    class Arg implements Serializable {
        private List<String> tenantIds;
        private String module;
        private JSONObject params;
    }

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class Result implements Serializable {
        private JSONObject result;
    }
}
