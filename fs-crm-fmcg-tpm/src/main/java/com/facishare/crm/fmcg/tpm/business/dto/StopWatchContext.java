package com.facishare.crm.fmcg.tpm.business.dto;

import com.facishare.paas.appframework.common.util.StopWatch;
import com.github.trace.TraceContext;

/**
 * <AUTHOR>
 * @date 2022/8/19 下午7:30
 */
public class StopWatchContext {

    public static ThreadLocal<StopWatch> stopWatchThreadLocal = new ThreadLocal<>();

    public static StopWatch get() {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        if (stopWatch == null) {
            stopWatch = StopWatch.create("stop:" + TraceContext.get().getMethod());
            stopWatchThreadLocal.set(stopWatch);
        }
        return stopWatch;
    }

    public static StopWatch get(String name) {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        if (stopWatch == null) {
            stopWatch = StopWatch.create("stop:" + name);
            stopWatchThreadLocal.set(stopWatch);
        }
        return stopWatch;
    }

    public static void destroy() {
        StopWatch stopWatch = stopWatchThreadLocal.get();
        if (stopWatch != null) {
            stopWatch.lap("destroy");
            stopWatch.log();
        }
        stopWatchThreadLocal.remove();
    }
}
