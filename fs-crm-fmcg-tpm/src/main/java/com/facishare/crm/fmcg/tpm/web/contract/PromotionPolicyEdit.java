package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2023/7/5 20:01
 */
public interface PromotionPolicyEdit {

    @Data
    @ToString
    class Arg implements Serializable {

        @J<PERSON>NField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;

        @JSONField(name = "object_id")
        @JsonProperty(value = "object_id")
        @SerializedName("object_id")
        private String objectId;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private Boolean enable;
        private String msg;

    }
}