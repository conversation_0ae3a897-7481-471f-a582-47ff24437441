package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.TPMBudgetAccountFields;
import com.facishare.crm.fmcg.tpm.business.BudgetOperatorFactory;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetAccountDetailService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetStatisticTableService;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/6 下午5:23
 */
public class TPMBudgetAccountObjFlowCompletedAction extends StandardFlowCompletedAction {
    public static final Logger log = LoggerFactory.getLogger(TPMBudgetAccountObjFlowCompletedAction.class);

    private final TransactionProxy transactionProxy = SpringUtil.getContext().getBean(TransactionProxy.class);
    private final Set<String> APPROVAL_TRIGGER_TYPE = Sets.newHashSet("1", "2");
    private final IBudgetAccountDetailService budgetAccountDetailService = SpringUtil.getContext().getBean(IBudgetAccountDetailService.class);
    private final IBudgetStatisticTableService budgetStatisticTableService = SpringUtil.getContext().getBean(IBudgetStatisticTableService.class);

    private IBudgetOperator budgetOperator;
    private User systemUser;
    private String approvalId;
    private String businessId;
    private String action;
    private JSONObject callBackMap;

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }

    @Override
    protected void init() {
        super.init();
        this.systemUser = User.systemUser(actionContext.getTenantId());
        this.approvalId = TraceUtil.getApprovalCallbackTraceId(arg.getCallbackData());
        this.businessId = TraceUtil.getBusinessCallbackTraceId(arg.getCallbackData());
        this.callBackMap = arg.getCallbackData() == null ? new JSONObject() : JSON.parseObject(JSON.toJSONString(arg.getCallbackData()));
        this.action = this.callBackMap.getString("action");
    }

    @Override
    protected Result doAct(Arg arg) {
        return transactionProxy.call(() -> {
            Result result = super.doAct(arg);
            if (APPROVAL_TRIGGER_TYPE.contains(arg.getTriggerType())) {
                if (ObjectAction.ENABLE_BUDGET.getActionCode().equals(this.action)) {
                    if ("pass".equals(arg.getStatus())) {
                        if (CollectionUtils.isEmpty(budgetAccountDetailService.queryByBusinessType(systemUser, this.dbData.getId(), BizType.INIT_MONEY))) {
                            this.budgetOperator = BudgetOperatorFactory.initOperator(BizType.INIT_MONEY, this.systemUser, this.dbData.getId(), this.businessId, this.approvalId, this.dbData, false, true);
                            this.budgetOperator.setWhat(this.dbData);
                            this.budgetOperator.income(this.data.get(TPMBudgetAccountFields.BASE_AMOUNT, BigDecimal.class));
                            this.budgetOperator.recalculate();
                            stopWatch.lap("enable_budget");
                        }
                        String enableStatus = this.dbData.get(TPMBudgetAccountFields.BUDGET_STATUS, String.class);

                        if (!TPMBudgetAccountFields.BUDGET_STATUS__ENABLE.equals(enableStatus)) {
                            Map<String, Object> updateMap = new HashMap<>();
                            updateMap.put(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__ENABLE);
                            serviceFacade.updateWithMap(this.systemUser, this.dbData, updateMap);
                        }
                    } else {
                        Map<String, Object> updateMap = new HashMap<>();
                        updateMap.put(TPMBudgetAccountFields.BUDGET_STATUS, TPMBudgetAccountFields.BUDGET_STATUS__DISABLE);
                        serviceFacade.updateWithMap(this.systemUser, this.dbData, updateMap);
                    }
                }
            }
            return result;
        });
    }


    @Override
    protected Result after(Arg arg, Result result) {
        log.info("init TPMBudgetAccountObjFlowCompletedAction after");
        Result inner = super.after(arg, result);
        IObjectData budgetAccount = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getDataId(), ApiNames.TPM_BUDGET_ACCOUNT);
        if (Objects.nonNull(budgetAccount)) {
            asyncSaveBudgetStatisticTable(budgetAccount);
        }
        log.info("end TPMBudgetAccountObjFlowCompletedAction after");
        return inner;
    }

    private void asyncSaveBudgetStatisticTable(IObjectData budgetAccountData) {
        String lifeStatus = budgetAccountData.get(CommonFields.LIFE_STATUS, String.class);
        if (CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus)) {
            budgetStatisticTableService.asyncDoStatistic(actionContext.getTenantId(), budgetAccountData);
        }
    }
}