package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RewardTagConfigHandler implements IConfigHandler {

    @Override
    public void validation(String tenantId, ConfigVO vo) {
        if(!(vo.getValue() instanceof List)){
            throw new ValidateException("value must be list");
        }
    }

    @Override
    public void after(String tenantId, ConfigVO vo) {

    }
}
