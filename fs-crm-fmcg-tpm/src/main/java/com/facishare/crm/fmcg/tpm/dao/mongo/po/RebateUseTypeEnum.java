package com.facishare.crm.fmcg.tpm.dao.mongo.po;


import com.facishare.paas.I18N;
import com.google.common.base.Strings;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum RebateUseTypeEnum {

    DISCOUNT("Discount", "分摊折价", RewardMethodTypeEnum.RETURN_BY_MONEY.code(), "fmcg.rebate_use_type.discount"),
    CASH("Cash", "冲抵回款", RewardMethodTypeEnum.RETURN_BY_MONEY.code(),"fmcg.rebate_use_type.cash")/*,
    QUANTITY("Quantity", "按数量返",RewardMethodTypeEnum.RETURN_BY_GOODS.code(), "fmcg.rebate_use_type.quantity"),
    AMOUNT("Amount", "按金额返", RewardMethodTypeEnum.RETURN_BY_GOODS.code(),"fmcg.rebate_use_type.amount")*/;

    RebateUseTypeEnum(String code, String describe,String rebateType, String i18nKey) {
        this.code = code;
        this.describe = describe;
        this.i18nKey = i18nKey;
        this.rebateType = rebateType;
    }

    private String code;

    private String describe;

    private String rebateType;

    private String i18nKey;

    private static final Map<String, RebateUseTypeEnum> CODE_MAP = Stream.of(values()).collect(Collectors.toMap(RebateUseTypeEnum::code, rebateUseTypeEnum -> rebateUseTypeEnum));

    public String code() {
        return this.code;
    }

    public String describe() {
        return this.describe;
    }


    public String rebateType() {
        return this.rebateType;
    }

    public String i18nDescribe() {
        String text;
        if (this.i18nKey == null) {
            text = this.describe;
        } else {
            text = I18N.text(this.i18nKey);
            if (Strings.isNullOrEmpty(text)) {
                text = this.describe;
            }
        }
        return text;
    }
}
