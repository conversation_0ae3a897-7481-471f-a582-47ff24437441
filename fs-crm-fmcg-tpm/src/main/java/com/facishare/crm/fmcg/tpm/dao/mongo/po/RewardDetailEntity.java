package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2023/9/13 19:43
 */

@Data
@EqualsAndHashCode
@ToString
public class RewardDetailEntity implements Serializable {

    public static final String F_DETAIL_ID = "detail_id";

    public static final String F_REWARD_NODE = "reward_node";

    public static final String F_REWARD_STRATEGY = "reward_strategy";

    public static final String F_REWARD_PAYMENT = "reward_payment";

    public static final String F_OUTER_CONSUMER_REWARD_STRATEGY = "outer_consumer_reward_strategy";

    @Property(F_DETAIL_ID)
    private String detailId;

    @Embedded(F_REWARD_NODE)
    private RewardNodeEntity rewardNode;

    @Embedded(F_REWARD_STRATEGY)
    private RewardStrategyEntity rewardStrategy;

    @Embedded(F_REWARD_PAYMENT)
    private RewardPaymentEntity rewardPayment;

    @Embedded(F_OUTER_CONSUMER_REWARD_STRATEGY)
    private OuterConsumerRewardStrategyEntity outerConsumerRewardStrategy;
}
