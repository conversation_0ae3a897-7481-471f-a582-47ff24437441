package com.facishare.crm.fmcg.tpm.business.enums;

import com.facishare.paas.appframework.core.exception.ValidateException;
import org.apache.commons.lang3.StringUtils;

public enum CarryForwardType {
    CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET("0"),
    CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE("1");
    private final String code;

    CarryForwardType(String code) {
        this.code = code;
    }

    public String code() {
        return this.code;
    }

    public static CarryForwardType of(String optionValue) {
        if (StringUtils.isEmpty(optionValue)) {
            return null;
        }
        switch (optionValue) {
            case "0":
                return CarryForwardType.CARRY_FORWARD_WITH_NEXT_PERIOD_BUDGET;
            case "1":
                return CarryForwardType.CARRY_FORWARD_WITHOUT_NEXT_PERIOD_BUDGET_OR_CREATE;
            default:
                throw new ValidateException("carry forward type not found");
        }

    }
}
