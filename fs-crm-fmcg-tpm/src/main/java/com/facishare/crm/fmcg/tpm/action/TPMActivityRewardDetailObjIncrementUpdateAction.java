package com.facishare.crm.fmcg.tpm.action;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.predef.action.StandardIncrementUpdateAction;


public class TPMActivityRewardDetailObjIncrementUpdateAction extends StandardIncrementUpdateAction {

    @Override
    protected void before(Arg arg) {
        if (!actionContext.getRequestContext().isFromFunction()) {
            throw new ValidateException("action not allowed!");
        }
        super.before(arg);
    }
}
