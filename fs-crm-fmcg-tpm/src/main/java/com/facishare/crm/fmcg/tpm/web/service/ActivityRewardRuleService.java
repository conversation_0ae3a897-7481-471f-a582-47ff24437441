package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.api.rule.*;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityRewardRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IRewardRuleManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IActivityRewardRuleService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fmcg.framework.http.MengNiuOuterRewardProxy;
import com.fmcg.framework.http.contract.mengniu.ActList;
import com.fs.fmcg.sdk.ai.plat.SecretUtil;
import com.fxiaoke.api.IdGenerator;
import com.github.autoconf.ConfigFactory;
import com.github.trace.TraceContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * Author: linmj
 * Date: 2023/9/15 17:23
 */
//IgnoreI18nFile
@Slf4j
@Service
public class ActivityRewardRuleService implements IActivityRewardRuleService {

    @Resource
    private ActivityRewardRuleDAO activityRewardRuleDAO;

    @Resource
    private IRewardRuleManager rewardRuleManager;

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private MengNiuOuterRewardProxy mengNiuOuterRewardProxy;



    @Override
    public AddActivityRewardRule.Result add(AddActivityRewardRule.Arg arg) {
        arg.getRewardRule().setTenantId(arg.getTenantId());
        rewardRuleManager.validateRule(arg.getRewardRule());
        ActivityRewardRulePO rewardRulePO = arg.getRewardRule().toPO();
        activityRewardRuleDAO.save(rewardRulePO);
        AddActivityRewardRule.Result result = new AddActivityRewardRule.Result();
        result.setRewardRule(RewardRuleDTO.fromPO(rewardRulePO));
        writeLog(rewardRulePO.getTenantId(), rewardRulePO.getRelatedObjectApiName(), rewardRulePO.getRelatedObjectId(), "新建激励规则：" + JSON.toJSONString(rewardRulePO));
        return result;
    }

    @Override
    public GetActivityRewardRule.Result get(GetActivityRewardRule.Arg arg) {
        ActivityRewardRulePO po;
        if (!Strings.isNullOrEmpty(arg.getUniqueId())) {
            po = activityRewardRuleDAO.get(arg.getTenantId(), arg.getUniqueId());
        } else {
            po = activityRewardRuleDAO.getByRelatedObject(arg.getTenantId(), arg.getRelatedObjectApiName(), arg.getRelatedObjectId());
        }

        GetActivityRewardRule.Result result = new GetActivityRewardRule.Result();
        result.setRewardRule(RewardRuleDTO.fromPO(po));

        // outer code reward support
        for (RewardDetailDTO rewardDetail : result.getRewardRule().getRewardDetails()) {
            if (Objects.nonNull(rewardDetail.getOuterConsumerRewardStrategy()) && CollectionUtils.isNotEmpty(rewardDetail.getOuterConsumerRewardStrategy().getOuterConsumerRewardRules())) {
                for (OuterConsumerRewardRuleDTO rule : rewardDetail.getOuterConsumerRewardStrategy().getOuterConsumerRewardRules()) {
                    String id = rule.getId();
                    String platform = rule.getPlatform();

                    rule.setCode("MN-CCA-" + rule.getId());
                    rule.setName("--");

                    if (!Strings.isNullOrEmpty(id) && "mn_code_center".equals(platform)) {

                        long time = System.currentTimeMillis();
                        String clientId;
                        String sk;

                        if ("777421".equals(arg.getTenantId())) {
                            clientId = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_client_id");
                            sk = ConfigFactory.getConfig("gray-rel-fmcg").get("sales_mengniu_openapi_sk");
                        } else {
                            clientId = "bf68d8a3-4763-40aa-a8d8-a34763e0aace";
                            sk = "D7ABC695-6EB3-4902-ABC6-956EB3790228";
                        }
                        String sign = SecretUtil.md5(clientId + sk + time).toUpperCase();

                        ActList.Arg codeCenterArg = new ActList.Arg();
                        codeCenterArg.setActName("");
                        codeCenterArg.setPage(1);
                        codeCenterArg.setSize(1);
                        codeCenterArg.setSearchForbidden(true);
                        codeCenterArg.setCodecenteractID(id);

                        ActList.Result codeCenterResult = mengNiuOuterRewardProxy.actList(clientId, sign, String.valueOf(time), codeCenterArg);
                        if (Objects.nonNull(codeCenterResult) && !CollectionUtils.isEmpty(codeCenterResult.getData().getData())) {
                            rule.setName(codeCenterResult.getData().getData().get(0).getActivityName());
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public UpdateRewardRule.Result update(UpdateRewardRule.Arg arg) {
        ActivityRewardRulePO po = arg.getRewardRule().toPO();
        if (rewardRuleManager.checkHasEdit(po)) {
            rewardRuleManager.validateRule(arg.getRewardRule());
            //rewardRuleManager.validateContainsFailRewardDetail(po.getTenantId(), po.getUniqueId());
            po.getRewardDetails().forEach(rewardDetail -> {
                if (Strings.isNullOrEmpty(rewardDetail.getDetailId())) {
                    rewardDetail.setDetailId(IdGenerator.get());
                }
            });
            activityRewardRuleDAO.edit(arg.getTenantId(), -10000, po.getUniqueId(), po);
            writeLog(po.getTenantId(), po.getRelatedObjectApiName(), po.getRelatedObjectId(), "编辑激励规则：" + JSON.toJSONString(po));//ignorei18n
        }
        return new UpdateRewardRule.Result(RewardRuleDTO.fromPO(activityRewardRuleDAO.get(arg.getTenantId(), po.getUniqueId())));
    }


    @Override
    public DeleteRewardRule.Result delete(DeleteRewardRule.Arg arg) {
        activityRewardRuleDAO.delete(arg.getTenantId(), -10000, arg.getUniqueId());
        return new DeleteRewardRule.Result();
    }


    private void writeLog(String tenantId, String apiName, String objectId, String message) {
        try {
            IObjectDescribe describe = serviceFacade.findObject(tenantId, apiName);
            String userId = Strings.isNullOrEmpty(TraceContext.get().getEmployeeId()) ? "-10000" : TraceContext.get().getEmployeeId();
            User user = User.builder().tenantId(tenantId).userId(userId).build();
            IObjectData data = serviceFacade.findObjectData(User.systemUser(tenantId), objectId, apiName);
            serviceFacade.logWithCustomMessage(user, EventType.MODIFY,
                    ActionType.MODIFY,
                    describe,
                    data,
                    message);
        } catch (Exception e) {
            log.info("记录消费规则变更失败。", e);
        }
    }
}
