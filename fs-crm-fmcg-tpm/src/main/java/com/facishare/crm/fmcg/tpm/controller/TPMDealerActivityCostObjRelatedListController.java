package com.facishare.crm.fmcg.tpm.controller;

import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;

public class TPMDealerActivityCostObjRelatedListController extends StandardRelatedListController {
    @Override
    protected void beforeQueryData(SearchTemplateQuery query) {
        if (TPMGrayUtils.TPMUseEs(controllerContext.getTenantId())) {
            query.setSearchSource("es");
        }
        super.beforeQueryData(query);
    }


    @Override
    protected Result after(Arg arg, Result result) {
        fillDealerActivityCost(result);
        return super.after(arg, result);
    }

    private void fillDealerActivityCost(Result result) {

        for (ObjectDataDocument activity : result.getDataList()) {
            Long beginDate = (Long) activity.get(TPMDealerActivityCostFields.BEGIN_DATE);
            Long endDate = (Long) activity.get(TPMDealerActivityCostFields.END_DATE);

            if (beginDate <= TimeUtils.MIN_DATE) {
                activity.put(TPMDealerActivityCostFields.BEGIN_DATE, null);
            }
            if (endDate >= TimeUtils.MAX_DATE) {
                activity.put(TPMDealerActivityCostFields.END_DATE, null);
            }
        }

    }
}
