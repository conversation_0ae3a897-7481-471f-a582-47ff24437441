package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/14 16:04
 */
@Data
@EqualsAndHashCode
@ToString
public class RewardNodeEntity implements Serializable {

    public static final String F_REWARD_DIMENSION = "reward_dimension";

    public static final String F_REWARD_TYPE = "reward_type";

    public static final String F_REWARD_ACTION = "reward_action";

    public static final String F_REWARD_TARGET = "reward_target";

    public static final String F_REQUIRED = "required";

    public static final String F_REWARD_TAG = "reward_tag";

    public static final String F_REWARD_ROLE = "reward_role";

    @Property(F_REWARD_DIMENSION)
    private String rewardDimension;

    @Property(F_REWARD_TYPE)
    private String rewardType;

    @Property(F_REWARD_ACTION)
    private String rewardAction;

    @Property(F_REWARD_TARGET)
    private String rewardTarget;

    @Property(F_REQUIRED)
    private Boolean required;

    @Property(F_REWARD_ROLE)
    private List<String> rewardRole;

    @Property(F_REWARD_TAG)
    private String rewardTag;


}
