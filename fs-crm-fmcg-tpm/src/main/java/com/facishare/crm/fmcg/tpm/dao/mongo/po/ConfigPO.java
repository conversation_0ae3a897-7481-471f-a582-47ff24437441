package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;
import java.util.List;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/20 15:03
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_config", noClassnameStored = true)
public class ConfigPO extends MongoPO implements Serializable {

    public static final String F_KEY = "key";
    public static final String F_VALUE = "value";

    @Property(F_KEY)
    private String key;

    @Property(F_VALUE)
    private Object value;

    public static ConfigPO toPO(ConfigVO vo) {
        if (vo == null) {
            return null;
        }

        ConfigPO po = new ConfigPO();
        po.setKey(vo.getKey());
        po.setValue(vo.getValue());
        return po;
    }

    public static ConfigVO toVO(ConfigPO po) {
        if (po == null) {
            return null;
        }

        ConfigVO vo = new ConfigVO();
        vo.setKey(po.getKey());
        vo.setValue(po.getValue());
        return vo;
    }

    public <T> T getObjectValue() {
        return (T) value;
    }

    public <T> List<T> getListValue(Class<T> clazz) {
        return JSON.parseArray(JSON.toJSONString(value)).toJavaList(clazz);
    }

}
