package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/12/16 14:53
 */
@Data
@ToString
@Builder
public class ActivityProofReportVO implements Serializable {

    @JSONField(name = "object_display_name")
    @JsonProperty(value = "object_display_name")
    @SerializedName("object_display_name")
    private String objectDisplayName;

    @JSONField(name = "total_count")
    @JsonProperty(value = "total_count")
    @SerializedName("total_count")
    private int totalCount;
}
