package com.facishare.crm.fmcg.tpm.api;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/15 15:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class SimpleDTO {

    private String name;
    private String value;
    private String color;

    @JsonProperty(value = "belong_to")
    @SerializedName("belong_to")
    @JSONField(name = "belong_to")
    private List<String> belongTo;
    private Integer order;
    private String type;

    @JsonProperty(value = "contain_items")
    @SerializedName("contain_items")
    @JSONField(name = "contain_items")
    private List<String> containItems;

    @JsonProperty(value = "allow_trigger")
    @SerializedName("allow_trigger")
    @JSONField(name = "allow_trigger")
    private Boolean allowTrigger;

    public SimpleDTO(String name, String value) {
        this.name = name;
        this.value = value;
    }
}
