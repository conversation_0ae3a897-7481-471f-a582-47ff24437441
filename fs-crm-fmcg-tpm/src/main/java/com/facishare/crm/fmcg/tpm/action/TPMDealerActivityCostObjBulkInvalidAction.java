package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityFields;
import com.facishare.crm.fmcg.common.apiname.TPMDealerActivityCostFields;
import com.facishare.crm.fmcg.tpm.business.BudgetService;
import com.facishare.crm.fmcg.tpm.business.abstraction.IDealerActivityCostRebateService;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityWriteOffSourceConfigEntity;
import com.facishare.crm.fmcg.tpm.utils.CommonUtils;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/11 下午5:03
 */

@SuppressWarnings("Duplicates")
@Slf4j
public class TPMDealerActivityCostObjBulkInvalidAction extends StandardBulkInvalidAction {

    private static final BudgetService budgetService = SpringUtil.getContext().getBean(BudgetService.class);

    private static final ActivityTypeManager activityTypeManager = SpringUtil.getContext().getBean(ActivityTypeManager.class);
    private static final IDealerActivityCostRebateService dealerActivityCostRebateService = SpringUtil.getContext().getBean(IDealerActivityCostRebateService.class);

    private static final Logger LOGGER = LoggerFactory.getLogger(TPMDealerActivityCostObjBulkInvalidAction.class);

    private List<String> costIds;

    @Override
    protected void before(Arg arg) {

        List<IObjectData> costs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), ApiNames.TPM_DEALER_ACTIVITY_COST);
        costIds = costs.stream().map(DBRecord::getId).collect(Collectors.toList());
        List<String> names = Lists.newArrayList();
        costs.forEach(cost -> {
            if (cost.get("enter_into_account", Boolean.class, false))
                names.add(cost.getName());
        });
        if (!CollectionUtils.isEmpty(names)) {
            throw new ValidateException(I18N.text(I18NKeys.THIS_COST_HAS_TRANSFER_IN_ACCOUNT_DO_NOT_SUPPORT_INVALID) + names);
        }
        if (TPMGrayUtils.excessDeductionForCost(actionContext.getTenantId())) {
            throw new ValidateException(I18N.text(I18NKeys.DEALER_ACTIVITY_COST_OBJ_BULK_INVALID_ACTION_0));
        }
        super.before(arg);
    }

    @Override
    protected Result after(Arg arg, Result result) {

        //作废重新计算
        if (budgetService.isOpenBudge(Integer.parseInt(actionContext.getTenantId()))) {
            Set<String> activityIds = new HashSet<>();
            Map<String, List<String>> budgetToActivity = new HashMap<>();
            result.getObjectDataList().forEach(v -> {
                if (!Strings.isNullOrEmpty((String) v.getOrDefault(TPMDealerActivityCostFields.ACTIVITY_ID, "")))
                    activityIds.add((String) v.get(TPMDealerActivityCostFields.ACTIVITY_ID));
            });
            List<IObjectData> activities = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), new ArrayList<>(activityIds), ApiNames.TPM_ACTIVITY_OBJ);
            activities.forEach(activity -> {
                String budgetId = activity.get(TPMActivityFields.BUDGET_TABLE, String.class, "");
                if (!Strings.isNullOrEmpty(budgetId)) {
                    if (budgetToActivity.containsKey(budgetId)) {
                        budgetToActivity.get(budgetId).add(activity.getId());
                    } else {
                        budgetToActivity.put(budgetId, Lists.newArrayList(activity.getId()));
                    }

                }
            });
            budgetToActivity.forEach((budgetId, activityList) -> {
                budgetService.tryLockBudget(actionContext, budgetId);
                activityList.forEach(acId -> budgetService.calculateActivity(actionContext.getTenantId(), acId));
                budgetService.calculateBudget(actionContext.getTenantId(), budgetId);
                budgetService.unLockBudget(actionContext);
            });
        } else {
            Set<String> activityIds = new HashSet<>();
            result.getObjectDataList().forEach(v -> {
                if (!Strings.isNullOrEmpty((String) v.getOrDefault(TPMDealerActivityCostFields.ACTIVITY_ID, "")))
                    activityIds.add((String) v.get(TPMDealerActivityCostFields.ACTIVITY_ID));
            });
            activityIds.forEach(acId -> budgetService.calculateActivity(actionContext.getTenantId(), acId));
        }
        rmRelated(result);
        //作废返利单
        invalidRebateObjData();
        return super.after(arg, result);
    }

    private void invalidRebateObjData() {
        dealerActivityCostRebateService.invalidRebateObjData(actionContext.getRequestContext(), costIds);
    }
    public void rmRelated(Result result) {

        List<ObjectDataDocument> objectDataDocuments = new ArrayList<>(result.getObjectDataList());
        objectDataDocuments.removeAll(result.getFailureObjectDataList());
        List<String> costIds = objectDataDocuments.stream().map(ObjectDataDocument::getId).collect(Collectors.toList());
        for (String costId : costIds) {
            IObjectData cost = serviceFacade.findObjectDataIncludeDeleted(User.systemUser(actionContext.getTenantId()), costId, ApiNames.TPM_DEALER_ACTIVITY_COST);
            String activityId = cost.get(TPMDealerActivityCostFields.ACTIVITY_ID, String.class);
            ActivityTypeExt activityType = activityTypeManager.findByActivityId(actionContext.getTenantId(), activityId);
            ActivityWriteOffSourceConfigEntity config = activityType.writeOffSourceConfig();
            if (config == null || Strings.isNullOrEmpty(config.getApiName())) {
                return;
            }
            SearchTemplateQuery proofQuery = new SearchTemplateQuery();

            proofQuery.setLimit(-1);
            proofQuery.setOffset(0);
            proofQuery.setSearchSource("db");

            IFilter costFilter = new Filter();
            costFilter.setFieldName(config.getReferenceWriteOffFieldApiName());
            costFilter.setOperator(Operator.EQ);
            costFilter.setFieldValues(Lists.newArrayList(costId));
            proofQuery.setFilters(Lists.newArrayList(costFilter));

            List<IObjectData> referenceObjs = CommonUtils.queryData(serviceFacade, User.systemUser(actionContext.getTenantId()), config.getApiName(), proofQuery);

            List<String> proofUpdateFields = Lists.newArrayList(config.getReferenceWriteOffFieldApiName());
            for (List<IObjectData> proofObjs : Lists.partition(referenceObjs, 200)) {
                proofObjs.forEach(v -> v.set(config.getReferenceWriteOffFieldApiName(), ""));
                serviceFacade.batchUpdateByFields(User.systemUser(actionContext.getTenantId()), proofObjs, proofUpdateFields);
            }
        }
    }

    @Override
    protected void finallyDo() {
        try {
            super.finallyDo();
        } finally {
            budgetService.unLockBudget(actionContext);
        }
    }
}
