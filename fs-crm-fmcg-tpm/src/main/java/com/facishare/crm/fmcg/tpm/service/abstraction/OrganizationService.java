package com.facishare.crm.fmcg.tpm.service.abstraction;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.employee.EmployeeDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrganizationService {

    EmployeeDto getEmployee(int tenantId, int employeeId);

    DepartmentDto getDepartment(int tenantId, int departmentId);

    DepartmentDto getParentDepartment(int tenantId, int subDepartmentId, int parentLevel);

    List<DepartmentDto> getChildrenDepartment(int tenantId, int parentDepartmentId);

    List<EmployeeDto> queryAllEmployee(int tenantId);

    List<Integer> getDepartmentIds(int tenantId, int employeeId);

    List<Integer> queryEmployeeIds(int tenantId, int departmentId);

    List<Integer> queryLowerDepartmentIds(int tenantId, int departmentId);

    List<Integer> queryUpperDepartmentIds(int tenantId, int departmentId);

    List<DepartmentDto> queryLowerDepartments(int tenantId, int departmentId);

    List<Integer> batchQueryLowerDepartmentIds(int tenantId, List<Integer> departmentIds);

    List<Integer> batchQueryLowerDepartmentIdsIncludeAll(int tenantId, List<Integer> departmentIds);

    boolean employeeInRange(int tenantId, int employeeId, List<Integer> departmentIds);

    List<Integer> queryAllDepartmentIds(int tenantId);

    List<DepartmentDto> queryAllDepartment(int tenantId);

    List<String> getDepartmentIdsByNames(int tenantId, List<String> names);

    List<DepartmentDto> batchGetDepartment(int tenantId, List<Integer> departments);

    List<DepartmentDto> batchGetAllDepartment(int tenantId, List<Integer> departments);
}
