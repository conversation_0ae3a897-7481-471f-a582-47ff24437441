package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetTypeVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

public interface AddBudgetType {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "budget_type")
        @JsonProperty(value = "budget_type")
        @SerializedName("budget_type")
        private BudgetTypeVO budgetType;
    }


    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "budget_type")
        @JsonProperty(value = "budget_type")
        @SerializedName("budget_type")
        private BudgetTypeVO budgetType;
    }
}
