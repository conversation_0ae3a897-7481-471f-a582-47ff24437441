package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.tpm.api.agreement.AgreementStoreConfirm;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.common.apiname.TPMActivityAgreementFields;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/10 3:46 PM
 */
@SuppressWarnings("Duplicates,unused")
public class TPMActivityAgreementObjAgreementStoreConfirmAction extends PreDefineAction<AgreementStoreConfirm.Arg, AgreementStoreConfirm.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList("AgreementStoreConfirm");
    }

    @Override
    protected List<String> getDataPrivilegeIds(AgreementStoreConfirm.Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected AgreementStoreConfirm.Result doAct(AgreementStoreConfirm.Arg arg) {
        IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
        IObjectData old = serviceFacade.findObjectData(User.systemUser(actionContext.getTenantId()), arg.getObjectDataId(), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);

        String mode = old.get(TPMActivityAgreementFields.SIGNING_MODE, String.class);
        if (!TPMActivityAgreementFields.SIGNING_MODE__AGENT_SIGNING.equals(mode)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ACTIVITY_AGREEMENT_OBJ_AGREEMENT_STORE_CONFIRM_ACTION_0));
        }

        String agreementStatus = old.get(TPMActivityAgreementFields.AGREEMENT_STATUS, String.class);
        if (TPMActivityAgreementFields.AGREEMENT_STATUS__END.equals(agreementStatus)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ACTIVITY_AGREEMENT_OBJ_AGREEMENT_STORE_CONFIRM_ACTION_1));
        }

        if (TPMActivityAgreementFields.AGREEMENT_STATUS__INVALID.equals(agreementStatus)) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ACTIVITY_AGREEMENT_OBJ_AGREEMENT_STORE_CONFIRM_ACTION_2));
        }

        old.set(TPMActivityAgreementFields.STORE_CONFIRM_STATUS, TPMActivityAgreementFields.STORE_CONFIRM_STATUS__CONFIRMED);
        IObjectData agreement = serviceFacade.updateObjectData(actionContext.getUser(), old);

        serviceFacade.logWithCustomMessage(actionContext.getUser(), EventType.MODIFY, ActionType.AGREEMENT_STORE_CONFIRM, describe, agreement, "已确认");//ignorei18n
        return AgreementStoreConfirm.Result.builder().data(ObjectDataDocument.of(agreement)).build();
    }
}
