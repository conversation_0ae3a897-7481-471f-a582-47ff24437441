package com.facishare.crm.fmcg.tpm.reward.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/6/12 18:13
 */
public interface StockCheckReward {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "upperTenantId")
        @JsonProperty(value = "upperTenantId")
        @SerializedName("upperTenantId")
        private String upperTenantId;

        @JSONField(name = "serialNumberStatusId")
        @JsonProperty(value = "serialNumberStatusId")
        @SerializedName("serialNumberStatusId")
        private String serialNumberStatusId;

        @JSONField(name = "dataTenantId")
        @JsonProperty(value = "dataTenantId")
        @SerializedName("dataTenantId")
        private String dataTenantId;

        @JSONField(name = "storeCheckId")
        @JsonProperty(value = "storeCheckId")
        @SerializedName("storeCheckId")
        private String storeCheckId;
    }


    @Data
    @ToString
    class Result implements Serializable {

        /**
         * 1 active   2 verification   0 none  3 没有开灰度 不支持
         */
        private int status;

    }
}
