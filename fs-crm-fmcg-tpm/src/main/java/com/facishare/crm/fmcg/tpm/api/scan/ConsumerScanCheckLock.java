package com.facishare.crm.fmcg.tpm.api.scan;

import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

public interface ConsumerScanCheckLock {

    @Data
    @ToString
    @EqualsAndHashCode(callSuper = true)
    class Arg extends WeChatArg implements Serializable {

        private String code;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private boolean locked;
    }
}