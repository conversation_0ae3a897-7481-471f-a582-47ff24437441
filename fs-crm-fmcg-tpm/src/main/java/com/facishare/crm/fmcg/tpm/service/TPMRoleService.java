package com.facishare.crm.fmcg.tpm.service;

import com.facishare.crm.fmcg.tpm.api.SimpleDTO;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.organization.paas.model.PaaSPermissionArgument.AuthContext;
import com.facishare.organization.paas.model.PaaSPermissionArgument.ProjectProperties;
import com.facishare.organization.paas.model.PaaSResult;
import com.facishare.organization.paas.model.permission.*;
import com.facishare.organization.paas.service.PaaSPermissionService;
import com.facishare.organization.paas.util.PaasArgumentUtil;
import com.facishare.paas.auth.model.RolePojo;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

//IgnoreI18nFile
@Slf4j
@SuppressWarnings("Duplicates,unused")
@Component("tpmRoleService")
public class TPMRoleService implements IRoleService {
    @Resource
    private PaaSPermissionService paaSPermissionService;

    @Resource
    private RoleClient roleClient;

    private static final String ROLE_APP_ID = "CRM";

    @Override
    public Map<String, String> queryRoleNames(Integer enterpriseId) {
        RoleListDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(RoleListDto.Argument.class, enterpriseId, -10000, ROLE_APP_ID);
        PaaSResult<RoleListDto.Result> result = paaSPermissionService.roleList(argument);
        return result.getResult().getRoles().stream().peek(role -> {
            if (role.isDelFlag()) {
                role.setRoleName(role.getRoleName() + "（已删除）");
            }
        }).collect(Collectors.toMap(PaasRoleDetail::getRoleCode, PaasRoleDetail::getRoleName));
    }

    @Override
    public Map<String, String> queryRoleCodes(Integer enterpriseId) {
        RoleListDto.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(RoleListDto.Argument.class, enterpriseId, -10000, ROLE_APP_ID);
        PaaSResult<RoleListDto.Result> result = paaSPermissionService.roleList(argument);
        return result.getResult().getRoles().stream().peek(role -> {
            if (role.isDelFlag()) {
                role.setRoleName(role.getRoleName() + "（已删除）");
            }
        }).collect(Collectors.toMap(PaasRoleDetail::getRoleName, PaasRoleDetail::getRoleCode));
    }

    @Override
    public List<Integer> queryEmployeeIdsByRoleIds(int enterpriseId, List<String> roleIds) {
        return queryEmployeeIdsByRoleIds(enterpriseId, roleIds, true);
    }


    @Override
    public List<Integer> queryEmployeeIdsByRoleIds(int enterpriseId, List<String> roleIds, boolean isOnlyMainRole) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }
        QueryRoleUsersByRoles.Argument argument = PaasArgumentUtil.buildPaaSPermissionArgument(QueryRoleUsersByRoles.Argument.class, enterpriseId, -10000, ROLE_APP_ID);
        argument.setRoles(roleIds);
        PaaSResult<Map<String, Map<String, Boolean>>> result = paaSPermissionService.queryRoleUsersByRoles(argument);
        if (Objects.isNull(result) || Objects.isNull(result.getResult())) {
            return new ArrayList<>();
        }
        List<Integer> employeeIds = Lists.newArrayList();
        result.getResult().values().forEach(item ->
        {
            if (Objects.nonNull(item)) {
                item.forEach((key, value) -> {
                    if (isOnlyMainRole) {
                        if (value != null && value) {
                            employeeIds.add(Integer.parseInt(key));
                        }
                    } else {
                        employeeIds.add(Integer.parseInt(key));
                    }
                });
            }
        });
        return employeeIds;
    }

    @Override
    public Map<Integer, String> queryRoleByEmployeeIds(Integer enterpriseId, List<Integer> employeeIds) {
        List<RoleRelationEntityDto> relations = queryRoleRelationByEmployeeIds(enterpriseId, employeeIds);
        Map<Integer, String> result = new HashMap<>();
        for (RoleRelationEntityDto relation : relations) {
            int key = Integer.parseInt(relation.getOrgId());
            if (!result.containsKey(key)) {
                result.put(key, relation.getRoleCode());
            }
        }
        return result;
    }

    private List<RoleRelationEntityDto> queryRoleRelationByEmployeeIds(int enterpriseId, List<Integer> employeeIds) {
        if (CollectionUtils.isEmpty(employeeIds)) {
            return new ArrayList<>();
        }
        List<String> ids = employeeIds.stream().map(String::valueOf).collect(Collectors.toList());
        GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument arg = PaasArgumentUtil.buildPaaSPermissionArgument(GetMultiEmployeeRoleRelationEntitiesByEmployeeIDs.Argument.class, enterpriseId, -10000, ROLE_APP_ID);
        arg.setUsers(ids);
        PaaSResult<List<RoleRelationEntityDto>> result = paaSPermissionService.getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(arg);

        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
            return new ArrayList<>();
        }
        return result.getResult().stream().filter(f -> Boolean.TRUE.equals(f.getDefaultRole())).collect(Collectors.toList());
    }

    @Override
    public List<String> queryRoleByEmployeeId(int enterpriseId, int employeeId) {
        QueryRoleCodeListByUserId.Argument arg = PaasArgumentUtil.buildPaaSPermissionArgument(QueryRoleCodeListByUserId.Argument.class, enterpriseId, employeeId, ROLE_APP_ID);
        PaaSResult<List<String>> result = paaSPermissionService.queryRoleCodeListByUserId(arg);
        if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getResult())) {
            return new ArrayList<>();
        }
        return result.getResult();
    }

    @Override
    public List<String> queryRoleCodeListByUserId(String enterpriseId, int employeeId) {
        AuthContext authContext = new AuthContext();
        authContext.setTenantId(enterpriseId);
        authContext.setAppId(ROLE_APP_ID);
        authContext.setUserId(String.valueOf(employeeId));
        authContext.setObjectProperties(new ProjectProperties());

        QueryRoleCodeListByUserId.Argument argument = new QueryRoleCodeListByUserId.Argument();
        argument.setAuthContext(authContext);
        PaaSResult<List<String>> paaSResult = paaSPermissionService.queryRoleCodeListByUserId(argument);
        if (Objects.isNull(paaSResult) || CollectionUtils.isEmpty(paaSResult.getResult())) {
            return new ArrayList<>();
        }
        return paaSResult.getResult();
    }

    @Override
    public Map<String, List<SimpleDTO>> queryOuterRoleByOuterInfo(String upstreamTenantId, String outerTenantId, List<String> outerUserIds) {
        com.facishare.paas.auth.model.AuthContext authContext = new com.facishare.paas.auth.model.AuthContext();
        authContext.setTenantId(upstreamTenantId);
        authContext.setAppId("All");
        Map<String, String> roleNameMap = queryAllOuterRoleCode2RoleName(upstreamTenantId);

        Map<String, List<SimpleDTO>> map = new HashMap<>();
        roleClient.queryOuterUserRole(authContext, outerUserIds, Sets.newHashSet(), Sets.newHashSet(outerTenantId)).forEach(role -> {
            SimpleDTO simpleDTO = new SimpleDTO();
            if (role.getDefaultRole()) {
                simpleDTO.setType("default");
            } else {
                simpleDTO.setType("other");
            }
            simpleDTO.setName(roleNameMap.get(role.getRoleCode()));
            simpleDTO.setValue(role.getRoleCode());
            List<SimpleDTO> roles = map.getOrDefault(role.getOrgId(), new ArrayList<>());
            roles.add(simpleDTO);
            map.put(role.getOrgId(), roles);
        });

        return map;

    }

    @Override
    public Map<String, String> queryAllOuterRoleCode2RoleName(String upstreamTenantId) {
        com.facishare.paas.auth.model.AuthContext authContext = new com.facishare.paas.auth.model.AuthContext();
        authContext.setTenantId(upstreamTenantId);
        authContext.setAppId("All");
        Map<String, String> roleNameMap = new HashMap<>();
        roleClient.queryRole(authContext, null, null).forEach(rolePojo -> {
            if (rolePojo.getDelFlag()) {
                rolePojo.setRoleName(rolePojo.getRoleName() + "(已删除)");
            }
            roleNameMap.put(rolePojo.getRoleCode(), rolePojo.getRoleName());
        });
        return roleNameMap;
    }

    @Override
    public String getRoleName(String tenantId, String roleCode) {
        com.facishare.paas.auth.model.AuthContext authContext = new com.facishare.paas.auth.model.AuthContext();
        authContext.setTenantId(tenantId);
        authContext.setAppId("All");
        List<RolePojo> rolePojos = roleClient.queryRole(authContext, roleCode, null);

        return CollectionUtils.isEmpty(rolePojos) ? null : rolePojos.get(0).getRoleName();
    }

    @Override
    public SimpleDTO getMainRoleByEmployeeId(String tenantId, String employeeId) {
        List<RoleRelationEntityDto> roleList = queryRoleRelationByEmployeeIds(Integer.valueOf(tenantId), Lists.newArrayList(Integer.valueOf(employeeId)));
        if (CollectionUtils.isNotEmpty(roleList)) {
            SimpleDTO simpleDTO = new SimpleDTO();
            simpleDTO.setName(getRoleName(tenantId, roleList.get(0).getRoleCode()));
            simpleDTO.setValue(roleList.get(0).getRoleCode());
            return simpleDTO;
        }
        return null;
    }

    @Override
    public List<RolePojo> queryExistRole(String tenantId, Set<String> roleCodes) {
        com.facishare.paas.auth.model.AuthContext authContext = new com.facishare.paas.auth.model.AuthContext();
        authContext.setTenantId(tenantId);
        authContext.setAppId("All");
        List<RolePojo> roles = roleClient.queryAllRoleInfo(authContext, roleCodes, true);
        if (CollectionUtils.isEmpty(roles)) {
            return new ArrayList<>();
        }
        return roles.stream().filter(rolePojo -> roleCodes.contains(rolePojo.getRoleCode())).collect(Collectors.toList());
    }
}
