package com.facishare.crm.fmcg.tpm.web.designer;

import com.facishare.crm.fmcg.tpm.dao.mongo.po.ConfigEnum;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ConfigVO;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * author: wuyx
 * description:
 * createTime: 2023/3/23 17:40
 */
@Component
public class ConfigManager implements IConfigManager {

    @Resource
    private ConfigFactory configFactory;

    @Override
    public void basicValidation(String tenantId, List<ConfigVO> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            throw new ValidateException(I18N.text(I18NKeys.CONFIG_MANAGER_0));
        }

        for (ConfigVO vo : vos) {
            String key = vo.getKey();
            Object value = vo.getValue();

            if (StringUtils.isEmpty(key)) {
                throw new ValidateException(I18N.text(I18NKeys.CONFIG_MANAGER_1));
            }

            if (Objects.isNull(value)) {
                throw new ValidateException(I18N.text(I18NKeys.CONFIG_MANAGER_2));
            }

            if (!ConfigEnum.getDefault().containsKey(key)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.CONFIG_MANAGER_3), key));
            }

            if (!isValue(value)) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.CONFIG_MANAGER_4), value));
            }
        }
    }

    public static boolean isValue(Object value) {
        String pattern = "^[a-zA-Z][a-zA-Z0-9_]{1,128}$";
        if (value instanceof String) {
            return Pattern.matches(pattern, (String) value);
        }
        if (value instanceof List) {
            return ((List<?>) value).stream().filter(v -> v instanceof String).allMatch(v -> Pattern.matches(pattern, (String) v));
        }
        return true;
    }


    @Override
    public void validation(String tenantId, List<ConfigVO> vos) {
        for (ConfigVO vo : vos) {
            configFactory.resolve(vo.getKey()).validation(tenantId, vo);
        }
    }

    @Override
    public void after(String tenantId, List<ConfigVO> vos) {
        for (ConfigVO vo : vos) {
            configFactory.resolve(vo.getKey()).after(tenantId, vo);
        }
    }

}
