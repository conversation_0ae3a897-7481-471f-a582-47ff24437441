package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.controller.StandardRelatedListController;
import com.facishare.paas.metadata.api.IObjectData;

import java.math.BigDecimal;

public class TPMActivityAgreementCashingProductObjRelatedListController extends StandardRelatedListController {

    private static final String PRODUCT_ID = "product_id";
    private static final String PRICE = "price";

    @Override
    protected Result after(Arg arg, Result result) {
        return overrideResult(super.after(arg, result));
    }

    private Result overrideResult(Result result) {
        for (ObjectDataDocument dataDocument : result.getDataList()) {
            String productId = (String) dataDocument.get(PRODUCT_ID);
            IObjectData product = serviceFacade.findObjectData(User.systemUser(controllerContext.getTenantId()), productId, ApiNames.PRODUCT_OBJ);
            if (product != null) {
                BigDecimal price = product.get(PRICE, BigDecimal.class);
                dataDocument.put(PRICE, price);
            }
        }
        return result;
    }

}
