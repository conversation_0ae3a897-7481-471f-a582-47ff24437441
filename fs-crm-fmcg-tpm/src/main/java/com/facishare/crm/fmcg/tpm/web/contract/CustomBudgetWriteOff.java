package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.web.contract.model.WriteOffItemVO;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/10/8 16:17
 */
public interface CustomBudgetWriteOff {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "write_off_object_api_name")
        @JsonProperty(value = "write_off_object_api_name")
        @SerializedName("write_off_object_api_name")
        private String writeOffObjectApiName;

        @JSONField(name = "write_off_object_data_id")
        @JsonProperty(value = "write_off_object_data_id")
        @SerializedName("write_off_object_data_id")
        private String writeOffObjectDataId;

        @JSONField(name = "biz_object_api_name")
        @JsonProperty(value = "biz_object_api_name")
        @SerializedName("biz_object_api_name")
        private String bizObjectApiName;

        @JSONField(name = "biz_object_data_id")
        @JsonProperty(value = "biz_object_data_id")
        @SerializedName("biz_object_data_id")
        private String bizObjectDataId;

        @JSONField(name = "close")
        @JsonProperty(value = "close")
        @SerializedName("close")
        private boolean close;

        @JSONField(name = "exclude_account_ids")
        @JsonProperty(value = "exclude_account_ids")
        @SerializedName("exclude_account_ids")
        private List<String> excludeAccountIds;

        @JSONField(name = "data")
        @JsonProperty(value = "data")
        @SerializedName("data")
        private List<WriteOffItemVO> data;

        @JSONField(name = "ignore_department_dimension")
        @JsonProperty(value = "ignore_department_dimension")
        @SerializedName("ignore_department_dimension")
        private Boolean ignoreDepartmentDimension;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "business_trace_id")
        @JsonProperty(value = "business_trace_id")
        @SerializedName("business_trace_id")
        private String businessTraceId;

        @JSONField(name = "approval_trace_id")
        @JsonProperty(value = "approval_trace_id")
        @SerializedName("approval_trace_id")
        private String approvalTraceId;
    }
}
