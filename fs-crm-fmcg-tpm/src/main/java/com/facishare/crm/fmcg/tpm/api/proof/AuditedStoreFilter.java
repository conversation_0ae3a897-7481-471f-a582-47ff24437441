package com.facishare.crm.fmcg.tpm.api.proof;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface AuditedStoreFilter {

    @Data
    @ToString
    class Arg implements Serializable {

        private Long begin;

        private Long end;

        @SerializedName("dealer_id")
        @JSONField(name = "dealer_id")
        @JsonProperty("dealer_id")
        private String dealerId;

        @SerializedName("activity_id")
        @JSONField(name = "activity_id")
        @JsonProperty("activity_id")
        private String activityId;

        @SerializedName("checkin_id_list")
        @J<PERSON><PERSON>ield(name = "checkin_id_list")
        @JsonProperty("checkin_id_list")
        private List<String> checkinIdList;
    }

    @Data
    @ToString
    class Result implements Serializable {

        private List<String> checkinIdList = Lists.newArrayList();
    }
}