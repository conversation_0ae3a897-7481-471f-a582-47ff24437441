package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Property;

import java.io.Serializable;

@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Embedded
public class RewardTagEntity implements Serializable {

    @Property("label")
    private String label;

    @Property("color")
    private String color;

    @Property("value")
    private String value;
}
