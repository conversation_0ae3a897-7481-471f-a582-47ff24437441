package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


@Data
@ToString
public class PersonRewardRuleTriggerConditionVO implements Serializable {

    @JSONField(name = "field_name")
    @JsonProperty(value = "field_name")
    @SerializedName("field_name")
    private String fieldName;

    @JSONField(name = "field_values")
    @JsonProperty(value = "field_values")
    @SerializedName("field_values")
    private List<String> fieldValues;

    private String operator;

    @JSONField(name = "value_type")
    @JsonProperty(value = "value_type")
    @SerializedName("value_type")
    private Integer valueType;

    @JSONField(name = "type")
    @JsonProperty(value = "type")
    @SerializedName("type")
    private String type;
}
