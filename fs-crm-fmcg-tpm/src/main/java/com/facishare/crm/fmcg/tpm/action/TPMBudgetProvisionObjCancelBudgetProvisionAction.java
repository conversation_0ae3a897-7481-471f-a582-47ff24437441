package com.facishare.crm.fmcg.tpm.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.business.BudgetProvisionService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.AbstractStandardAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * 预提 取消预算预提
 */
public class TPMBudgetProvisionObjCancelBudgetProvisionAction extends AbstractStandardAction<TPMBudgetProvisionObjCancelBudgetProvisionAction.Arg, TPMBudgetProvisionObjCancelBudgetProvisionAction.Result> {

    private final BudgetProvisionService budgetProvisionService = SpringUtil.getContext().getBean(BudgetProvisionService.class);

    private IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CANCEL_BUDGET_PROVISION.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Override
    protected TPMBudgetProvisionObjCancelBudgetProvisionAction.Result doAct(TPMBudgetProvisionObjCancelBudgetProvisionAction.Arg arg) {
        if (this.objectData != null) {
            budgetProvisionService.cancelBudgetProvision(objectData);
        }
        return TPMBudgetProvisionObjCancelBudgetProvisionAction.Result.of(objectData);
    }

    protected IObjectData getPreObjectData() {
        return objectData;
    }

    protected IObjectData getPostObjectData() {
        return objectData;
    }

    protected String getButtonApiName() {
        return "CancelBudgetProvision_button_default";
    }

    @Override
    protected void init() {
        super.init();
        if (CollectionUtils.notEmpty(dataList)) {
            objectData = dataList.get(0);
        }
    }

    @Data
    public static class Arg {

        @SerializedName("objectDataId")
        @JSONField(name = "objectDataId")
        @JsonProperty("objectDataId")
        private String dataId;
    }


    @Data
    public static class Result {
        private ObjectDataDocument objectData;

        public static Result of(IObjectData objectData) {
            Result result = new Result();
            result.setObjectData(ObjectDataDocument.of(objectData));
            return result;
        }
    }
}
