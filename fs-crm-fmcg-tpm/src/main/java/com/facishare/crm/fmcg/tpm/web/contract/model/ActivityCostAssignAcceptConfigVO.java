package com.facishare.crm.fmcg.tpm.web.contract.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityCostAssignAcceptConfigEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @wuyx
 * create time 2022/5/30 19:42
 */
@Data
@ToString
public class ActivityCostAssignAcceptConfigVO implements Serializable {

    @JSONField(name = "accept_status")
    @JsonProperty(value = "accept_status")
    @SerializedName("accept_status")
    private Boolean acceptStatus;

    public static ActivityCostAssignAcceptConfigVO fromPO(ActivityCostAssignAcceptConfigEntity po) {
        if (po == null) {
            return null;
        }
        ActivityCostAssignAcceptConfigVO vo = new ActivityCostAssignAcceptConfigVO();
        vo.setAcceptStatus(po.getAcceptStatus());
        return vo;
    }
}
