package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.web.contract.model.BudgetNewConsumeRuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.mongodb.morphia.annotations.Embedded;
import org.mongodb.morphia.annotations.Entity;
import org.mongodb.morphia.annotations.Property;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/14 下午5:59
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = true)
@SuppressWarnings("Duplicates")
@Entity(value = "fmcg_tpm_new_budget_consume_rule", noClassnameStored = true)
public class BudgetNewConsumeRulePO extends MongoPO {


    public static final String F_VERSION = "version";
    public static final String F_NAME = "name";
    public static final String F_RULE_TYPE = "rule_type";
    public static final String F_RULE_DESCRIPTION = "rule_description";
    public static final String F_RULE_STATUS = "rule_status";
    public static final String F_API_NAME = "api_name";
    public static final String F_RECORD_TYPE = "record_type";
    public static final String F_RULE_TYPE_NODES = "rule_type_nodes";
    public static final String F_BUDGET_TABLE_AUTOMATIC_NODES = "budget_table_automatic_nodes";
    public static final String F_BUDGET_TABLE_MANUAL_NODES = "budget_table_manual_nodes";
    public static final String F_BUDGET_TYPE = "budget_type";

    public static final String F_DEDUCT_API_NAME = "deduct_api_name";
    public static final String F_DEDUCT_RECORD_TYPE = "deduct_record_type";
    public static final String F_DEDUCT_TYPE = "deduct_type";
    public static final String F_BUDGET_METHOD = "budget_method";
    public static final String F_OVER_DEDUCT_FLAG = "over_deduct_flag";

    public static final String F_RELEASE_STATUS = "release_status";
    public static final String F_PROVISION_STATUS = "provision_status";

    public static final String F_RELEASE_LIST = "release_list";



    @Property(F_VERSION)
    private long version;

    @Property(F_NAME)
    private String name;

    @Property(F_RULE_TYPE)
    private String ruleType;

    @Property(F_RULE_DESCRIPTION)
    private String ruleDescription;

    @Property(F_RULE_STATUS)
    private String ruleStatus;

    @Property(F_API_NAME)
    private String apiName;

    @Property(F_RECORD_TYPE)
    private String recordType;

    // 0：扣减本身对象， 1：扣减其他对象
    @Property(F_DEDUCT_TYPE)
    private Integer deductType;

    @Property(F_DEDUCT_API_NAME)
    private String deductApiName;

    @Property(F_DEDUCT_RECORD_TYPE)
    private String deductRecordType;

    @Property(F_RELEASE_STATUS)
    private Integer releaseStatus;

    @Property(F_PROVISION_STATUS)
    private Integer provisionStatus;

    @Embedded(F_RELEASE_LIST)
    private List<ReleaseBusinessEntity> releaseList;

    // 手动选择 manual / 自动映射 automatic
    @Property(F_BUDGET_METHOD)
    private String budgetMethod;

    @Embedded(F_RULE_TYPE_NODES)
    private List<BudgetRuleTypeNodeEntity> ruleTypeNodes;

    @Embedded(F_BUDGET_TABLE_AUTOMATIC_NODES)
    private List<BudgetTableAutomaticNodeEntity> budgetTableAutomaticNodes;

    @Embedded(F_BUDGET_TABLE_MANUAL_NODES)
    private List<BudgetTableManualNodeEntity> budgetTableManualNodes;

    @Property(F_OVER_DEDUCT_FLAG)
    private Boolean overDeductFlag = false;

    public boolean getOverDeductFlag() {
        String tenantId = getTenantId();
        if (Objects.isNull(tenantId)) {
            return overDeductFlag;
        }
        return TPMGrayUtils.forceOverDeductInConsumeRule(tenantId) || Boolean.TRUE.equals(overDeductFlag);
    }

    public static BudgetNewConsumeRulePO fromVO(BudgetNewConsumeRuleVO vo) {
        if (vo == null) {
            return null;
        }
        BudgetNewConsumeRulePO budgetConsumeRulePO = new BudgetNewConsumeRulePO();
        budgetConsumeRulePO.setUniqueId(vo.getId());
        budgetConsumeRulePO.setName(vo.getName());
        budgetConsumeRulePO.setRuleType(vo.getRuleType());
        budgetConsumeRulePO.setRuleDescription(vo.getRuleDescription());
        budgetConsumeRulePO.setRuleStatus(vo.getRuleStatus());
        budgetConsumeRulePO.setApiName(vo.getApiName());
        budgetConsumeRulePO.setRecordType(vo.getRecordType());
        budgetConsumeRulePO.setDeductType(vo.getDeductType());
        budgetConsumeRulePO.setDeductApiName(vo.getDeductApiName());
        budgetConsumeRulePO.setDeductRecordType(vo.getDeductRecordType());
        budgetConsumeRulePO.setBudgetMethod(vo.getBudgetMethod());
        budgetConsumeRulePO.setOverDeductFlag(Boolean.TRUE.equals(vo.getOverDeductFlag()));
        budgetConsumeRulePO.setReleaseStatus(vo.getReleaseStatus());
        budgetConsumeRulePO.setProvisionStatus(vo.getProvisionStatus());
        if (CollectionUtils.isEmpty(vo.getReleaseList())){
            budgetConsumeRulePO.setReleaseList(new ArrayList<>());
        }else {
            budgetConsumeRulePO.setReleaseList(vo.getReleaseList().stream().map(ReleaseBusinessEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getRuleTypeNodes())) {
            budgetConsumeRulePO.setRuleTypeNodes(new ArrayList<>());
        } else {
            budgetConsumeRulePO.setRuleTypeNodes(vo.getRuleTypeNodes().stream().map(BudgetRuleTypeNodeEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getBudgetTableAutomaticNodes())) {
            budgetConsumeRulePO.setBudgetTableAutomaticNodes(new ArrayList<>());
        } else {
            budgetConsumeRulePO.setBudgetTableAutomaticNodes(vo.getBudgetTableAutomaticNodes().stream().map(BudgetTableAutomaticNodeEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getBudgetTableManualNodes())) {
            budgetConsumeRulePO.setBudgetTableManualNodes(new ArrayList<>());
        } else {
            budgetConsumeRulePO.setBudgetTableManualNodes(vo.getBudgetTableManualNodes().stream().map(BudgetTableManualNodeEntity::fromVO).collect(Collectors.toList()));
        }
        return budgetConsumeRulePO;

    }

    public static BudgetNewConsumeRuleVO toVO(BudgetNewConsumeRulePO po) {
        if (po == null) {
            return null;
        }
        BudgetNewConsumeRuleVO budgetConsumeRuleVO = new BudgetNewConsumeRuleVO();
        budgetConsumeRuleVO.setId(po.getId().toString());
        budgetConsumeRuleVO.setName(po.getName());
        budgetConsumeRuleVO.setRuleType(po.getRuleType());
        budgetConsumeRuleVO.setRuleDescription(po.getRuleDescription());
        budgetConsumeRuleVO.setRuleStatus(po.getRuleStatus());
        budgetConsumeRuleVO.setApiName(po.getApiName());
        budgetConsumeRuleVO.setRecordType(po.getRecordType());
        budgetConsumeRuleVO.setDeductType(po.getDeductType());
        budgetConsumeRuleVO.setDeductApiName(po.getDeductApiName());
        budgetConsumeRuleVO.setDeductRecordType(po.getDeductRecordType());
        budgetConsumeRuleVO.setBudgetMethod(po.getBudgetMethod());
        budgetConsumeRuleVO.setOverDeductFlag(po.getOverDeductFlag());
        budgetConsumeRuleVO.setReleaseStatus(po.getReleaseStatus());
        budgetConsumeRuleVO.setProvisionStatus(po.getProvisionStatus());
        if (CollectionUtils.isEmpty(po.getReleaseList())){
            budgetConsumeRuleVO.setReleaseList(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setReleaseList(po.getReleaseList().stream().map(ReleaseBusinessEntity::toVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(po.getRuleTypeNodes())) {
            budgetConsumeRuleVO.setRuleTypeNodes(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setRuleTypeNodes(po.getRuleTypeNodes().stream().map(BudgetRuleTypeNodeEntity::toVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(po.getBudgetTableAutomaticNodes())) {
            budgetConsumeRuleVO.setBudgetTableAutomaticNodes(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setBudgetTableAutomaticNodes(po.getBudgetTableAutomaticNodes().stream().map(BudgetTableAutomaticNodeEntity::toVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(po.getBudgetTableManualNodes())) {
            budgetConsumeRuleVO.setBudgetTableManualNodes(new ArrayList<>());
        } else {
            budgetConsumeRuleVO.setBudgetTableManualNodes(po.getBudgetTableManualNodes().stream().map(BudgetTableManualNodeEntity::toVO).collect(Collectors.toList()));
        }
        budgetConsumeRuleVO.setTenantId(po.getTenantId());
        budgetConsumeRuleVO.setCreator(po.getCreator());
        budgetConsumeRuleVO.setCreateTime(po.getCreateTime());
        budgetConsumeRuleVO.setLastUpdater(po.getLastUpdater());
        budgetConsumeRuleVO.setLastUpdateTime(po.getLastUpdateTime());
        budgetConsumeRuleVO.setDeleted(po.isDeleted());
        budgetConsumeRuleVO.setVersion(po.getVersion());
        return budgetConsumeRuleVO;
    }

    public static BudgetNewConsumeRulePO fromEditVO(BudgetNewConsumeRulePO po, BudgetNewConsumeRuleVO vo) {
        if (vo == null) {
            return null;
        }
        po.setId(po.getOriginalId());
        po.setUniqueId(vo.getId());
        po.setName(vo.getName());
        po.setRuleType(vo.getRuleType());
        po.setRuleDescription(vo.getRuleDescription());
        po.setRuleStatus(vo.getRuleStatus());
        po.setApiName(vo.getApiName());
        po.setRecordType(vo.getRecordType());
        po.setDeductType(vo.getDeductType());
        po.setDeductApiName(vo.getDeductApiName());
        po.setDeductRecordType(vo.getDeductRecordType());
        po.setBudgetMethod(vo.getBudgetMethod());
        po.setOverDeductFlag(vo.getOverDeductFlag());
        po.setReleaseStatus(vo.getReleaseStatus());
        po.setProvisionStatus(vo.getProvisionStatus());
        if (CollectionUtils.isEmpty(vo.getReleaseList())){
            po.setReleaseList(new ArrayList<>());
        }else {
            po.setReleaseList(vo.getReleaseList().stream().map(ReleaseBusinessEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getRuleTypeNodes())) {
            po.setRuleTypeNodes(new ArrayList<>());
        } else {
            po.setRuleTypeNodes(vo.getRuleTypeNodes().stream().map(BudgetRuleTypeNodeEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getBudgetTableAutomaticNodes())) {
            po.setBudgetTableAutomaticNodes(new ArrayList<>());
        } else {
            po.setBudgetTableAutomaticNodes(vo.getBudgetTableAutomaticNodes().stream().map(BudgetTableAutomaticNodeEntity::fromVO).collect(Collectors.toList()));
        }
        if (CollectionUtils.isEmpty(vo.getBudgetTableManualNodes())) {
            po.setBudgetTableManualNodes(new ArrayList<>());
        } else {
            po.setBudgetTableManualNodes(vo.getBudgetTableManualNodes().stream().map(BudgetTableManualNodeEntity::fromVO).collect(Collectors.toList()));
        }
        return po;
    }
}