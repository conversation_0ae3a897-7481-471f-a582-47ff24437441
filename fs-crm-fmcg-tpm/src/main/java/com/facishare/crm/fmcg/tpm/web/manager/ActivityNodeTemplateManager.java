package com.facishare.crm.fmcg.tpm.web.manager;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.business.TPM2Service;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityNodeTemplateDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.ActivityTypeDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.web.contract.model.ActivityNodeTemplateVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.CommonVO;
import com.facishare.crm.fmcg.tpm.web.contract.model.IActivityNodeTemplate;
import com.facishare.crm.fmcg.tpm.web.manager.abstraction.IActivityNodeTemplateManager;
import com.facishare.crm.fmcg.tpm.web.manager.dto.ApiNameCheckResult;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/17 15:16
 */
//IgnoreI18nFile
@Slf4j
@Component("activityNodeTemplateManager")
public class ActivityNodeTemplateManager implements IActivityNodeTemplateManager {

    protected static final Set<String> PRE_DEFINE_API_NAMES = Sets.newHashSet(
            ApiNames.TPM_ACTIVITY_OBJ,
            ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ,
            ApiNames.TPM_ACTIVITY_PROOF_OBJ,
            ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ,
            ApiNames.TPM_DEALER_ACTIVITY_COST
    );

    @Resource
    private DescribeLogicService describeLogicService;

    @Resource
    private ActivityNodeTemplateDAO activityNodeTemplateDAO;

    @Resource
    private ActivityTypeDAO activityTypeDAO;

    @Resource
    private TPM2Service tpm2Service;

    @Override
    public void fillObjectDisplayName(String tenantId, List<ActivityNodeTemplateVO> data) {
        List<String> objectApiNames = data.stream().map(ActivityNodeTemplateVO::getObjectApiName).distinct().collect(Collectors.toList());
        Map<String, String> objectDisplayNameMap = describeLogicService.findObjects(tenantId, objectApiNames).entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, v -> v.getValue().getDisplayName()));
        for (ActivityNodeTemplateVO datum : data) {
            datum.setObjectDisplayName(objectDisplayNameMap.getOrDefault(datum.getObjectApiName(), "--"));
        }
    }

    @Override
    public void fillRelatedActivityTypes(String tenantId, List<ActivityNodeTemplateVO> data) {
        List<String> templateIds = data.stream().map(CommonVO::getId).collect(Collectors.toList());
        List<ActivityTypePO> activityTypes = activityTypeDAO.queryByNodeTemplateId(tenantId, templateIds);

        for (ActivityTypePO type : activityTypes) {
            for (ActivityNodeTemplateVO datum : data) {
                if (type.getActivityNodes().stream().anyMatch(a -> a.getTemplateId().equals(datum.getId()))) {
                    datum.getRelatedActivityTypes().add(type.getName());
                }
            }
        }
    }

    @Override
    public void basicInformationValidation(String tenantId, IActivityNodeTemplate nodeTemplate) {
        this.basicInformationValidation(tenantId, nodeTemplate.getName(), nodeTemplate.getDescription());
    }

    @Override
    public void basicInformationValidation(String tenantId, String id, IActivityNodeTemplate nodeTemplate) {
        this.basicInformationValidation(tenantId, id, nodeTemplate.getName(), nodeTemplate.getDescription());
    }

    @Override
    public void apiNameValidation(String tenantId, IActivityNodeTemplate nodeTemplate) {
        this.duplicateApiNameValidation(tenantId, nodeTemplate.getObjectApiName());
        this.apiNameValidation(tenantId, nodeTemplate.getObjectApiName(), nodeTemplate.getReferenceFieldApiName());
    }

    @Override
    public void apiNameValidation(String tenantId, String id, IActivityNodeTemplate nodeTemplate) {
        this.duplicateApiNameValidation(tenantId, id, nodeTemplate.getObjectApiName());
        this.apiNameValidation(tenantId, id, nodeTemplate.getObjectApiName(), nodeTemplate.getReferenceFieldApiName());
    }

    @Override
    public ApiNameCheckResult apiNameCheck(String tenantId, String objectApiName, String referenceFieldApiName) {
        IObjectDescribe describe = describeLogicService.findObject(tenantId, objectApiName);
        if (Objects.isNull(describe)) {
            return ApiNameCheckResult.OBJECT_API_NAME_VALIDATE_FAIL;
        }
        if (!ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName)
                && !ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(objectApiName)) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(referenceFieldApiName);
            if (Objects.isNull(fieldDescribe) ||
                    !(fieldDescribe instanceof ObjectReferenceFieldDescribe) ||
                    !ApiNames.TPM_ACTIVITY_OBJ.equals(((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName())) {
                return ApiNameCheckResult.REFERENCE_FIELD_API_NAME_VALIDATE_FAIL;
            }
        }
        return ApiNameCheckResult.SUCCESS;
    }

    @Override
    public boolean isUsed(String tenantId, String id) {
        return activityTypeDAO.templateIsUsed(tenantId, id);
    }

    @Override
    public boolean isSystem(String tenantId, String id) {
        return PackageType.SYSTEM.value().equals(activityNodeTemplateDAO.get(tenantId, id).getPackageType());
    }

    @Override
    public void tryInitSystemTemplate(String tenantId, int operator) {
        if (!tpm2Service.existTPMLicenseTenant(Integer.valueOf(tenantId))) {
            log.info(" tryInitSystemTemplate tpm license is invalid or is not open. tenantId : {}", tenantId);
        } else {
            boolean storeWriteOff = isStoreWriteOff(tenantId);
            activityNodeTemplateDAO.initSystemTemplate(tenantId, operator, storeWriteOff);
        }

        if (!tpm2Service.existTPMCodeLicenseTenant(Integer.valueOf(tenantId))) {
            log.info(" tryInitSystemTemplate tpm code license is invalid or is not open. tenantId : {}", tenantId);
        }else {
            activityNodeTemplateDAO.initSystemTPModeTemplate(tenantId, operator);
        }
    }

    private boolean isStoreWriteOff(String tenantId) {
        boolean storeWriteOff = false;
        try {
            IObjectDescribe objectDescribe = describeLogicService.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
            if (objectDescribe != null) {
                storeWriteOff = true;
            }
        } catch (Exception e) {
            log.info("findObject tenantId is {} find error.", tenantId);
        }
        return storeWriteOff;
    }

    @Override
    public String checkValidation(String tenantId, ActivityNodeTemplatePO nodeTemplate) {
        IObjectDescribe describe = describeLogicService.findObject(tenantId, nodeTemplate.getObjectApiName());

        String errorMessage = "";
        if (Objects.isNull(describe)) {
            nodeTemplate.setExceptionStatus(ExceptionStatusType.ERROR.value());
            errorMessage = String.format("[%s]活动节点中的【%s】对象不存在", nodeTemplate.getName(), nodeTemplate.getObjectApiName());
            return errorMessage;
        }
        if (Boolean.FALSE.equals(describe.isActive())) {
            errorMessage = String.format("[%s]活动节点中的【%s】对象被禁用", nodeTemplate.getName(), describe.getDisplayName());
            nodeTemplate.setExceptionStatus(ExceptionStatusType.ERROR.value());
        }

        if (!ApiNames.TPM_ACTIVITY_OBJ.equals(nodeTemplate.getObjectApiName()) && !ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(nodeTemplate.getObjectApiName())) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(nodeTemplate.getReferenceFieldApiName());

            boolean hasActivity = false;
            if (Objects.nonNull(fieldDescribe)
                    && fieldDescribe instanceof ObjectReferenceFieldDescribe
                    && ApiNames.TPM_ACTIVITY_OBJ.equals(((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName())) {
                if (Boolean.FALSE.equals(fieldDescribe.isActive())) {
                    errorMessage = String.format("[%s]活动节点中的[%s]关联[活动申请]对象字段被禁用，" +
                                    "导致该节点暂时无法使用。节点异常，会影响活动类型及≈的正常使用，请及时处理",
                            nodeTemplate.getName(), describe.getDisplayName());

                    NodeExceptionInfoEntity nodeExceptionInfo = nodeTemplate.getNodeExceptionInfo();
                    nodeExceptionInfo.setObjectReferenceActivity(ExceptionStatusType.ERROR.value());
                    nodeTemplate.setNodeExceptionInfo(nodeExceptionInfo);
                    nodeTemplate.setExceptionStatus(ExceptionStatusType.ERROR.value());
                }
                hasActivity = true;
            }

            if (!hasActivity) {
                errorMessage = String.format("[%s]活动节点中的[%s]关联[活动申请]对象字段不存在，" +
                                "导致该节点暂时无法使用。节点异常，会影响活动类型及活动申请的正常使用，请及时处理",
                        nodeTemplate.getName(), describe.getDisplayName());
                NodeExceptionInfoEntity nodeExceptionInfo = nodeTemplate.getNodeExceptionInfo();
                nodeExceptionInfo.setObjectReferenceActivity(ExceptionStatusType.ERROR.value());
                nodeTemplate.setNodeExceptionInfo(nodeExceptionInfo);
                nodeTemplate.setExceptionStatus(ExceptionStatusType.ERROR.value());
            }
        }
        return errorMessage;
    }

    private void duplicateApiNameValidation(String tenantId, String id, String apiName) {
        if (activityNodeTemplateDAO.isDuplicateApiName(tenantId, id, apiName)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NAME_DUPLICATE_ERROR));
        }
    }

    private void apiNameValidation(String tenantId, String id, String objectApiName, String referenceFieldApiName) {
        ActivityNodeTemplatePO po = activityNodeTemplateDAO.get(tenantId, id);
        if (!po.getPackageType().equals(PackageType.SYSTEM.value()) && PRE_DEFINE_API_NAMES.contains(objectApiName)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_OBJECT_API_NAME_ERROR));
        }
        this.apiNameValidation(tenantId, objectApiName, referenceFieldApiName);
    }

    private void duplicateApiNameValidation(String tenantId, String apiName) {
        if (activityNodeTemplateDAO.isDuplicateApiName(tenantId, apiName)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NAME_DUPLICATE_ERROR));
        }
    }

    private void apiNameValidation(String tenantId, String objectApiName, String referenceFieldApiName) {
        IObjectDescribe describe = describeLogicService.findObject(tenantId, objectApiName);
        if (Objects.isNull(describe)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_OBJECT_API_NAME_ERROR));
        }
        if (!ApiNames.TPM_ACTIVITY_OBJ.equals(objectApiName)
                && !ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(objectApiName)) {
            IFieldDescribe fieldDescribe = describe.getFieldDescribe(referenceFieldApiName);
            if (Objects.isNull(fieldDescribe) ||
                    !(fieldDescribe instanceof ObjectReferenceFieldDescribe) ||
                    !ApiNames.TPM_ACTIVITY_OBJ.equals(((ObjectReferenceFieldDescribe) fieldDescribe).getTargetApiName())) {
                throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_REFERENCE_FIELD_API_NAME_ERROR));
            }
        }
    }


    private void basicInformationValidation(String tenantId, String id, String name, String description) {

        nameValidation(name);
        duplicateNameValidation(tenantId, id, name);
        descriptionValidation(description);
    }

    private void duplicateNameValidation(String tenantId, String id, String name) {
        if (activityNodeTemplateDAO.isDuplicateName(tenantId, id, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NAME_DUPLICATE_ERROR));
        }
    }

    private void basicInformationValidation(
            String tenantId,
            String name,
            String description) {

        nameValidation(name);
        duplicateNameValidation(tenantId, name);
        descriptionValidation(description);
    }

    private void nameValidation(String name) {
        if (Strings.isNullOrEmpty(name) ||
                name.length() < 2 ||
                name.length() > 200) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NAME_FORMAT_ERROR));
        }
    }

    private void duplicateNameValidation(String tenantId, String name) {
        if (activityNodeTemplateDAO.isDuplicateName(tenantId, name)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_NAME_DUPLICATE_ERROR));
        }
    }

    private void descriptionValidation(String description) {
        if (!Strings.isNullOrEmpty(description) && description.length() > 2000) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_NODE_TEMPLATE_DESCRIPTION_FORMAT_ERROR));
        }
    }
}
