package com.facishare.crm.fmcg.tpm.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ButtonDocument;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/6/30 上午11:18
 */
public class TPMStoreWriteOffObjListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {

        buttonFilter(arg, result);
        return super.after(arg, result);
    }

    private void buttonFilter(Arg arg, Result result) {
        log.info("arg layoutAgentType is {}", arg.getLayoutAgentType());
        if (!"mobile".equals(arg.getLayoutAgentType())) {
            List<JSONObject> buttons = (List<JSONObject>) result.getLayout().get("buttons");
            List<String> removeList = new ArrayList<>();
            // 是否允许导入。
            if (!TPMGrayUtils.isAllowStoreWriteOffImport(controllerContext.getTenantId())) {
                removeList.add("Import");
            }
            if ("list".equals(arg.getLayoutType())) {
                removeList.add("Add");
            }
            result.getLayout().put("buttons", buttons.stream().filter(v -> !removeList.contains(v.getString("action"))).collect(Collectors.toList()));
        } else {
            List<String> removeList = Lists.newArrayList("Import", "Add", "Edit", ObjectAction.COST_WRITE_OFF.getActionCode());
            ArrayList buttons = (ArrayList) result.getLayout().get("buttons");
            List<ButtonDocument> buttonDocuments = result.getButtons();
            buttons.removeIf(button -> {
                Map btn = (Map) (button);
                return removeList.contains(btn.get("action"));
            });
            buttonDocuments.removeIf(bs -> ObjectAction.COST_WRITE_OFF.getActionCode().equals(bs.get("action")));
            buttonDocuments.removeIf(bs -> "AsyncBulkCostWriteOff".equals(bs.get("action")));
        }
    }
}
