package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.*;

/**
 * description:
 * createTime: 2023/3/20 14:27
 *
 * <AUTHOR>
 */
public interface IConfigService {

    AddConfig.Result save(AddConfig.Arg arg);

    ListConfig.Result list(ListConfig.Arg arg);

    GetConfig.Result get(GetConfig.Arg arg);

    GetConfigDescribe.Result getDescribe(GetConfigDescribe.Arg arg);

    CheckRewardTagUsage.Result checkRewardTagUsage(CheckRewardTagUsage.Arg arg);


}
