package com.facishare.crm.fmcg.tpm.action;

import com.facishare.common.parallel.ParallelUtils;
import com.facishare.crm.fmcg.tpm.business.abstraction.ICarryForwardActionService;
import com.facishare.crm.fmcg.tpm.utils.TraceUtil;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@SuppressWarnings("unused")
public class TPMBudgetCarryForwardObjFlowCompletedAction extends StandardFlowCompletedAction {

    private final ICarryForwardActionService carryForwardActionService = SpringUtil.getContext().getBean(ICarryForwardActionService.class);

    @Override
    protected Result doAct(Arg arg) {
        Result inner = super.doAct(arg);

        String approvalTraceId = TraceUtil.getApprovalCallbackTraceId(this.arg.getCallbackData());
        String businessTraceId = TraceUtil.getBusinessCallbackTraceId(this.arg.getCallbackData());

        log.info("approve trace id : {}, business trace id : {}", approvalTraceId, businessTraceId);

        if (!Strings.isNullOrEmpty(approvalTraceId) && !Strings.isNullOrEmpty(businessTraceId)) {
            ParallelUtils.createParallelTask().submit(() -> {
                if (arg.isPass()) {
                    carryForwardActionService.carryForward(actionContext.getUser(), arg.getDataId());
                } else {
                    carryForwardActionService.unfreeze(actionContext.getUser(), arg.getDataId());
                }
            }).run();
        } else {
            log.error(String.format("carry forward completed action trace id error - data id : %s, approval : %s, business : %s.", arg.getDataId(), approvalTraceId, businessTraceId));
        }

        return inner;
    }
}