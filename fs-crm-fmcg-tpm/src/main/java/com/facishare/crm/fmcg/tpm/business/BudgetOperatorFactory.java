package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IBudgetOperator;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.SpringUtil;
import lombok.experimental.UtilityClass;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/8/3 11:02
 */
@UtilityClass
public class BudgetOperatorFactory {

    public static IBudgetOperator initOperator(
            BizType biz,
            User user,
            String budgetAccountId,
            String businessTraceId,
            String approvalTraceId) {
        IBudgetOperator bean = SpringUtil.getContext().getBean(IBudgetOperator.class);
        bean.init(biz, user, budgetAccountId, businessTraceId, approvalTraceId);
        return bean;
    }

    public static IBudgetOperator initOperator(
            BizType biz,
            User user,
            String budgetAccountId,
            String businessTraceId,
            String approvalTraceId,
            IObjectData relatedObjectData) {
        IBudgetOperator bean = SpringUtil.getContext().getBean(IBudgetOperator.class);
        bean.init(biz, user, budgetAccountId, businessTraceId, approvalTraceId, relatedObjectData);
        return bean;
    }

    public static IBudgetOperator initOperator(
            BizType biz,
            User user,
            String budgetAccountId,
            String businessTraceId,
            String approvalTraceId,
            IObjectData relatedObjectData,
            boolean isValidateBudgetEnable,
            boolean isValidateWhat) {
        IBudgetOperator bean = SpringUtil.getContext().getBean(IBudgetOperator.class);
        bean.init(biz, user, budgetAccountId, businessTraceId, approvalTraceId, relatedObjectData, isValidateBudgetEnable,isValidateWhat);
        return bean;
    }
}