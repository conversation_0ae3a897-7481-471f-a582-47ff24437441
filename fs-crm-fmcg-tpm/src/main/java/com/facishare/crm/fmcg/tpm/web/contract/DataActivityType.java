package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/8/8 下午4:21
 */
public interface DataActivityType {

    @Data
    @ToString
    class Arg implements Serializable {

        @JSONField(name = "object_api_name")
        @JsonProperty(value = "object_api_name")
        @SerializedName("object_api_name")
        private String objectApiName;

    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        private Map<String, ObjectFilter> data;
    }

    @Data
    @ToString
    class ObjectFilter {

        private String name;

        //true 包含  false 不包含
        @JSONField(name = "is_include_unified_case")
        @JsonProperty(value = "is_include_unified_case")
        @SerializedName("is_include_unified_case")
        private Boolean isIncludeUnifiedCase;

    }


}
