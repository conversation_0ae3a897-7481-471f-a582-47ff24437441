package com.facishare.crm.fmcg.tpm.business;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.utils.SearchQueryUtil;
import com.facishare.crm.fmcg.tpm.business.abstraction.*;
import com.facishare.crm.fmcg.tpm.business.dto.ConsumeRuleBudgetAmountDTO;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetTypeDAO;
import com.facishare.crm.fmcg.tpm.utils.*;
import com.facishare.crm.fmcg.tpm.web.manager.BudgetTypeManager;
import com.facishare.paas.I18N;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.tpm.api.consume.EndConsume;
import com.facishare.crm.fmcg.tpm.api.consume.PreValidateConsumeRuleAmount;
import com.facishare.crm.fmcg.tpm.api.consume.Reconsume;
import com.facishare.crm.fmcg.tpm.api.plugin.DealApprovalAction;
import com.facishare.crm.fmcg.tpm.api.plugin.DealTriggerAction;
import com.facishare.crm.fmcg.tpm.business.dto.BudgetConsumeSession;
import com.facishare.crm.fmcg.tpm.business.enums.BizType;
import com.facishare.crm.fmcg.tpm.business.enums.BudgetDetailOperateMark;
import com.facishare.crm.fmcg.tpm.business.enums.MainType;
import com.facishare.crm.fmcg.tpm.dao.mongo.BudgetNewConsumeRuleDAO;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.*;
import com.facishare.crm.fmcg.tpm.service.TransactionProxy;
import com.facishare.crm.fmcg.tpm.web.condition.ConditionAdapter;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.dto.FieldResult;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.api.IdGenerator;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/10 下午3:00
 */
//IgnoreI18nFile
@Slf4j
@Service
public class BudgetConsumeV2Service implements IBudgetConsumeV2Service {


    private static List<String> CHANGE_OPERATE_LIST = Lists.newArrayList("Add", "Edit", "Abolish", "Delete");

    private static Set<String> APPROVAL_TRIGGER_CODE = Sets.newHashSet("pass", "reject", "cancel");

    private static final String RELEASE_AMOUNT_TEMPLATE = "ReleaseAmountTemplate:%s";

    @Resource
    private ServiceFacade serviceFacade;

    @Resource
    private BudgetNewConsumeRuleDAO budgetNewConsumeRuleDAO;

    @Resource
    private BudgetAccountService budgetAccountService;

    @Resource
    private ConditionAdapter conditionAdapter;

    @Resource
    private TransactionProxy transactionProxy;

    @Resource
    private BudgetAccountDetailService budgetAccountDetailService;

    @Resource
    private RedisLockService redisLockService;

    @Resource
    private IBudgetOccupyService budgetOccupyService;

    @Resource
    private BudgetCalculateService budgetCalculateService;

    @Resource
    private IFiscalTimeService fiscalTimeService;

    @Resource
    private BudgetTypeDAO budgetTypeDAO;

    @Resource
    private IBudgetCompareService budgetCompareService;

    @Resource
    private IBudgetSubjectService budgetSubjectService;
    @Resource
    private IProductCategoryService productCategoryService;


    @Override
    public void dealActionTriggerBefore(DealTriggerAction.Arg arg) {
        switch (arg.getActionCode()) {
            case "Add":
                preValidateAndLockMoney(arg);
                break;
            case "Edit":
                IObjectData masterData = arg.getMasterData();
                if (CommonFields.LIFE_STATUS__INEFFECTIVE.equals(masterData.get(CommonFields.LIFE_STATUS, String.class))) {
                    BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
                    session.setEditInfectiveFlag();
                    preValidateAndLockMoney(arg);
                } else {
                    forbidEditImportantValue(arg);
                }
                break;
            case "IncrementUpdate":
                forbidEditImportantValue(arg);
                break;
            case "BulkInvalid":
                checkInvalidAction(arg);
                break;
            case "Invalid":
                checkInvalidAction(arg);
                break;
            default:
        }
    }

    private void checkInvalidAction(DealTriggerAction.Arg arg) {
        if (arg.getMasterData() == null) {
            invalidValidateBefore(arg.getUser().getTenantId(), arg.getObjectList());
        } else {
            invalidValidateBefore(arg.getUser().getTenantId(), Lists.newArrayList(arg.getMasterData()));
        }
    }

    @Override
    public void dealActionTriggerAfter(DealTriggerAction.Arg arg) {
        switch (arg.getActionCode()) {
            case "Add":
                cleanOccupyAndDealNoApprovalEvent(arg);
                break;
            case "Edit":
                IObjectData masterData = arg.getMasterData();
                BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
                if (session.isEditInfective()) {
                    cleanOccupyAndDealNoApprovalEvent(arg);
                }
                break;
            case "IncrementUpdate":
                break;
            case "Invalid":
                if (CommonFields.LIFE_STATUS__INVALID.equals(arg.getMasterData().get(CommonFields.LIFE_STATUS, String.class))) {
                    dealReturnFrozenMoney(arg.getUser().getTenantId(), Lists.newArrayList(new Pair<>(arg.getMasterData(), arg.getMasterData())), BizType.INVALID_BACK);
                } else {
                    log.info("life status:{}", arg.getMasterData().get(CommonFields.LIFE_STATUS, String.class));
                }
                break;
            case "BulkInvalid":
                List<IObjectData> dataList = new ArrayList<>(arg.getObjectList());
                dataList = dataList.stream().filter(data -> data.get(CommonFields.LIFE_STATUS, String.class).equals(CommonFields.LIFE_STATUS__INVALID)).collect(Collectors.toList());
                dealReturnFrozenMoney(arg.getUser().getTenantId(), dataList.stream().map(v -> new Pair<>(v, v)).collect(Collectors.toList()), BizType.INVALID_BACK);
                break;
            default:
        }
    }

    @Override
    public void dealActionFinallyDo(DealTriggerAction.Arg arg) {
        switch (arg.getActionCode()) {
            case "Add":
                cleanOccupyInFinallyDo(arg);
                break;
            case "Edit":
                IObjectData masterData = arg.getMasterData();
                BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
                if (session.isEditInfective()) {
                    cleanOccupyInFinallyDo(arg);
                }
                break;
            case "IncrementUpdate":
                break;
            case "Invalid":
                break;
            case "BulkInvalid":
                break;
            default:
        }
    }

    @Override
    public void dealFlowCompletedBefore(DealApprovalAction.Arg arg) {
        if (APPROVAL_TRIGGER_CODE.contains(arg.getApprovalStatus()) && ApprovalFlowTriggerType.CREATE.getTriggerTypeCode().equals(arg.getApprovalType())) {
            IObjectData masterData = arg.getMasterData();
            User systemUser = User.systemUser(arg.getUser().getTenantId());
            String tenantId = arg.getUser().getTenantId();

            BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
            if (consumeRulePO == null) {
                log.info("没有规则");
                return;
            }

            Map<String, List<IObjectData>> detailsMap = getDetailsMap(arg.getDetailMap(), masterData, systemUser);
            //merge change
            mergeApprovalChange(masterData, detailsMap, arg.getCallbackMap(), arg.getDetailChangeMap());
            BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
            log.debug("merge after master:{},detail:{}", masterData, detailsMap);
            session.setExecuteStage("after");

            if ("pass".equals(arg.getApprovalStatus())) {
                dealInApprovalPassEvent(tenantId, consumeRulePO, masterData, detailsMap, arg.getCallbackMap(), session);
            } else {
                dealInApprovalRejectEvent(tenantId, consumeRulePO, masterData, detailsMap, arg.getCallbackMap(), session);
            }
            session.setExecuteStage("before");
        }
    }

    @Override
    public void dealFlowCompletedAfter(DealApprovalAction.Arg arg) {
        if ("pass".equals(arg.getApprovalStatus())) {
            String tenantId = arg.getUser().getTenantId();
            if (ApprovalFlowTriggerType.INVALID.getTriggerTypeCode().equals(arg.getApprovalType())) {
                dealReturnFrozenMoney(tenantId, Lists.newArrayList(new Pair<>(arg.getMasterData(), arg.getMasterData())), BizType.INVALID_BACK);
            }
            if (ApprovalFlowTriggerType.CLOSE_ACTIVITY_AGREEMENT.getTriggerTypeCode().equals(arg.getApprovalType())) {
                //middleRelease(User.systemUser(tenantId), arg.getMasterData().getDescribeApiName(), arg.getMasterData().getId(), ActionModeType.AGREEMENT_ABANDON.getKey());
            }
        }
    }

    private void dealReturnFrozenMoney(String tenantId, List<Pair<IObjectData, IObjectData>> dataList, BizType bizType) {
        User sysUser = User.systemUser(tenantId);
        dataList.forEach(pair -> {
            IObjectData masterData = pair.getFirst();
            IObjectData frozeData = pair.getSecond();
            BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
            if (consumeRulePO == null) {
                log.info("没有规则");
                return;
            }
            if (!consumeRulePO.getApiName().equals(masterData.getDescribeApiName()) && bizType == BizType.INVALID_BACK) {
                log.info("非冻结对象作废。");
                return;
            }
            //中间节点的重新获取下冻结数据
            if (CollectionUtils.isNotEmpty(consumeRulePO.getReleaseList()) && consumeRulePO.getReleaseList().stream().anyMatch(release -> release.getReleaseApiName().equals(masterData.getDescribeApiName()))) {
                BudgetTableManualNodeEntity manualNodeEntity = consumeRulePO.getBudgetTableManualNodes().get(0);
                BudgetTableReleaseEntity releaseEntity = manualNodeEntity.getBudgetTableReleaseEntity().stream().filter(entity -> entity.getApiName().equals(masterData.getDescribeApiName()) && entity.getRecordType().equals(masterData.getRecordType())).findFirst().orElse(null);
                if (releaseEntity == null) {
                    throw new ValidateException("找不到释放节点的数据。");
                }
                if (Strings.isNullOrEmpty(releaseEntity.getRelationField())) {
                    frozeData = masterData;
                } else {
                    String frozenId = masterData.get(releaseEntity.getRelationField(), String.class);
                    if (Strings.isNullOrEmpty(frozenId)) {
                        throw new ValidateException(I18N.text(I18NEnums.RELEASE_NODE_NOT_RELATED_FROZEN_DATA.getCode()));
                    }
                    frozeData = serviceFacade.findObjectDataIncludeDeleted(sysUser, frozenId, consumeRulePO.getApiName());
                }
            }
            String businessId = budgetAccountDetailService.queryConsumeDetailBusinessId(sysUser, frozeData.getDescribeApiName(), frozeData.getId(), consumeRulePO.getId().toString(), ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()));
            String traceId = TraceUtil.initApprovalTraceId();
            String releaseKey = String.format(RELEASE_AMOUNT_TEMPLATE, businessId);
            String releaseValue = UUID.randomUUID().toString();
            Map<String, IBudgetOperator> budgetOperatorMap = new HashMap<>();
            if (Strings.isNullOrEmpty(businessId)) {
                log.info("没有需要释放的。");
                return;
            }
            try {
                if (redisLockService.tryLock(releaseKey, releaseValue)) {
                    Map<String, BigDecimal> unfrozenAmountMap = budgetAccountDetailService.queryFrozenAmountByBusinessId(sysUser, businessId);
                    // 确保只在预提功能启用时才查询预提信息
                    List<IObjectData> withholdingDetails = ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()) ?
                            queryWithholdingDetailsByBusinessId(sysUser, frozeData, businessId, consumeRulePO) : Lists.newArrayList();
                    unfrozenAmountMap.keySet().forEach(budgetId -> budgetOperatorMap.put(budgetId, BudgetOperatorFactory.initOperator(BizType.CONSUME, sysUser, budgetId, businessId, traceId, masterData)));
                    budgetOperatorMap.values().forEach(iBudgetOperator -> {
                        if (!iBudgetOperator.tryLock()) {
                            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_1));
                        }
                    });
                    transactionProxy.run(() -> allRefund(sysUser, businessId, unfrozenAmountMap, budgetOperatorMap, withholdingDetails, bizType));
                }
            } finally {
                redisLockService.unLock(releaseKey, releaseValue);
                budgetOperatorMap.values().forEach(IBudgetOperator::unlock);
            }
        });
    }

    private IObjectData getFrozenDataFromReleaseData(String tenantId, String frozenApiName, ReleaseNodeEntity releaseNode, IObjectData masterData) {
        String frozenDataId = masterData.get(releaseNode.getReferencedFieldApiName(), String.class);
        if (Strings.isNullOrEmpty(frozenDataId)) {
            log.info("关联的冻结数据为空。masterData:{}", masterData);
            return null;
        }
        return serviceFacade.findObjectData(User.systemUser(tenantId), frozenDataId, frozenApiName);
    }

    private List<IObjectData> queryWithholdingDetailsByBusinessId(User sysUser, IObjectData masterData, String businessId, BudgetNewConsumeRulePO consumeRulePO) {
        // 只有启用预提功能时才查询预提数据
        if (!ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus())) {
            return Lists.newArrayList();
        }

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMBudgetProvisionObjFields.BIZ_TRACE_ID, Operator.EQ, Lists.newArrayList(businessId)),
                SearchQueryUtil.filter(TPMBudgetProvisionObjFields.PROVISION_STATUS, Operator.IN, Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.IN_EFFECT, TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL))
        ));
        if (masterData != null) {
            query.getFilters().addAll(Lists.newArrayList(SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(masterData.getDescribeApiName())),
                    SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(masterData.getId()))));
        }
        return CommonUtils.queryData(serviceFacade, sysUser, ApiNames.TPM_BUDGET_PROVISION_OBJ, query, Lists.newArrayList(CommonFields.ID, CommonFields.OBJECT_DESCRIBE_API_NAME, CommonFields.TENANT_ID));
    }

    private boolean existsWithholdingDetail(User user, IObjectData masterData, BudgetNewConsumeRulePO consumeRulePO) {
        // 只有启用预提功能时才查询预提数据
        if (!ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus())) {
            return false;
        }

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                Lists.newArrayList(SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(masterData.getDescribeApiName())),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(masterData.getId())),
                        SearchQueryUtil.filter(TPMBudgetProvisionObjFields.PROVISION_STATUS, Operator.IN, Lists.newArrayList(TPMBudgetProvisionObjFields.ProvisionStatus.IN_EFFECT, TPMBudgetProvisionObjFields.ProvisionStatus.OCCUPY_FAIL))
                )));
        return CollectionUtils.isNotEmpty(CommonUtils.queryData(serviceFacade, user, ApiNames.TPM_BUDGET_PROVISION_OBJ, query, Lists.newArrayList(CommonFields.ID)));
    }


    @Override
    public void dealFlowCompletedFinallyDo(DealApprovalAction.Arg arg) {
        if (APPROVAL_TRIGGER_CODE.contains(arg.getApprovalStatus()) && ApprovalFlowTriggerType.CREATE.getTriggerTypeCode().equals(arg.getApprovalType())) {
            IObjectData masterData = arg.getMasterData();
            BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
            String lifeStatus = masterData.get(CommonFields.LIFE_STATUS, String.class);
            User systemUser = User.systemUser(arg.getUser().getTenantId());
            //审批正常更新状态
            if (!CommonFields.LIFE_STATUS__UNDER_REVIEW.equals(lifeStatus) && BudgetDetailOperateMark.APPROVAL_COMPLETE.value().equals(session.getOperateMark())) {
                String businessCallbackTraceId = TraceUtil.getBusinessCallbackTraceId(arg.getCallbackMap());
                updateAdvanceDetails2ApprovalCompleted(systemUser, businessCallbackTraceId);
            } else if (CommonFields.LIFE_STATUS__UNDER_REVIEW.equals(lifeStatus)) {
                //审批异常回退明细
                List<String> detailIds = session.getBudgetDetailIds();
                if (CollectionUtils.isNotEmpty(detailIds)) {
                    List<IObjectData> details = serviceFacade.findObjectDataByIdsIgnoreAll(systemUser.getTenantId(), detailIds, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
                    serviceFacade.bulkInvalid(details, systemUser);
                    recalculateBudget(systemUser, details);
                }
            }
            log.info("unlock session lock");
            unlockBySessionMap(session.all());
            session.destroy();
        }
    }

    private void recalculateBudget(User user, List<IObjectData> details) {
        if (!CollectionUtils.isEmpty(details)) {
            Set<String> budgetIdSet = new HashSet<>();
            details.forEach(detail -> budgetIdSet.add(detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class)));
            log.info("recalculateBudgetAmount. ids:{}", budgetIdSet);
            budgetIdSet.forEach(id -> budgetCalculateService.recalculateBudgetAmount(user, id));
        }
    }


    @Override
    public EndConsume.Result endConsume(User user, String describeApiName, String dataId, boolean isForceEnd) {
        String tenantId = user.getTenantId();
        User systemUser = User.systemUser(tenantId);
        String approvalId = TraceUtil.initApprovalTraceId();
        IObjectData masterData = serviceFacade.findObjectDataIncludeDeleted(systemUser, dataId, describeApiName);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(masterData.get(CommonFields.LIFE_STATUS, String.class))) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_0));
        }

        String closeStatus = masterData.get(TPMActivityFields.CLOSE_STATUS, String.class);
        Long closeTime = masterData.get(TPMActivityFields.CLOSE_TIME, Long.class);
        if (closeTime != null || TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            log.info("已经结案。");
            return new EndConsume.Result();
        }
        Map<String, List<IObjectData>> detailsMap = getDetailsMap(null, masterData, systemUser);
        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
        if (consumeRulePO == null) {
            log.info("没有匹配的消费规则无需结案");
            updateCloseStatus(user, masterData);
            return new EndConsume.Result();
        }
        if (ConsumeRuleType.DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
            log.info("直接扣减无需结案。");
            updateCloseStatus(user, masterData);
            return new EndConsume.Result();
        }
        //结案必须是冻结主对象的按钮 todo:审批前冻结可能不止一个冻结数据 可能会驳回
        String businessId = budgetAccountDetailService.queryConsumeDetailBusinessId(systemUser, masterData.getDescribeApiName(), masterData.getId(), consumeRulePO.getId().toString(), ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()));
        if (Strings.isNullOrEmpty(businessId)) {
            log.info("没有冻结明细。");
            updateCloseStatus(user, masterData);
            return new EndConsume.Result();
        }
        Map<String, IBudgetOperator> budgetOperatorMap = new HashMap<>();
        String releaseValue = UUID.randomUUID().toString();
        String releaseKey = String.format(RELEASE_AMOUNT_TEMPLATE, businessId);
        try {
            if (!redisLockService.tryLock(releaseKey, releaseValue)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_1));
            }
            Map<String, BigDecimal> unfrozenAmountMap = budgetAccountDetailService.queryFrozenAmountByBusinessId(systemUser, businessId);
            unfrozenAmountMap.keySet().forEach(budgetId -> budgetOperatorMap.put(budgetId, BudgetOperatorFactory.initOperator(BizType.CONSUME, user, budgetId, businessId, approvalId, masterData)));
            budgetOperatorMap.values().forEach(iBudgetOperator -> {
                if (!iBudgetOperator.tryLock()) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_1));
                }
            });
            List<IObjectData> withholdingDetails = queryWithholdingDetailsByBusinessId(systemUser, masterData, businessId, consumeRulePO);
            return transactionProxy.call(() -> {
                if (ConsumeDeductType.DEDUCT_SELF.value().equals(consumeRulePO.getDeductType())) {
                    BudgetRuleTypeNodeEntity endConsumeNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                    boolean matchCondition = conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, masterData.getDescribeApiName(), endConsumeNode.getConditionCode(), ObjectDataDocument.of(masterData));
                    if (!isForceEnd && !matchCondition) {
                        return new EndConsume.Result(true, String.format("该单据不满足规则[%s]的结案条件,是否进行强制结案？", consumeRulePO.getName()));
                    }
                    if (matchCondition) {
                        dealDeductAndReturnAllMoney(user, businessId, consumeRulePO, detailsMap, masterData, unfrozenAmountMap, budgetOperatorMap, withholdingDetails);
                    } else {
                        //全部结案 强制结案
                        allRefund(systemUser, businessId, unfrozenAmountMap, budgetOperatorMap, withholdingDetails, BizType.RETURN);
                    }
                } else {
                    //全部结案
                    allRefund(systemUser, businessId, unfrozenAmountMap, budgetOperatorMap, withholdingDetails, BizType.RETURN);
                }
                updateCloseStatus(user, masterData);
                return new EndConsume.Result();
            });
        } finally {
            redisLockService.unLock(releaseKey, releaseValue);
            budgetOperatorMap.values().forEach(IBudgetOperator::unlock);
        }
    }

    void dealDeductAndReturnAllMoney(User user, String businessId, BudgetNewConsumeRulePO consumeRulePO, Map<String, List<IObjectData>> detailsMap, IObjectData masterData, Map<String, BigDecimal> unfrozenAmountMap, Map<String, IBudgetOperator> budgetOperatorMap, List<IObjectData> withholdingDetails) {
        IObjectData frozenMasterData;
        String tenantId = user.getTenantId();
        User systemUser = User.systemUser(tenantId);
        BudgetRuleTypeNodeEntity endConsumeNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap;
        if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
            budgetMoneyMap = getAutomaticBudgetMap(systemUser, endConsumeNode, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap();
            frozenMasterData = masterData;
        } else {
            Map<String, IObjectData> id2DataMap = new HashMap<>();
            budgetMoneyMap = getManualDeductBudgetMap(systemUser, consumeRulePO, masterData, detailsMap, id2DataMap);
            frozenMasterData = id2DataMap.get("frozenMaster");
        }
        log.info("budgetMoneyMap:{}", JSON.toJSONString(budgetMoneyMap));

        if (!consumeRulePO.getOverDeductFlag() && budgetMoneyMap.keySet().stream().anyMatch(v -> !unfrozenAmountMap.containsKey(v))) {
            log.info("budgetMoneyMap contain more budget");
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_2));
        }

        unfrozenAmountMap.forEach((budgetId, amount) -> {
            IBudgetOperator operator = budgetOperatorMap.get(budgetId);
            BigDecimal needConsumeAmount = budgetMoneyMap.get(budgetId) == null ? BigDecimal.ZERO : budgetMoneyMap.get(budgetId).getSecond();
            if (!consumeRulePO.getOverDeductFlag() && amount.compareTo(needConsumeAmount) < 0) {
                log.info("needConsumeAmount:{} is bigger than amount:{}", needConsumeAmount, amount);
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_3));
            }
            BigDecimal unfreezeAmount = CurrencyUtils.min(amount, needConsumeAmount);
            operator.unfreeze(unfreezeAmount, BizType.RELEASE);
            operator.expenditure(unfreezeAmount);
            if (CurrencyUtils.gt(amount, unfreezeAmount)) {
                operator.unfreeze(amount.subtract(unfreezeAmount), BizType.RETURN);
            } else if (CurrencyUtils.gt(needConsumeAmount, amount)) {
                BigDecimal deductAmount = needConsumeAmount.subtract(amount);
                if (consumeRulePO.getOverDeductFlag()) {
                    validateOverDeductConsumableAmount(operator, frozenMasterData.getDescribeApiName(), frozenMasterData.getName(), needConsumeAmount, amount, deductAmount);
                } else if (deductAmount.compareTo(operator.consumableAmount()) > 0) {
                    String describeName = getDisplayName(operator.getUser().getTenantId(), frozenMasterData.getDescribeApiName());
                    if (TPMGrayUtils.budgetValidateMessageDesensitization(tenantId)) {
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_4));
                    }
                    throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_5), describeName, frozenMasterData.getName(), deductAmount, needConsumeAmount, needConsumeAmount));
                }

                operator.expenditure(deductAmount);
            }
            operator.recalculate();
        });
        releaseWithholdingDetails(tenantId, withholdingDetails);
        updateFrozenDetailOperateMark(user, businessId, new ArrayList<>(unfrozenAmountMap.keySet()));
    }


    @Override
    public Reconsume.Result reconsume(User user, String describeApiName, String dataId, String action) {
        Reconsume.Result result = new Reconsume.Result();
        String tenantId = user.getTenantId();
        User systemUser = User.systemUser(tenantId);
        IObjectData masterData = serviceFacade.findObjectData(user, dataId, describeApiName);
        String finalAction = Strings.isNullOrEmpty(action) ? ActionModeType.ADD.getKey() : action;

        if (!CommonFields.LIFE_STATUS__NORMAL.equals(masterData.get(CommonFields.LIFE_STATUS, String.class))) {
            log.info("life status is err.");
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_6));
        }

        Map<String, List<IObjectData>> detailsMap = getDetailsMap(null, masterData, systemUser);
        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
        if (consumeRulePO == null) {
            log.warn("no math any rule.");
            return result;
        }

        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());

        return ((Function<Object, Reconsume.Result>) (object) -> {
            String businessTraceId = String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), consumeRulePO.getId().toString(), UUID.randomUUID().toString());
            String approvalTraceId = TraceUtil.initApprovalTraceId();
            try {
                Map<String, List<Pair<MainType, BigDecimal>>> toBeConsumedMoneyMap = new HashMap<>();
                if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
                    if (isMathRuleType(consumeRulePO.getApiName(), consumeRulePO.getRecordType(), masterData) && finalAction.equals(ActionModeType.ADD.getKey())) {
                        //deal frozen
                        BudgetRuleTypeNodeEntity frozenRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.FREEZE);
                        if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), frozenRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                            log.warn("not fit freeze condition jump.");
                            return result;
                        }
                        ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                                getAutomaticBudgetMap(systemUser, frozenRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData) :
                                getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap, consumeRulePO, businessTraceId, approvalTraceId);
                        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = consumeRuleBudgetAmountDTO.getBudgetMoneyMap();
                        log.info("consumeRuleBudgetAmountDTO:{}", consumeRuleBudgetAmountDTO);
                        if (MapUtils.isEmpty(budgetMoneyMap) && MapUtils.isEmpty(consumeRuleBudgetAmountDTO.getWithholdingMoneyMap())) {
                            log.info("no budget need deal");
                            return result;
                        }
                        budgetNumberValidate(budgetMoneyMap.size());
                        changeBudgetMoneyMap(budgetMoneyMap, MainType.FREEZE, false, toBeConsumedMoneyMap);
                        formConsumeBudgetDetails(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, consumeRuleBudgetAmountDTO.getWithholdingMoneyMap(), approvalTraceId, businessTraceId, false);
                    } else if (isMathRuleType(consumeRulePO.getDeductApiName(), consumeRulePO.getDeductRecordType(), masterData) && finalAction.equals(ActionModeType.ADD.getKey())) {
                        //deal deduction
                        BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                        if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                            log.warn("not fit deduct condition jump.");
                            return result;
                        }
                        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap;
                        Map<String, IObjectData> id2DataMap = new HashMap<>();
                        //这里排除扣减本身的场景，扣减本身不可能有2次新建自己，所以走不到这。
                        if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                            String referenceFrozenObjectId = masterData.get(consumeRulePO.getRuleTypeNodes().get(1).getReferencedFieldApiName(), String.class);
                            if (Strings.isNullOrEmpty(referenceFrozenObjectId)) {
                                log.info("no reference id jump.");
                                return result;
                            }
                            IObjectData automaticData = consumeRulePO.getApiName().equals(consumeRulePO.getDeductApiName()) ? masterData : serviceFacade.findObjectData(systemUser, referenceFrozenObjectId, consumeRulePO.getApiName());
                            budgetMoneyMap = getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, automaticData).getBudgetMoneyMap();
                            log.info("budgetMoneyMap:{}", budgetMoneyMap);
                            if (MapUtils.isEmpty(budgetMoneyMap)) {
                                log.info("no budget need deal");
                                return result;
                            }
                            budgetNumberValidate(budgetMoneyMap.size());
                            closeConsumeJudge(automaticData);
                            session.setFrozenMasterData(automaticData);
                        } else {
                            budgetMoneyMap = getManualDeductBudgetMap(systemUser, consumeRulePO, masterData, detailsMap, id2DataMap);
                            log.info("budgetMoneyMap:{}", budgetMoneyMap);
                            if (MapUtils.isEmpty(budgetMoneyMap)) {
                                log.info("no budget need deal");
                                return result;
                            }
                            budgetNumberValidate(budgetMoneyMap.size());
                            closeConsumeJudge(serviceFacade.findObjectData(systemUser, id2DataMap.get("masterId").getId(), consumeRulePO.getApiName()));
                            session.setFrozenMasterData(id2DataMap.get("frozenMaster"));
                        }
                        businessTraceId = getBusinessId(systemUser, masterData, detailsMap, consumeRulePO, id2DataMap);
                        if (businessTraceId != null && !businessTraceId.contains(consumeRulePO.getId().toString())) {
                            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_7));
                        }

                        validateWithholdingAllTransferWhenDeduct(systemUser, consumeRulePO, masterData, businessTraceId);

                        //解冻-》有超额的 超额
                        Map<String, Pair<IObjectData, BigDecimal>> additionBudgetMap = validateDeductForFrozen(systemUser, businessTraceId, budgetMoneyMap, session, consumeRulePO.getOverDeductFlag());
                        changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, true, toBeConsumedMoneyMap);
                        changeBudgetMoneyMap(additionBudgetMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                        formConsumeBudgetDetails(systemUser, masterData, session.getFrozenMasterData(), toBeConsumedMoneyMap, budgetMoneyMap, null, approvalTraceId, businessTraceId, consumeRulePO.getOverDeductFlag());
                    } else {
                        log.info("middle release stage");
                        if (!isMiddleReleaseNode(consumeRulePO, masterData, action)) {
                            return result;
                        }
                        BudgetRuleTypeNodeEntity releaseNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.RELEASE);
                        ReleaseNodeEntity releaseNodeEntity = releaseNode.getReleaseNode().stream().filter(node -> node.getReleaseApiName().equals(masterData.getDescribeApiName()) && node.getReleaseRecordType().equals(masterData.getRecordType())).findFirst().get();
                        if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), releaseNodeEntity.getConditionCode(), ObjectDataDocument.of(masterData))) {
                            log.warn("not fit release condition jump.");
                            return result;
                        }
                        dealReturnFrozenMoney(tenantId, Lists.newArrayList(new Pair<>(masterData, null)), BizType.RELEASE);
                    }
                } else {
                    //direct deduct node
                    BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                    if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                        log.warn("not fit deduct condition jump.");
                        return result;
                    }
                    Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                            getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap() :
                            getManualDirectDeductBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap);
                    if (MapUtils.isEmpty(budgetMoneyMap)) {
                        log.info("no budget need deal");
                        return result;
                    }
                    budgetNumberValidate(budgetMoneyMap.size());
                    changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                    formConsumeBudgetDetails(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, null, approvalTraceId, businessTraceId, false);
                }
            } finally {
                unlockBySessionMap(session.all());
            }
            return result;
        }).apply(null);
    }

    private boolean isMiddleReleaseNode(BudgetNewConsumeRulePO consumeRulePO, IObjectData masterData, String action) {
        if (CollectionUtils.isEmpty(consumeRulePO.getReleaseList())) {
            return false;
        }
        if (!ConsumeConfigType.RELEASE_ENABLE.value().equals(consumeRulePO.getReleaseStatus())) {
            return false;
        }
        BudgetRuleTypeNodeEntity releaseNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.RELEASE);

        return releaseNode.getReleaseNode().stream().anyMatch(node -> node.getReleaseApiName().equals(masterData.getDescribeApiName()) && node.getReleaseRecordType().equals(masterData.getRecordType()) && node.getTriggerTime().equals(action));
    }

    //校验是否预提已经全部转化
    private void validateWithholdingAllTransferWhenDeduct(User systemUser, BudgetNewConsumeRulePO consumeRulePO, IObjectData masterData, String businessTraceId) {
        //如果是预提且没有businessId说明全是预提
        if (Strings.isNullOrEmpty(businessTraceId)) {
            if (ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()) && existsWithholdingDetail(systemUser, masterData, consumeRulePO)) {
                throw new ValidateException(I18NEnums.EXISTS_WITHHOLDING_DETAIL_CAN_NOT_DEDUCT.getCode());
            } else {
                throw new ValidateException(I18N.text(I18NEnums.DEDUCT_HAS_NOT_RELATED_FROZEN_DATA.getCode()));
            }
        }
        if (!ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus())) {
            return;
        }
        List<IObjectData> withholdingDetails = queryWithholdingDetailsByBusinessId(systemUser, null, businessTraceId, consumeRulePO);
        if (CollectionUtils.isNotEmpty(withholdingDetails)) {
            throw new ValidateException(I18NEnums.EXISTS_WITHHOLDING_DETAIL_CAN_NOT_DEDUCT.getCode());
        }
    }

    @Override
    public PreValidateConsumeRuleAmount.Result preValidateConsumeRuleAmount(User user, PreValidateConsumeRuleAmount.Arg arg) {
        IObjectData masterData = serviceFacade.findObjectData(user, arg.getObjectId(), arg.getApiName());
        String tenantId = user.getTenantId();
        User systemUser = User.systemUser(tenantId);
        Map<String, List<IObjectData>> detailsMap = getDetailsMap(null, masterData, systemUser);

        PreValidateConsumeRuleAmount.Result result = new PreValidateConsumeRuleAmount.Result();
        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
        if (consumeRulePO == null) {
            log.info("没有规则");
            return result;
        }
        String closeStatus = masterData.get(TPMActivityFields.CLOSE_STATUS, String.class);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            log.info("已经结案。");
            return result;
        }
        String lifeStatus = masterData.get(CommonFields.LIFE_STATUS, String.class);
        if (!CommonFields.LIFE_STATUS__NORMAL.equals(lifeStatus) && !CommonFields.LIFE_STATUS__UNDER_REVIEW.equals(lifeStatus)) {
            log.info("仅允许生命状态为正常或者审批中的调用。");
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_8));
        }
        boolean isTriggerBeforeApproval = ApprovalTriggerTime.BEFORE.value().equals(consumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime());
        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId() + "validate");
        try {
            Map<String, List<Pair<MainType, BigDecimal>>> toBeConsumedMoneyMap = new HashMap<>();
            if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
                if (isMathRuleType(consumeRulePO.getApiName(), consumeRulePO.getRecordType(), masterData)) {
                    if (isTriggerBeforeApproval) {
                        return result;
                    }
                    BudgetRuleTypeNodeEntity frozenRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.FREEZE);
                    if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), frozenRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                        log.warn("not fit freeze condition jump.");
                        return result;
                    }
                    Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                            getAutomaticBudgetMap(systemUser, frozenRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap() :
                            getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap, consumeRulePO, null, null).getBudgetMoneyMap();
                    log.info("budgetMoneyMap:{}", budgetMoneyMap);
                    if (MapUtils.isEmpty(budgetMoneyMap)) {
                        log.info("no budget need deal");
                        return result;
                    }
                    budgetNumberValidate(budgetMoneyMap.size());
                    changeBudgetMoneyMap(budgetMoneyMap, MainType.FREEZE, false, toBeConsumedMoneyMap);
                    validateAmount(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, false);
                } else if (isMathRuleType(consumeRulePO.getDeductApiName(), consumeRulePO.getDeductRecordType(), masterData)) {
                    BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                    Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap;
                    Map<String, IObjectData> id2DataMap = new HashMap<>();
                    IObjectData automaticData;
                    if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                        log.warn("not fit deduct condition jump.");
                        return result;
                    }
                    if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                        //automatic deduct map need frozenData
                        String referenceFrozenObjectId = masterData.get(consumeRulePO.getRuleTypeNodes().get(1).getReferencedFieldApiName(), String.class);
                        if (Strings.isNullOrEmpty(referenceFrozenObjectId)) {
                            log.info("no reference id jump.");
                            return result;
                        }
                        automaticData = consumeRulePO.getApiName().equals(consumeRulePO.getDeductApiName()) ? masterData : serviceFacade.findObjectData(systemUser, referenceFrozenObjectId, consumeRulePO.getApiName());
                        budgetMoneyMap = getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, automaticData).getBudgetMoneyMap();
                        log.info("budgetMoneyMap:{}", budgetMoneyMap);
                        if (MapUtils.isEmpty(budgetMoneyMap)) {
                            return result;
                        }
                        budgetNumberValidate(budgetMoneyMap.size());
                    } else {
                        budgetMoneyMap = getManualDeductBudgetMap(systemUser, consumeRulePO, masterData, detailsMap, id2DataMap);
                        log.info("budgetMoneyMap:{}", budgetMoneyMap);
                        if (MapUtils.isEmpty(budgetMoneyMap)) {
                            return result;
                        }
                        budgetNumberValidate(budgetMoneyMap.size());
                        automaticData = id2DataMap.get("frozenMaster");
                    }
                    session.setFrozenMasterData(automaticData);
                    String businessTraceId = getBusinessId(systemUser, masterData, detailsMap, consumeRulePO, id2DataMap);
                    validateWithholdingAllTransferWhenDeduct(user, consumeRulePO, masterData, businessTraceId);
                    Map<String, Pair<IObjectData, BigDecimal>> additionBudgetMap = validateDeductForFrozen(systemUser, businessTraceId, budgetMoneyMap, session, consumeRulePO.getOverDeductFlag());
                    changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, true, toBeConsumedMoneyMap);
                    changeBudgetMoneyMap(additionBudgetMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                    validateAmount(systemUser, masterData, automaticData, toBeConsumedMoneyMap, budgetMoneyMap, consumeRulePO.getOverDeductFlag());
                }
            } else {
                if (isTriggerBeforeApproval) {
                    return result;
                }
                BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit deduct condition jump.");
                    return result;
                }

                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                        getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap() :
                        getManualDirectDeductBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap);
                log.info("budgetMoneyMap:{}", budgetMoneyMap);
                if (MapUtils.isEmpty(budgetMoneyMap)) {
                    return result;
                }
                budgetNumberValidate(budgetMoneyMap.size());
                changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                validateAmount(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, false);
            }
        } finally {
            unlockBySessionMap(session.all());
            session.destroy();
        }
        return result;
    }

    @Override
    public void middleRelease(User user, String describeApiName, String dataId, String action) {
        log.info("middleRelease dataId:{}, describeApiName:{}, action:{}", dataId, describeApiName, action);
        if (Strings.isNullOrEmpty(dataId) || Strings.isNullOrEmpty(describeApiName) || Strings.isNullOrEmpty(action)) {
            throw new ValidateException("dataId or describeApiName or action can not be null.");
        }
        String tenantId = user.getTenantId();
        IObjectData masterData = serviceFacade.findObjectData(user, dataId, describeApiName);
        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
        if (consumeRulePO == null) {
            log.warn("no math any rule.");
            return;
        }
        if (!ConsumeConfigType.RELEASE_ENABLE.value().equals(consumeRulePO.getReleaseStatus())) {
            log.info("没有开启中间释放");
            return;
        }
        BudgetRuleTypeNodeEntity releaseNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.RELEASE);
        ReleaseNodeEntity releaseNodeEntity = releaseNode.getReleaseNode().stream().filter(node -> node.getReleaseApiName().equals(masterData.getDescribeApiName()) && node.getReleaseRecordType().equals(masterData.getRecordType())).findFirst().get();
        if (!releaseNodeEntity.getTriggerTime().equals(action)) {
            log.info("动作不匹配，{}", action);
            return;
        }
        if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), user.getUserIdInt(), masterData.getDescribeApiName(), releaseNodeEntity.getConditionCode(), ObjectDataDocument.of(masterData))) {
            log.info("not fit release condition jump.");
            return;
        }
        if (TPMGrayUtils.isOpenYqslStoreWriteOffMiddleDeduct(tenantId) && masterData.getDescribeApiName().equals(ApiNames.TPM_STORE_WRITE_OFF_OBJ)) {
            /*String auditStatus = masterData.get("approval_status__c", String.class);
            BigDecimal amount = masterData.get("monthly_fee_actual_amount__c", BigDecimal.class);
            String writeOffStatus = masterData.get("write_off_status__c", String.class);
            if ("pass".equals(auditStatus) && "option1".equals(writeOffStatus) && amount.compareTo(BigDecimal.ZERO) != 0) {
                log.info("不满足核销条件,auditStatus:{},writeOffStatus:{},amount:{}", auditStatus, writeOffStatus, amount);
                return;
            }*/
            User systemUser = User.systemUser(tenantId);
            IObjectData agreementData = serviceFacade.findObjectData(systemUser, masterData.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class), ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
            List<IObjectData> agreementDetails = querySameMonthAgreementDetailsByStoreWriteOff(tenantId, masterData);
            String businessId = budgetAccountDetailService.queryConsumeDetailBusinessId(systemUser, agreementData.getDescribeApiName(), agreementData.getId(), consumeRulePO.getId().toString(), ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()));
            //客开逻辑 不处理预提以及处理过的得跳出去出，为空可能是全是预提
            if (Strings.isNullOrEmpty(businessId) || hasDealRelease(systemUser, businessId, masterData)) {
                log.info("已经执行中间释放。business:{}", businessId);
                return;
            }
            Map<String, BigDecimal> unfrozenAmountMap = budgetAccountDetailService.queryFrozenAmountByBusinessId(systemUser, businessId);
            Map<String, List<IObjectData>> detailsMap = new HashMap<>();
            detailsMap.put("agreement_budget_table__c", agreementDetails);
            //不考虑预提
            consumeRulePO.setProvisionStatus(ConsumeConfigType.PROVISION_DISABLE.value());
            Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), agreementData, detailsMap, consumeRulePO, null, null).getBudgetMoneyMap();
            consumeRulePO.setProvisionStatus(ConsumeConfigType.PROVISION_ENABLE.value());
            releaseFrozenMoney(systemUser, masterData, unfrozenAmountMap, budgetMoneyMap, businessId);

        } else if (masterData.getDescribeApiName().equals(ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ) && ActionModeType.AGREEMENT_ABANDON.getKey().equals(action)) {
            // 查询协议从对象
            List<IObjectData> agreementDetails = querySameMonthAgreementDetailsByAgreement(tenantId, masterData);
            if (CollectionUtils.isEmpty(agreementDetails)) {
                log.info("agreementDetails is Empty!");
                return;
            }
            User systemUser = User.systemUser(tenantId);
            String businessId = budgetAccountDetailService.queryConsumeDetailBusinessId(systemUser, masterData.getDescribeApiName(), masterData.getId(), consumeRulePO.getId().toString(), ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()));
            //客开逻辑 不处理预提以及处理过的得跳出去出，为空可能是全是预提
            if (Strings.isNullOrEmpty(businessId) || hasDealRelease(systemUser, businessId, masterData)) {
                log.info("已经执行中间释放。business:{}", businessId);
                return;
            }
            Map<String, BigDecimal> unfrozenAmountMap = budgetAccountDetailService.queryFrozenAmountByBusinessId(systemUser, businessId);

            Map<String, List<IObjectData>> detailsMap = new HashMap<>();
            detailsMap.put("agreement_budget_table__c", agreementDetails);
            //不考虑预提
            consumeRulePO.setProvisionStatus(ConsumeConfigType.PROVISION_DISABLE.value());
            Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap, consumeRulePO, null, null).getBudgetMoneyMap();
            consumeRulePO.setProvisionStatus(ConsumeConfigType.PROVISION_ENABLE.value());
            // 取消预提
            List<IObjectData> provisionDataList = queryWithholdingDetailsByBusinessId(User.systemUser(tenantId), masterData, businessId, consumeRulePO);
            releaseWithholdingDetails(tenantId, provisionDataList);
            // 解冻
            releaseFrozenMoney(systemUser, masterData, unfrozenAmountMap, budgetMoneyMap, businessId);
        } else {
            BudgetTableManualNodeEntity manualNodeEntity = consumeRulePO.getBudgetTableManualNodes().get(0);
            BudgetTableReleaseEntity releaseEntity = manualNodeEntity.getBudgetTableReleaseEntity().stream().filter(entity -> entity.getApiName().equals(masterData.getDescribeApiName()) && entity.getRecordType().equals(masterData.getRecordType())).findFirst().orElse(null);
            if (releaseEntity == null) {
                throw new ValidateException("找不到释放节点的数据。");
            }
            IObjectData frozenData;
            if (Strings.isNullOrEmpty(releaseEntity.getRelationField())) {
                frozenData = masterData;
            } else {
                String frozenId = masterData.get(releaseEntity.getRelationField(), String.class);
                if (Strings.isNullOrEmpty(frozenId)) {
                    throw new ValidateException(I18N.text(I18NEnums.RELEASE_NODE_NOT_RELATED_FROZEN_DATA.getCode()));
                }
                frozenData = serviceFacade.findObjectData(user, frozenId, consumeRulePO.getApiName());
            }
            dealReturnFrozenMoney(tenantId, Lists.newArrayList(new Pair<>(masterData, frozenData)), BizType.RELEASE);
        }
    }

    private List<IObjectData> querySameMonthAgreementDetailsByAgreement(String tenantId, IObjectData masterData) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime monthBegin = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter("activity_agreement__c", Operator.EQ, Lists.newArrayList(masterData.getId())),
                SearchQueryUtil.filter("month__c", Operator.GTE, Lists.newArrayList(String.valueOf(monthBegin.toInstant(ZoneOffset.of("+8")).toEpochMilli())))
        ));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), "agreement_budget_table__c", query);
    }

    private boolean hasDealRelease(User systemUser, String businessId, IObjectData masterData) {
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, 1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMBudgetAccountDetailFields.BIZ_TRACE_ID, Operator.EQ, Lists.newArrayList(businessId)),
                SearchQueryUtil.filter(TPMBudgetAccountDetailFields.RELATED_OBJECT_API_NAME, Operator.EQ, Lists.newArrayList(masterData.getDescribeApiName())),
                SearchQueryUtil.filter(TPMBudgetAccountDetailFields.RELATED_OBJECT_DATA_ID, Operator.EQ, Lists.newArrayList(masterData.getId())),
                SearchQueryUtil.filter(TPMBudgetAccountDetailFields.BUSINESS_TYPE, Operator.EQ, Lists.newArrayList(BizType.RELEASE.value()))
        ));

        return !CommonUtils.queryData(serviceFacade, systemUser, ApiNames.TPM_BUDGET_ACCOUNT_DETAIL, query, Lists.newArrayList(CommonFields.ID)).isEmpty();
    }

    private void releaseFrozenMoney(User systemUser, IObjectData agreementData, Map<String, BigDecimal> unfrozenAmountMap, Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap, String businessId) {
        Map<String, BigDecimal> dealMap = new HashMap<>();
        unfrozenAmountMap.forEach((budgetId, amount) -> {
            if (budgetMoneyMap.containsKey(budgetId) && amount.compareTo(BigDecimal.ZERO) != 0) {
                Pair<IObjectData, BigDecimal> pair = budgetMoneyMap.get(budgetId);
                dealMap.put(budgetId, pair.getSecond().compareTo(amount) <= 0 ? pair.getSecond() : amount);
            }
        });
        if (MapUtils.isEmpty(dealMap)) {
            return;
        }
        String traceId = TraceUtil.initApprovalTraceId();
        Map<String, IBudgetOperator> operators = new HashMap<>();
        dealMap.keySet().forEach(budgetId -> operators.put(budgetId, BudgetOperatorFactory.initOperator(BizType.CONSUME, systemUser, budgetId, businessId, traceId, agreementData)));
        try {
            operators.values().forEach(operator -> {
                if (!operator.tryLock(30)) {
                    throw new ValidateException("服务繁忙请稍后再试。");
                }
            });
            transactionProxy.run(() -> dealMap.forEach((budgetId, amount) -> {
                IBudgetOperator operator = operators.get(budgetId);
                operator.unfreeze(amount, BizType.RELEASE);
                operator.recalculate();
            }));
        } finally {
            operators.values().forEach(IBudgetOperator::unlock);
        }
    }


    private List<IObjectData> querySameMonthAgreementDetailsByStoreWriteOff(String tenantId, IObjectData storeWriteOffData) {
        String agreementId = storeWriteOffData.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class);
        Integer month = storeWriteOffData.get("month__c", Integer.class);
        Integer year = storeWriteOffData.get("year__c", Integer.class);
        LocalDateTime monthBegin = LocalDate.now().withMonth(month).withYear(year).withDayOfMonth(1).atStartOfDay();
        LocalDateTime monthEnd = monthBegin.plusMonths(1);
        SearchTemplateQuery query = SearchQueryUtil.formSimpleQuery(0, -1, Lists.newArrayList(
                SearchQueryUtil.filter(TPMActivityAgreementDetailFields.ACTIVITY_AGREEMENT_ID, Operator.EQ, Lists.newArrayList(agreementId)),
                SearchQueryUtil.filter("month__c", Operator.BETWEEN, Lists.newArrayList(String.valueOf(monthBegin.toInstant(ZoneOffset.of("+8")).toEpochMilli()), String.valueOf(monthEnd.toInstant(ZoneOffset.of("+8")).toEpochMilli())))
        ));
        return CommonUtils.queryData(serviceFacade, User.systemUser(tenantId), "agreement_budget_table__c", query);
    }

    private void closeConsumeJudge(IObjectData data) {
        String closeStatus = data.get(TPMActivityFields.CLOSED_STATUS, String.class);
        if (TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus)) {
            throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_9), data.getName()));
        }
    }

    private void forbidEditImportantValue(DealTriggerAction.Arg arg) {

        String tenantId = arg.getUser().getTenantId();
        User systemUser = User.systemUser(tenantId);
        IObjectData masterData = arg.getMasterData();
        IObjectData dbMasterData = arg.getDbMasterData() == null ? serviceFacade.findObjectData(systemUser, masterData.getId(), masterData.getDescribeApiName()) : arg.getDbMasterData();

        Map<String, List<IObjectData>> detailsMap = arg.getDetailMap();
        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, masterData);
        if (consumeRulePO == null) {
            return;
        }
        List<IObjectData> details = budgetAccountDetailService.queryDetailsByRelatedData(tenantId, masterData.getDescribeApiName(), masterData.getId());

        if (CollectionUtils.isEmpty(details) && !existsWithholdingDetail(systemUser, masterData, consumeRulePO)) {
            log.info("没有预算明细，可以跳出。");
            return;
        }
        Map<String, List<IObjectData>> dbDetailsMap = arg.getActionCode().equals("Edit") ? getDetailsMap(null, masterData, systemUser) : new HashMap<>();

        List<String> noNeedSet = dbDetailsMap.keySet().stream().filter(key -> !detailsMap.containsKey(key)).collect(Collectors.toList());
        noNeedSet.forEach(dbDetailsMap::remove);

        if (isFrozenRuleType(systemUser, masterData, consumeRulePO)) {
            //冻结
            BudgetRuleTypeNodeEntity frozenRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.FREEZE);
            if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, masterData.getDescribeApiName(), frozenRuleType.getConditionCode(), ObjectDataDocument.of(dbMasterData))) {
                log.info("no validate rule jump");
                return;
            }
            boolean isTriggerBeforeApproval = ApprovalTriggerTime.BEFORE.value().equals(consumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime());
            if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                automaticEditLimit(masterData, dbMasterData, Lists.newArrayList(frozenRuleType), consumeRulePO.getBudgetTableAutomaticNodes(), isTriggerBeforeApproval);
            } else {
                manualEditList(masterData, dbMasterData, frozenRuleType, consumeRulePO.getBudgetTableManualNodes(), isTriggerBeforeApproval);
                manualEditDetailList(dbDetailsMap, detailsMap, frozenRuleType, consumeRulePO.getBudgetTableManualNodes(), isTriggerBeforeApproval);
            }

        } else {
            BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
            if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), -10000, masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(dbMasterData))) {
                log.info("no validate rule jump");
                return;
            }
            boolean isTriggerBeforeApproval = ApprovalTriggerTime.BEFORE.value().equals(consumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime());
            if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                if (ConsumeDeductType.DEDUCT_OTHER.value().equals(consumeRulePO.getDeductType())) {
                    automaticEditLimit(masterData, dbMasterData, Lists.newArrayList(deductRuleType), consumeRulePO.getBudgetTableAutomaticNodes(), isTriggerBeforeApproval);
                } else {
                    automaticEditLimit(masterData, dbMasterData, consumeRulePO.getRuleTypeNodes(), consumeRulePO.getBudgetTableAutomaticNodes(), isTriggerBeforeApproval);
                }
            } else {
                manualEditList(masterData, dbMasterData, deductRuleType, consumeRulePO.getBudgetTableManualNodes(), isTriggerBeforeApproval);
                manualEditDetailList(dbDetailsMap, detailsMap, deductRuleType, consumeRulePO.getBudgetTableManualNodes(), isTriggerBeforeApproval);
            }
        }
    }

    private void manualEditDetailList(Map<String, List<IObjectData>> dbDetailsMap, Map<String, List<IObjectData>> detailsMap, BudgetRuleTypeNodeEntity budgetRuleTypeNodeEntity, List<BudgetTableManualNodeEntity> manualNodeEntities, boolean isTriggerBeforeApproval) {
        if (dbDetailsMap.size() != detailsMap.size()) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_10));
        }
        Set<String> ruleDetailApiNames = new HashSet<>();
        manualNodeEntities.forEach(entity -> {
            if (entity.getBudgetTableFrozenEntity() != null) {
                ruleDetailApiNames.add(entity.getBudgetTableFrozenEntity().getApiName());
            }
            if (entity.getBudgetTableDeDuctEntity() != null) {
                ruleDetailApiNames.add(entity.getBudgetTableDeDuctEntity().getApiName());
            }
        });
        dbDetailsMap.forEach((detailApiName, dbDetailsList) -> {
            if (!ruleDetailApiNames.contains(detailApiName)) {
                return;
            }
            List<IObjectData> detailList = detailsMap.getOrDefault(detailApiName, new ArrayList<>());
            if (dbDetailsList.size() != detailList.size()) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_11));
            }
            Map<String, IObjectData> detailObjectMap = detailList.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (old, newOne) -> old));
            dbDetailsList.forEach(dbDetail -> {
                IObjectData detail = detailObjectMap.get(dbDetail.getId());
                if (detail == null) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_12));
                }
                manualEditList(detail, dbDetail, budgetRuleTypeNodeEntity, manualNodeEntities, isTriggerBeforeApproval);
                detailObjectMap.remove(dbDetail.getId());
            });
        });
    }

    private boolean isFrozenRuleType(User user, IObjectData masterData, BudgetNewConsumeRulePO consumeRulePO) {
        if (ConsumeDeductType.DEDUCT_OTHER.value().equals(consumeRulePO.getDeductType())) {
            return isMathRuleType(consumeRulePO.getApiName(), consumeRulePO.getRecordType(), masterData);
        } else {
            //List<IObjectData> dataList = budgetAccountDetailService.queryFixedMainTypeBudgetDetailByRelatedData(user, MainType.UNFREEZE, masterData);
            //扣减过自己必定是结案过，否则就是冻结。
            String closeStatus = masterData.get(TPMActivityFields.CLOSED_STATUS, String.class);
            return !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(closeStatus);
        }
    }

    private void manualEditList(IObjectData relatedData, IObjectData dbRelatedData, BudgetRuleTypeNodeEntity ruleTypeNodeEntity, List<BudgetTableManualNodeEntity> manualNodeEntities, boolean isTriggerBeforeApproval) {

        Set<String> notAllowEditSet = new HashSet<>();
        //冻结扣减的 扣减不需要限制 审批前冻结的需要控制条件不要变
        if (isTriggerBeforeApproval && Strings.isNullOrEmpty(ruleTypeNodeEntity.getReferencedFieldApiName())) {
            if (CollectionUtils.isNotEmpty(ruleTypeNodeEntity.getWhereConditions())) {
                ruleTypeNodeEntity.getWhereConditions().forEach(budgetWhereConditionEntity -> budgetWhereConditionEntity.getTriggerConditions().forEach(budgetTriggerConditionEntity -> notAllowEditSet.add(budgetTriggerConditionEntity.getFieldName())));
            }
            notAllowEditSet.add(ruleTypeNodeEntity.getSourceField());
            notAllowEditSet.add(ruleTypeNodeEntity.getReferencedFieldApiName());
        }
        manualNodeEntities.forEach(budgetTableManualNodeEntity -> {
            BudgetTableDeDuctEntity budgetTableDeDuctEntity = budgetTableManualNodeEntity.getBudgetTableDeDuctEntity();
            if (ConsumeRuleType.DEDUCTION.value().equals(ruleTypeNodeEntity.getType()) && budgetTableDeDuctEntity != null && budgetTableDeDuctEntity.getApiName().equals(relatedData.getDescribeApiName())) {
                notAllowEditSet.add(budgetTableDeDuctEntity.getAmountField());
                notAllowEditSet.add(budgetTableDeDuctEntity.getMasterDetail());
                notAllowEditSet.add(budgetTableDeDuctEntity.getRelationField());
            }

            BudgetTableFrozenEntity budgetTableFrozenEntity = budgetTableManualNodeEntity.getBudgetTableFrozenEntity();
            if (budgetTableFrozenEntity != null && budgetTableFrozenEntity.getApiName().equals(relatedData.getDescribeApiName())) {
                notAllowEditSet.add(budgetTableFrozenEntity.getAmountField());
                notAllowEditSet.add(budgetTableFrozenEntity.getMasterDetail());
                notAllowEditSet.add(budgetTableFrozenEntity.getRelationField());
                //预提的字段不可编辑
                if (budgetTableFrozenEntity.getBudgetTableProvisionEntity() != null) {
                    budgetTableFrozenEntity.getBudgetTableProvisionEntity().getFieldRelation().forEach(v -> notAllowEditSet.add(v.getTargetField()));
                }
            }
        });
        containsTheSameFieldValidate(relatedData, dbRelatedData, notAllowEditSet);
    }

    private void containsTheSameFieldValidate(IObjectData relatedData, IObjectData dbRelatedData, Set<String> notAllowEditSet) {
        Map<String, Object> map = diff(relatedData.getTenantId(), dbRelatedData, relatedData);
        map.keySet().forEach(key -> {
            if (notAllowEditSet.contains(key)) {
                FieldResult field = serviceFacade.findCustomFieldDescribe(relatedData.getTenantId(), relatedData.getDescribeApiName(), key);
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_13), field.getField().getLabel()));
            }
        });
    }

    private void automaticEditLimit(IObjectData relatedData, IObjectData dbRelatedData, List<BudgetRuleTypeNodeEntity> ruleTypeNodeEntities, List<BudgetTableAutomaticNodeEntity> automaticNodeEntities, boolean isTriggerBeforeApproval) {
        Set<String> notAllowEditSet = new HashSet<>();
        ruleTypeNodeEntities.forEach(ruleTypeNodeEntity -> {
            notAllowEditSet.add(ruleTypeNodeEntity.getReferencedFieldApiName());
            notAllowEditSet.add(ruleTypeNodeEntity.getSourceField());

            if (ConsumeRuleType.FREEZE.value().equals(ruleTypeNodeEntity.getType())) {
                notAllowEditSet.add(ruleTypeNodeEntity.getSourceField());
                automaticNodeEntities.forEach(entity -> {
                    if (CollectionUtils.isNotEmpty(entity.getFieldRelation())) {
                        entity.getFieldRelation().forEach(budgetFieldRelationEntity -> notAllowEditSet.add(budgetFieldRelationEntity.getTargetField()));
                    }
                });
            }
            if (isTriggerBeforeApproval && CollectionUtils.isNotEmpty(ruleTypeNodeEntity.getWhereConditions())) {
                ruleTypeNodeEntity.getWhereConditions().forEach(budgetWhereConditionEntity -> budgetWhereConditionEntity.getTriggerConditions().forEach(budgetTriggerConditionEntity -> notAllowEditSet.add(budgetTriggerConditionEntity.getFieldName())));
            }
        });
        containsTheSameFieldValidate(relatedData, dbRelatedData, notAllowEditSet);
    }

    private Map<String, Object> diff(String tenantId, IObjectData dbData, IObjectData newData) {
        IObjectDescribe describe = serviceFacade.findObject(tenantId, dbData.getDescribeApiName());
        Map<String, Object> updateFields = ObjectDataExt.of(dbData).diff(newData, describe);
        if (com.facishare.paas.appframework.common.util.CollectionUtils.notEmpty(updateFields)) {
            ObjectDataExt dataExt = ObjectDataExt.of(Maps.newHashMap(updateFields));
            dataExt.removeInvalidFieldForApproval(describe);
        }
        return updateFields;
    }


    private void allRefund(User user, String businessId, Map<String, BigDecimal> unfrozenAmountMap, Map<String, IBudgetOperator> budgetOperatorMap, List<IObjectData> withholdingDetails, BizType bizType) {
        unfrozenAmountMap.forEach((budgetId, amount) -> {
            IBudgetOperator operator = budgetOperatorMap.get(budgetId);
            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                operator.unfreeze(amount, bizType);
                operator.recalculate();
            }
        });
        releaseWithholdingDetails(user.getTenantId(), withholdingDetails);
        //只有差额返还的才需要标记终结
        if (bizType == BizType.RETURN) {
            updateFrozenDetailOperateMark(user, businessId, new ArrayList<>(unfrozenAmountMap.keySet()));
        }
    }

    private void releaseWithholdingDetails(String tenantId, List<IObjectData> withholdingDetails) {
        if (withholdingDetails.isEmpty()) {
            return;
        }
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMBudgetProvisionObjFields.PROVISION_STATUS, TPMBudgetProvisionObjFields.ProvisionStatus.INVALID);
        serviceFacade.batchUpdateWithMap(User.systemUser(tenantId), withholdingDetails, updateMap);
        IObjectDescribe describe = serviceFacade.findObject(tenantId, ApiNames.TPM_BUDGET_PROVISION_OBJ);
        serviceFacade.logWithCustomMessage(User.systemUser(tenantId), EventType.MODIFY, ActionType.MODIFY, describe, withholdingDetails, "依据消费规则作废预提。");
    }

    private void updateFrozenDetailOperateMark(User user, String businessId, List<String> budgetIds) {
        if (CollectionUtils.isEmpty(budgetIds)) {
            return;
        }
        Map<String, String> budgetId2frozenIdMap = budgetAccountDetailService.getConsumeFrozenDetailIdByBusinessId(user, businessId, budgetIds);
        List<IObjectData> updateLists = new ArrayList<>();
        budgetId2frozenIdMap.forEach((budgetId, id) -> {
            IObjectData objectData = new ObjectData();
            objectData.setId(id);
            objectData.setTenantId(user.getTenantId());
            objectData.setDescribeApiName(ApiNames.TPM_BUDGET_ACCOUNT_DETAIL);
            objectData.set(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, BudgetDetailOperateMark.COMPLETED_UNFREEZE.value());
            updateLists.add(objectData);
        });
        serviceFacade.batchUpdateByFields(user, updateLists, Lists.newArrayList(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL));
    }


    /**
     * @param user
     * @param triggerData
     * @param detailsMap
     * @param consumeRulePO
     * @param id2DataMap    非空 包含所有关联的数据
     * @return
     */
    private String getBusinessId(User user, IObjectData triggerData, Map<String, List<IObjectData>> detailsMap, BudgetNewConsumeRulePO consumeRulePO, Map<String, IObjectData> id2DataMap) {
        String apiName = triggerData.getDescribeApiName();
        String id = triggerData.getId();
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
            if (ConsumeDeductType.DEDUCT_OTHER.value().equals(consumeRulePO.getDeductType()) && consumeRulePO.getDeductApiName().equals(triggerData.getDescribeApiName())) {
                apiName = consumeRulePO.getApiName();
                if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                    id = triggerData.get(consumeRulePO.getRuleTypeNodes().get(1).getReferencedFieldApiName(), String.class);
                } else {
                    boolean isGet = false;
                    //此时如果能到这一步，所有能关联到的对象必须有冻结，不然早就异常跳出
                    for (BudgetTableManualNodeEntity manualNodeEntity : consumeRulePO.getBudgetTableManualNodes()) {
                        if (isGet) {
                            break;
                        }
                        BudgetTableDeDuctEntity deDuctEntity = manualNodeEntity.getBudgetTableDeDuctEntity();
                        BudgetTableFrozenEntity frozenEntity = manualNodeEntity.getBudgetTableFrozenEntity();
                        if (deDuctEntity.getApiName().equals(consumeRulePO.getDeductApiName())) {
                            //主对象
                            String masterId = triggerData.get(deDuctEntity.getRelationField(), String.class);
                            if (Strings.isNullOrEmpty(masterId)) {
                                continue;
                            }
                            id = masterId;
                            isGet = true;
                        } else {
                            if (MapUtils.isNotEmpty(detailsMap)) {
                                List<IObjectData> details = detailsMap.get(deDuctEntity.getApiName());
                                if (CollectionUtils.isNotEmpty(details)) {
                                    for (IObjectData detail : details) {
                                        String relationId = detail.get(deDuctEntity.getRelationField(), String.class);
                                        if (!Strings.isNullOrEmpty(relationId)) {
                                            String objectKey = frozenEntity.getApiName() + "." + relationId;
                                            IObjectData relatedDetail = id2DataMap.get(objectKey);
                                            id = relatedDetail.get(frozenEntity.getMasterDetail(), String.class);
                                            isGet = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!isGet) {
                        log.info("no related object can find.");
                        return null;
                    }
                }
            }
        }
        return budgetAccountDetailService.queryConsumeDetailBusinessId(user, apiName, id, consumeRulePO.getId().toString(), ConsumeConfigType.PROVISION_ENABLE.value().equals(consumeRulePO.getProvisionStatus()));
    }


    private void updateCloseStatus(User user, IObjectData data) {
        Map<String, Object> updateMap = new HashMap<>();
        updateMap.put(TPMActivityFields.CLOSE_STATUS, TPMActivityFields.CLOSE_STATUS__CLOSED);
        updateMap.put(TPMActivityFields.CLOSE_TIME, System.currentTimeMillis());
        serviceFacade.updateWithMap(user, data, updateMap);
    }

    private void unlockBySessionMap(Map<String, String> all) {
        log.info("start unlock. size:{}", all.size());
        String temp = String.format(BudgetConsumeSession.SESSION_LOCK_PREFIX, "");
        for (String key : all.keySet()) {
            String value = all.get(key);
            try {
                if (!Strings.isNullOrEmpty(key) && key.startsWith(temp)) {
                    redisLockService.unLock(key, value);
                }
                if (!Strings.isNullOrEmpty(key) && key.startsWith(RedisLockService.REDIS_SESSION_PREFIX)) {
                    if (!Strings.isNullOrEmpty(value)) {
                        String[] args = value.split("#");
                        redisLockService.unLock(args[0], args[1]);
                    }
                }
            } catch (Exception e) {
                log.error("unlock key:{} value:{}", key, value, e);
            }
        }
    }

    private void dealNoUpdateEvent() {


    }

    private void dealInApprovalPassEvent(String tenantId, BudgetNewConsumeRulePO consumeRulePO, IObjectData masterData, Map<String, List<IObjectData>> detailsMap, Map<String, Object> callBackData, BudgetConsumeSession session) {
        String approvalTraceId = TraceUtil.getApprovalCallbackTraceId(callBackData);
        String businessTraceId = TraceUtil.getBusinessCallbackTraceId(callBackData);
        User systemUser = User.systemUser(tenantId);
        log.info("dealInApprovalEvent approvalTrace:{},businessId:{}", approvalTraceId, businessTraceId);

        Map<String, List<Pair<MainType, BigDecimal>>> toBeConsumedMoneyMap = new HashMap<>();
        boolean isTriggerBeforeApproval = ApprovalTriggerTime.BEFORE.value().equals(consumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime());
        List<IObjectData> budgetDetails = new ArrayList<>();
        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
            if (isMathRuleType(consumeRulePO.getApiName(), consumeRulePO.getRecordType(), masterData)) {
                if (isTriggerBeforeApproval) {
                    log.info("isTriggerBeforeApproval");
                    return;
                }
                //deal frozen
                BudgetRuleTypeNodeEntity frozenRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.FREEZE);
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), frozenRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit freeze condition jump.");
                    return;
                }
                if (Strings.isNullOrEmpty(approvalTraceId)) {
                    approvalTraceId = String.format("consumeRule:%s:%s", consumeRulePO.getOriginalId(), masterData.getId());
                    businessTraceId = String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), consumeRulePO.getId().toString(), UUID.randomUUID());
                    log.info(" add frozen approvalTrace:{},businessId:{}", approvalTraceId, businessTraceId);
                }
                if (budgetAccountDetailService.countDetailByTraceId(systemUser, approvalTraceId) > 0) {
                    log.info("has done create detail");
                    return;
                }
                ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                        getAutomaticBudgetMap(systemUser, frozenRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData) :
                        getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap, consumeRulePO, businessTraceId, approvalTraceId);
                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = consumeRuleBudgetAmountDTO.getBudgetMoneyMap();
                log.info("consumeRuleBudgetAmountDTO:{}", consumeRuleBudgetAmountDTO);
                if (MapUtils.isEmpty(budgetMoneyMap) && MapUtils.isEmpty(consumeRuleBudgetAmountDTO.getWithholdingMoneyMap())) {
                    return;
                }
                budgetNumberValidate(budgetMoneyMap.size());
                changeBudgetMoneyMap(budgetMoneyMap, MainType.FREEZE, false, toBeConsumedMoneyMap);

                budgetDetails = formConsumeBudgetDetails(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, consumeRuleBudgetAmountDTO.getWithholdingMoneyMap(), approvalTraceId, businessTraceId, false);
            } else if (isMathRuleType(consumeRulePO.getDeductApiName(), consumeRulePO.getDeductRecordType(), masterData)) {
                //deal deduction
                BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap;
                Map<String, IObjectData> id2DataMap = new HashMap<>();
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit deduct condition jump.");
                    return;
                }
                if (Strings.isNullOrEmpty(approvalTraceId)) {
                    approvalTraceId = String.format("consumeRule:%s:%s", consumeRulePO.getId().toString(), masterData.getId());
                    log.info("add deduct frozen approvalId:{}", approvalTraceId);
                }
                if (budgetAccountDetailService.countDetailByTraceId(systemUser, approvalTraceId) > 0) {
                    log.info("has done create detail");
                    return;
                }
                //这里排除扣减本身的场景，扣减本身不可能有2次新建自己，所以走不到这。
                if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
                    //automatic deduct map need frozenData
                    String referenceFrozenObjectId = masterData.get(consumeRulePO.getRuleTypeNodes().get(1).getReferencedFieldApiName(), String.class);
                    if (Strings.isNullOrEmpty(referenceFrozenObjectId)) {
                        log.info("no reference id jump.");
                        return;
                    }
                    IObjectData automaticData = consumeRulePO.getApiName().equals(consumeRulePO.getDeductApiName()) ? masterData : serviceFacade.findObjectData(systemUser, referenceFrozenObjectId, consumeRulePO.getApiName());
                    budgetMoneyMap = getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, automaticData).getBudgetMoneyMap();
                    log.info("budgetMoneyMap:{}", budgetMoneyMap);
                    if (MapUtils.isEmpty(budgetMoneyMap)) {
                        return;
                    }
                    budgetNumberValidate(budgetMoneyMap.size());
                    closeConsumeJudge(automaticData);
                    session.setFrozenMasterData(automaticData);
                } else {
                    budgetMoneyMap = getManualDeductBudgetMap(systemUser, consumeRulePO, masterData, detailsMap, id2DataMap);
                    log.info("budgetMoneyMap:{}", budgetMoneyMap);
                    if (MapUtils.isEmpty(budgetMoneyMap)) {
                        return;
                    }
                    budgetNumberValidate(budgetMoneyMap.size());
                    closeConsumeJudge(serviceFacade.findObjectData(systemUser, id2DataMap.get("masterId").getId(), consumeRulePO.getApiName()));
                    session.setFrozenMasterData(id2DataMap.get("frozenMaster"));
                }
                if (Strings.isNullOrEmpty(businessTraceId)) {
                    businessTraceId = getBusinessId(systemUser, masterData, detailsMap, consumeRulePO, id2DataMap);
                    log.info("add deduct frozen businessTraceId:{}", businessTraceId);
                }
                validateWithholdingAllTransferWhenDeduct(systemUser, consumeRulePO, masterData, businessTraceId);
                Map<String, Pair<IObjectData, BigDecimal>> additionBudgetMap = validateDeductForFrozen(systemUser, businessTraceId, budgetMoneyMap, session, consumeRulePO.getOverDeductFlag());
                changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, true, toBeConsumedMoneyMap);
                changeBudgetMoneyMap(additionBudgetMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                budgetDetails = formConsumeBudgetDetails(systemUser, masterData, session.getFrozenMasterData(), toBeConsumedMoneyMap, budgetMoneyMap, null, approvalTraceId, businessTraceId, consumeRulePO.getOverDeductFlag());
            }
        } else {
            if (!isTriggerBeforeApproval) {
                //direct deduct node
                BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit deduct condition jump.");
                    return;
                }
                if (Strings.isNullOrEmpty(approvalTraceId)) {
                    approvalTraceId = String.format("consumeRule:%s:%s", consumeRulePO.getOriginalId(), masterData.getId());
                    businessTraceId = String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), consumeRulePO.getId().toString(), UUID.randomUUID());
                    log.info(" add deduct approvalTrace:{},businessId:{}", approvalTraceId, businessTraceId);
                }
                if (budgetAccountDetailService.countDetailByTraceId(systemUser, approvalTraceId) > 0) {
                    log.info("has done create detail");
                    return;
                }
                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                        getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap() :
                        getManualDirectDeductBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap);
                log.info("budgetMoneyMap:{}", budgetMoneyMap);
                if (MapUtils.isEmpty(budgetMoneyMap)) {
                    return;
                }
                budgetNumberValidate(budgetMoneyMap.size());
                changeBudgetMoneyMap(budgetMoneyMap, MainType.EXPENDITURE, false, toBeConsumedMoneyMap);
                budgetDetails = formConsumeBudgetDetails(systemUser, masterData, masterData, toBeConsumedMoneyMap, budgetMoneyMap, null, approvalTraceId, businessTraceId, false);
            } else {
                session.setOperateMark(BudgetDetailOperateMark.APPROVAL_COMPLETE.value());
            }
        }
        if (CollectionUtils.isNotEmpty(budgetDetails)) {
            log.info("setBudgetDetailIds ");
            session.setBudgetDetailIds(budgetDetails.stream().map(DBRecord::getId).collect(Collectors.toList()));
        }
    }

    private void updateAdvanceDetails2ApprovalCompleted(User user, String businessId) {
        List<IObjectData> details = budgetAccountDetailService.getAdvanceDetailByBusinessId(user, businessId);
        updateDetailOperateMark(user, details, BudgetDetailOperateMark.APPROVAL_COMPLETE);
    }

    private void dealInApprovalRejectEvent(String tenantId, BudgetNewConsumeRulePO consumeRulePO, IObjectData masterData, Map<String, List<IObjectData>> detailsMap, Map<String, Object> callBackData, BudgetConsumeSession session) {
        User systemUser = User.systemUser(tenantId);
        String approvalTraceId = TraceUtil.getApprovalCallbackTraceId(callBackData);
        String businessTraceId = TraceUtil.getBusinessCallbackTraceId(callBackData);

        if (Strings.isNullOrEmpty(approvalTraceId)) {
            log.warn("not traceId.");
            return;
        }
        if (!masterData.getDescribeApiName().equals(consumeRulePO.getApiName())) {
            //只有主对象才支持审批前冻结 需要进行返点
            return;
        }
        List<IObjectData> details = budgetAccountDetailService.queryDetailsByTraceIdAndBusinessId(systemUser, businessTraceId, approvalTraceId);
        if (!CollectionUtils.isEmpty(details)) {
            String lockValue = UUID.randomUUID().toString();
            String lockKey = "dealInApprovalRejectEvent:" + approvalTraceId;
            try {
                if (!redisLockService.tryLock(session, lockKey, lockValue, 20000)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_14));
                }
                if (details.stream().anyMatch(v -> BudgetDetailOperateMark.APPROVAL_ROLLBACK.value().equals(v.get(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL)))) {
                    log.info("已经处理过了。");
                    return;
                }
                List<IObjectData> backDetail = new ArrayList<>();
                details.forEach(detail -> {
                    String mainType = detail.get(TPMBudgetAccountDetailFields.MAIN_TYPE, String.class);
                    String budgetId = detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class);
                    MainType backMainType = null;
                    BizType bizType;
                    BigDecimal amount = detail.get(TPMBudgetAccountDetailFields.AMOUNT, BigDecimal.class);
                    if (mainType.equals(MainType.FREEZE.value())) {
                        backMainType = MainType.UNFREEZE;
                        bizType = BizType.RELEASE;
                    } else if (mainType.equals(MainType.EXPENDITURE.value())) {
                        backMainType = MainType.INCOME;
                        bizType = BizType.APPROVAL_BACK;
                    } else {
                        log.info("details:{}", details);
                        throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_15) + mainType);
                    }
                    backDetail.add(budgetAccountDetailService.fromDetail(systemUser, budgetId, backMainType, bizType, BudgetDetailOperateMark.APPROVAL_ROLLBACK, amount, masterData, businessTraceId, approvalTraceId, null));

                });

                transactionProxy.run(() -> {

                    budgetAccountDetailService.batchAdd(systemUser, backDetail);

                    session.setOperateMark(BudgetDetailOperateMark.APPROVAL_COMPLETE.value());

                    recalculateBudget(systemUser, details);
                });
                if (CollectionUtils.isNotEmpty(backDetail)) {
                    session.setBudgetDetailIds(backDetail.stream().map(DBRecord::getId).collect(Collectors.toList()));
                }
            } finally {
                redisLockService.unLock(lockKey, lockValue);
            }
        }

    }

    private void updateDetailOperateMark(User user, List<IObjectData> details, BudgetDetailOperateMark budgetDetailOperateMark) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        Map<String, Object> updateMap = new HashMap<>(4);
        updateMap.put(TPMBudgetAccountDetailFields.OPERATE_MARK_DETAIL, budgetDetailOperateMark.value());
        serviceFacade.batchUpdateWithMap(user, details, updateMap);
    }

    private void budgetNumberValidate(int number) {
        if (number > 8) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_16));
        }
    }

    private void cleanOccupyInFinallyDo(DealTriggerAction.Arg arg) {
        IObjectData masterData = arg.getMasterData();
        String tenantId = arg.getUser().getTenantId();
        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
        try {
            List<String> occupyIds = session.getSessionOccupyDetailIds();
            if (CollectionUtils.isEmpty(occupyIds)) {
                log.info("cleanOccupyInFinallyDo fast jump out");
                return;
            }
            transactionProxy.run(() -> budgetOccupyService.release(tenantId, occupyIds));
        } finally {
            unlockBySessionMap(session.all());
            session.destroy();
        }
    }

    private Map<String, List<IObjectData>> getDetailsMap(Map<String, List<IObjectData>> detailsMap, IObjectData masterData, User user) {
        if (MapUtils.isEmpty(detailsMap)) {
            List<IObjectDescribe> detailDescribe = serviceFacade.findDetailDescribes(user.getTenantId(), masterData.getDescribeApiName());
            detailsMap = serviceFacade.findDetailObjectDataList(detailDescribe, masterData, user);
        }
        return detailsMap;
    }

    private void getDetailsMap(List<String> detailApiNames, Map<String, List<IObjectData>> detailsMap, IObjectData masterData, User user) {
        if (!CollectionUtils.isEmpty(detailApiNames)) {
            List<IObjectDescribe> detailDescribe = new ArrayList<>(serviceFacade.findObjects(user.getTenantId(), detailApiNames).values());
            detailsMap.putAll(serviceFacade.findDetailObjectDataList(detailDescribe, masterData, user));
        }
    }

    private void cleanOccupyAndDealNoApprovalEvent(DealTriggerAction.Arg arg) {
        IObjectData masterData = arg.getMasterData();
        String tenantId = arg.getUser().getTenantId();
        User systemUser = User.systemUser(tenantId);
        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
        String approvalTraceId = session.getTraceId();
        String businessId = session.getBizKey();
        String executeStage = session.getExecuteStage();
        List<String> occupyIds = session.getSessionOccupyDetailIds();
        if ("-1".equals(executeStage) && CollectionUtils.isEmpty(occupyIds)) {
            log.info("fast jump out");
            return;
        }

        boolean isTriggerBeforeApproval = session.getIsTriggerBeforeApproval();

        if (!arg.getIsApprovalStartSuccess() || isTriggerBeforeApproval) {
            //无审批
            transactionProxy.run(() -> dealRealAmountOpInAfterByTransaction(systemUser, occupyIds, session, masterData, businessId, approvalTraceId, arg.getIsApprovalStartSuccess()));
        } else {
            budgetOccupyService.release(tenantId, occupyIds);
        }
        session.setSessionOccupyDetailIds("[]");
        session.setExecuteStage("after");
    }

    private void dealRealAmountOpInAfterByTransaction(User systemUser, List<String> occupyIds, BudgetConsumeSession session, IObjectData masterData, String businessId, String approvalTraceId, boolean isApprovalStartSuccess) {
        String tenantId = systemUser.getTenantId();
        budgetOccupyService.release(tenantId, occupyIds);
        List<IObjectData> occupiedDetails = serviceFacade.findObjectDataByIdsIgnoreAll(tenantId, occupyIds, ApiNames.TPM_BUDGET_OCCUPATION_DETAIL);
        List<String> budgetSet = new ArrayList<>();
        List<IObjectData> budgetDetails = new ArrayList<>();
        String operateMark = session.getOperateMark();
        switch (BudgetDetailOperateMark.of(operateMark)) {
            case CONSUME_FREEZE_THEN_DEDUCTION_FIRST:
                saveFrozenData(systemUser, session, occupiedDetails, budgetDetails);
                break;
            case CONSUME_FREEZE_THEN_DEDUCTION_SECOND:
                transferAndSaveDeductData(systemUser, session, masterData, businessId, approvalTraceId, isApprovalStartSuccess, occupiedDetails, budgetDetails);
                break;
            case CONSUME_DIRECT_DEDUCTION:
                budgetDetails.addAll(budgetAccountDetailService.batchAdd(systemUser, batchChangeOccupyToBudgetDetail(systemUser, MainType.EXPENDITURE, BizType.CONSUME, isApprovalStartSuccess ? BudgetDetailOperateMark.OPERATE_BEFORE_APPROVAL : BudgetDetailOperateMark.CONSUME_DIRECT_DEDUCTION, occupiedDetails)));
                break;
            case MIDDLE_RELEASE:
                dealReturnFrozenMoney(tenantId, Lists.newArrayList(new Pair<>(masterData, null)), BizType.RELEASE);
            default:
        }
        if (!CollectionUtils.isEmpty(budgetDetails)) {
            budgetDetails.forEach(detail -> budgetSet.add(detail.get(TPMBudgetAccountDetailFields.BUDGET_ACCOUNT_ID, String.class)));
            log.info("recalculateBudgetAmount. ids:{}", budgetSet);
            budgetSet.forEach(id -> budgetCalculateService.recalculateBudgetAmount(systemUser, id));
        }
    }

    private void saveFrozenData(User systemUser, BudgetConsumeSession session, List<IObjectData> occupiedDetails, List<IObjectData> budgetDetails) {
        List<IObjectData> withholdingList = session.getWithholdingList();
        transactionProxy.run(() -> {
            budgetDetails.addAll(budgetAccountDetailService.batchAdd(systemUser, batchChangeOccupyToBudgetDetail(systemUser, MainType.FREEZE, BizType.CONSUME, BudgetDetailOperateMark.CONSUME_FREEZE_THEN_DEDUCTION_FIRST, occupiedDetails)));
            saveWithholdingData(systemUser, withholdingList);
        });
    }

    private void saveWithholdingData(User systemUser, List<IObjectData> withholdingList) {
        List<IObjectData> details = new ArrayList<>();
        withholdingList.forEach(data -> data.get(TPMBudgetProvisionObjFields.DETAILS_LIST, List.class, Lists.newArrayList()).forEach(detail -> details.add(new ObjectData((Map) detail))));
        serviceFacade.bulkSaveObjectData(withholdingList, systemUser);
        serviceFacade.bulkSaveObjectData(details, systemUser);
    }

    private void transferAndSaveDeductData(User systemUser, BudgetConsumeSession session, IObjectData masterData, String businessId, String approvalTraceId, boolean isApprovalStartSuccess, List<IObjectData> occupiedDetails, List<IObjectData> budgetDetails) {
        //假设有审批 那必然是审批后扣减 那么肯定得跳过
        if (isApprovalStartSuccess) {
            log.info("先冻结后扣减，扣减阶段不做处理。");
            return;
        }
        Map<String, BigDecimal> dealMoneyAmountMap = JSON.parseObject(session.getBudgetAmountMap(), new TypeReference<Map<String, BigDecimal>>() {
        });
        log.info("dealMoneyAmountMap:{}", dealMoneyAmountMap);
        if (MapUtils.isEmpty(dealMoneyAmountMap)) {
            return;
        }
        budgetNumberValidate(dealMoneyAmountMap.size());
        budgetDetails.addAll(budgetAccountDetailService.batchAdd(systemUser, formFrozenDeduct(systemUser, masterData, dealMoneyAmountMap, businessId, approvalTraceId)));
        budgetDetails.addAll(budgetAccountDetailService.batchAdd(systemUser, batchChangeOccupyToBudgetDetail(systemUser, MainType.EXPENDITURE, BizType.CONSUME, BudgetDetailOperateMark.CONSUME_FREEZE_THEN_DEDUCTION_SECOND, occupiedDetails)));

    }


    private List<IObjectData> formFrozenDeduct(User user, IObjectData relatedData, Map<String, BigDecimal> amountMap, String businessId, String approvalId) {
        Map<String, String> budgetId2frozenIdMap = budgetAccountDetailService.getConsumeFrozenDetailIdByBusinessId(user, businessId, new ArrayList<>(amountMap.keySet()));
        List<IObjectData> details = Lists.newArrayList();
        if (MapUtils.isNotEmpty(amountMap)) {
            amountMap.forEach((budgetId, amount) -> {
                details.add(budgetAccountDetailService.fromDetail(user, budgetId, MainType.UNFREEZE, BizType.RELEASE, null, amount, relatedData, businessId, approvalId, budgetId2frozenIdMap.get(budgetId)));
                details.add(budgetAccountDetailService.fromDetail(user, budgetId, MainType.EXPENDITURE, BizType.CONSUME, null, amount, relatedData, businessId, approvalId, null));
            });
        }
        return details;
    }

    private List<IObjectData> batchChangeOccupyToBudgetDetail(User user, MainType changeToMainType, BizType bizType, BudgetDetailOperateMark operateMark, List<IObjectData> occupies) {
        List<IObjectData> changeToDetails = new ArrayList<>();
        Map<String, IObjectData> id2Object = new HashMap<>();
        occupies.forEach(occupy -> {
            String apiName = occupy.get(TPMBudgetOccupationDetailFields.RELATED_OBJECT_API_NAME, String.class);
            String id = occupy.get(TPMBudgetOccupationDetailFields.RELATED_OBJECT_ID, String.class);

            if (!id2Object.containsKey(id)) {
                id2Object.put(id, serviceFacade.findObjectDataIgnoreAll(user, id, apiName));
            }
            changeToDetails.add(budgetAccountDetailService.fromDetail(user,
                    occupy.get(TPMBudgetOccupationDetailFields.BUDGET_ACCOUNT_ID, String.class),
                    changeToMainType,
                    bizType,
                    operateMark,
                    occupy.get(TPMBudgetOccupationDetailFields.AMOUNT, BigDecimal.class),
                    id2Object.get(id),
                    occupy.get(TPMBudgetOccupationDetailFields.BIZ_TRACE_ID, String.class),
                    occupy.get(TPMBudgetOccupationDetailFields.APPROVAL_TRACE_ID, String.class),
                    null
            ));
        });
        return changeToDetails;
    }

    private void preValidateAndLockMoney(DealTriggerAction.Arg arg) {
        String tenantId = arg.getUser().getTenantId();
        IObjectData masterData = arg.getMasterData();
        Map<String, List<IObjectData>> detailsMap = arg.getDetailMap();
        User systemUser = User.systemUser(tenantId);

        BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, arg.getMasterData());
        if (consumeRulePO == null) {
            log.warn("no math any rule.");
            return;
        }
        fillDetailMap(consumeRulePO, masterData, detailsMap, systemUser);

        String approvalTraceId = TraceUtil.initApprovalTraceId();
        String businessTraceId = String.format(TPMBudgetAccountDetailFields.BizCode.BUDGET_CONSUME_RULE.template(), consumeRulePO.getId().toString(), UUID.randomUUID().toString());
        Map<String, Object> callBackData = arg.getCallbackMap();
        BudgetConsumeSession session = new BudgetConsumeSession(masterData.getId());
        session.setTraceId(approvalTraceId);
        session.setBizKey(businessTraceId);
        session.setFrozenMasterData(masterData);
        session.setIsTriggerBeforeApproval(ApprovalTriggerTime.BEFORE.value().equals(consumeRulePO.getRuleTypeNodes().get(0).getApprovalTriggerTime()));
        callBackData.put(TraceUtil.FMCG_TPM_BUDGET_APPROVAL_CALLBACK_TRACE_ID_KEY, approvalTraceId);
        callBackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, businessTraceId);
        List<String> occupyDetailIds = new ArrayList<>();


        if (ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType())) {
            if (isMathRuleType(consumeRulePO.getApiName(), consumeRulePO.getRecordType(), masterData)) {
                //deal frozen
                BudgetRuleTypeNodeEntity frozenRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.FREEZE);
                session.setOperateMark(BudgetDetailOperateMark.CONSUME_FREEZE_THEN_DEDUCTION_FIRST.value());
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), frozenRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit freeze condition jump.");
                    return;
                }
                ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                        getAutomaticBudgetMap(systemUser, frozenRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData) :
                        getManualFrozenBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap, consumeRulePO, businessTraceId, approvalTraceId);
                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = consumeRuleBudgetAmountDTO.getBudgetMoneyMap();
                log.info("consumeRuleBudgetAmountDTO:{}", consumeRuleBudgetAmountDTO);
                if (MapUtils.isEmpty(budgetMoneyMap) && MapUtils.isEmpty(consumeRuleBudgetAmountDTO.getWithholdingMoneyMap())) {
                    log.info("no budget need deal");
                    return;
                }
                budgetNumberValidate(budgetMoneyMap.size());
                List<IObjectData> occupies = occupyMoney(systemUser, masterData, masterData, budgetMoneyMap, budgetMoneyMap, approvalTraceId, businessTraceId, false);
                occupies.forEach(v -> occupyDetailIds.add(v.getId()));
                session.setWithholdingList(new ArrayList<>(consumeRuleBudgetAmountDTO.getWithholdingMoneyMap().values()));
            } else if (isMathRuleType(consumeRulePO.getDeductApiName(), consumeRulePO.getDeductRecordType(), masterData)) {
                //deal deduction
                BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
                session.setOperateMark(BudgetDetailOperateMark.CONSUME_FREEZE_THEN_DEDUCTION_SECOND.value());
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit deduct condition jump.");
                    return;
                }
                Map<String, IObjectData> id2DataMap = new HashMap<>();
                //这里排除扣减本身的场景，扣减本身不可能有2次新建自己，所以走不到这。
                Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = dealAutoOrManualBudgetMapInDeductNode(consumeRulePO, masterData, detailsMap, systemUser, session, id2DataMap);
                if (MapUtils.isEmpty(budgetMoneyMap)) {
                    return;
                }
                //查找的是冻结主对象的businessId todo:bug
                businessTraceId = getBusinessId(systemUser, masterData, detailsMap, consumeRulePO, id2DataMap);
                validateWithholdingAllTransferWhenDeduct(systemUser, consumeRulePO, masterData, businessTraceId);
                Map<String, Pair<IObjectData, BigDecimal>> additionBudgetMap = validateDeductForFrozen(systemUser, businessTraceId, budgetMoneyMap, session, consumeRulePO.getOverDeductFlag());
                if (MapUtils.isNotEmpty(additionBudgetMap)) {
                    occupyMoney(systemUser, masterData, session.getFrozenMasterData(), additionBudgetMap, budgetMoneyMap, approvalTraceId, businessTraceId, consumeRulePO.getOverDeductFlag()).forEach(v -> occupyDetailIds.add(v.getId()));
                }
                session.putBudgetAmountMap(budgetMoneyMap.values().stream().collect(Collectors.toMap(v -> v.getFirst().getId(), Pair::getSecond, (before, after) -> before)));
                //refill
                session.setBizKey(businessTraceId);
                callBackData.put(TraceUtil.FMCG_TPM_BUDGET_BUSINESS_CALLBACK_TRACE_ID_KEY, businessTraceId);
            } else {
                log.info("middle release stage");
                session.setOperateMark(BudgetDetailOperateMark.MIDDLE_RELEASE.value());
                BudgetRuleTypeNodeEntity releaseNode = getRuleNodeByType(consumeRulePO, ConsumeRuleType.RELEASE);
                ReleaseNodeEntity releaseNodeEntity = releaseNode.getReleaseNode().stream().filter(node -> node.getReleaseApiName().equals(masterData.getDescribeApiName()) && node.getReleaseRecordType().equals(masterData.getRecordType())).findFirst().get();
                if (!releaseNodeEntity.getTriggerTime().equals(ActionModeType.ADD.getKey())) {
                    //不是新建
                    return;
                }
                if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), releaseNodeEntity.getConditionCode(), ObjectDataDocument.of(masterData))) {
                    log.warn("not fit release condition jump.");
                    return;
                }
                BudgetTableManualNodeEntity manualNodeEntity = consumeRulePO.getBudgetTableManualNodes().get(0);
                BudgetTableReleaseEntity releaseEntity = manualNodeEntity.getBudgetTableReleaseEntity().stream().filter(entity -> entity.getApiName().equals(masterData.getDescribeApiName()) && entity.getRecordType().equals(masterData.getRecordType())).findFirst().orElse(null);
                if (releaseEntity == null) {
                    throw new ValidateException("找不到释放节点的数据。");
                }
                if (Strings.isNullOrEmpty(releaseEntity.getRelationField())) {
                    return;
                }
                String frozenId = masterData.get(releaseEntity.getRelationField(), String.class);
                if (Strings.isNullOrEmpty(frozenId)) {
                    throw new ValidateException(I18N.text(I18NEnums.RELEASE_NODE_NOT_RELATED_FROZEN_DATA.getCode()));
                }
            }
        } else {
            //direct deduct node
            BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
            session.setOperateMark(BudgetDetailOperateMark.CONSUME_DIRECT_DEDUCTION.value());
            if (!conditionAdapter.isDataMatchRuleCodes(Integer.valueOf(tenantId), systemUser.getUserIdInt(), masterData.getDescribeApiName(), deductRuleType.getConditionCode(), ObjectDataDocument.of(masterData))) {
                log.warn("not fit deduct condition jump.");
                return;
            }
            Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod()) ?
                    getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, masterData).getBudgetMoneyMap() :
                    getManualDirectDeductBudgetMap(systemUser, consumeRulePO.getBudgetTableManualNodes(), masterData, detailsMap);
            if (MapUtils.isEmpty(budgetMoneyMap)) {
                log.info("no budget need deal");
                return;
            }
            budgetNumberValidate(budgetMoneyMap.size());
            List<IObjectData> occupies = occupyMoney(systemUser, masterData, null, budgetMoneyMap, budgetMoneyMap, approvalTraceId, businessTraceId, false);
            occupies.forEach(v -> occupyDetailIds.add(v.getId()));
        }
        session.setSessionOccupyDetailIds(JSON.toJSONString(occupyDetailIds));
        session.setExecuteStage("before");
    }

    private BudgetRuleTypeNodeEntity getRuleNodeByType(BudgetNewConsumeRulePO po, ConsumeRuleType consumeRuleType) {
        return po.getRuleTypeNodes().stream().filter(v -> consumeRuleType.value().equals(v.getType())).findFirst().orElseThrow(() -> new ValidateException(String.format("无法获取对应消费规则节点【%s】", consumeRuleType.value())));
    }

    private Map<String, Pair<IObjectData, BigDecimal>> dealAutoOrManualBudgetMapInDeductNode(BudgetNewConsumeRulePO consumeRulePO, IObjectData masterData, Map<String, List<IObjectData>> detailsMap, User systemUser, BudgetConsumeSession session, Map<String, IObjectData> id2DataMap) {
        BudgetRuleTypeNodeEntity deductRuleType = getRuleNodeByType(consumeRulePO, ConsumeRuleType.DEDUCTION);
        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = new HashMap<>();
        if (BudgetMethodEnum.AUTOMATIC.value().equals(consumeRulePO.getBudgetMethod())) {
            String referenceFrozenObjectId = masterData.get(consumeRulePO.getRuleTypeNodes().get(1).getReferencedFieldApiName(), String.class);
            if (Strings.isNullOrEmpty(referenceFrozenObjectId)) {
                log.info("no reference id jump.");
                return budgetMoneyMap;
            }
            IObjectData automaticData = consumeRulePO.getApiName().equals(consumeRulePO.getDeductApiName()) ? masterData : serviceFacade.findObjectData(systemUser, referenceFrozenObjectId, consumeRulePO.getApiName());
            budgetMoneyMap = getAutomaticBudgetMap(systemUser, deductRuleType, consumeRulePO.getBudgetTableAutomaticNodes(), masterData, automaticData).getBudgetMoneyMap();
            log.info("budgetMoneyMap:{}", budgetMoneyMap);
            if (MapUtils.isEmpty(budgetMoneyMap)) {
                log.info("no budget need deal");
                return budgetMoneyMap;
            }
            budgetNumberValidate(budgetMoneyMap.size());
            closeConsumeJudge(automaticData);
            session.setFrozenMasterData(automaticData);
        } else {
            budgetMoneyMap = getManualDeductBudgetMap(systemUser, consumeRulePO, masterData, detailsMap, id2DataMap);
            log.info("budgetMoneyMap:{}", budgetMoneyMap);
            if (MapUtils.isEmpty(budgetMoneyMap)) {
                log.info("no budget need deal");
                return budgetMoneyMap;
            }
            budgetNumberValidate(budgetMoneyMap.size());
            closeConsumeJudge(serviceFacade.findObjectData(systemUser, id2DataMap.get("masterId").getId(), consumeRulePO.getApiName()));
            session.setFrozenMasterData(id2DataMap.get("frozenMaster"));
        }
        return budgetMoneyMap;
    }

    private void fillDetailMap(BudgetNewConsumeRulePO consumeRulePO, IObjectData master, Map<String, List<IObjectData>> detailsMap, User user) {
        List<String> detailApiNames = new ArrayList<>();
        String masterApiName = master.getDescribeApiName();
        if (CollectionUtils.isNotEmpty(consumeRulePO.getBudgetTableManualNodes())) {
            if (masterApiName.equals(consumeRulePO.getApiName())) {
                consumeRulePO.getBudgetTableManualNodes().stream().filter(node -> !masterApiName.equals(node.getBudgetTableFrozenEntity().getApiName())).forEach(node -> detailApiNames.add(node.getBudgetTableFrozenEntity().getApiName()));
            } else if (masterApiName.equals(consumeRulePO.getDeductApiName())) {
                consumeRulePO.getBudgetTableManualNodes().stream().filter(node -> !masterApiName.equals(node.getBudgetTableDeDuctEntity().getApiName())).forEach(node -> detailApiNames.add(node.getBudgetTableDeDuctEntity().getApiName()));
            } else {
                return;
            }
            detailApiNames.removeIf(detailsMap::containsKey);
            log.info("detailApiNames:{}", detailApiNames);
            getDetailsMap(detailApiNames, detailsMap, master, user);
        } else {
            log.info("auto map");
        }
    }

    private Map<String, Pair<IObjectData, BigDecimal>> validateDeductForFrozen(User user, String businessId, Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap, BudgetConsumeSession session, boolean isOverDeduct) {
        //need lock
        Map<String, Pair<IObjectData, BigDecimal>> additionMap = new HashMap<>();
        if (!Strings.isNullOrEmpty(businessId)) {
            String value = UUID.randomUUID().toString();
            String lockKey = String.format(BudgetConsumeSession.SESSION_LOCK_PREFIX, businessId);
            try {
                if (!redisLockService.tryLock(lockKey, value, 3000L)) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_17));
                }
                session.setAttribute(lockKey, value);
                Map<String, BigDecimal> moneyMap = budgetAccountDetailService.queryFrozenAmountByBusinessId(user, businessId);
                // check if allow over deduct. one budget  one money
                budgetMoneyMap.values().forEach(pair -> {
                    BigDecimal amount = moneyMap.getOrDefault(pair.getFirst().getId(), BigDecimal.ZERO);
                    if (!isOverDeduct && amount.compareTo(pair.getSecond()) < 0) {
                        String describeName = getDisplayName(user.getTenantId(), session.getFrozenMasterData().getDescribeApiName());
                        if (TPMGrayUtils.budgetValidateMessageDesensitization(user.getTenantId())) {
                            log.info(String.format("本操作需扣减「%s-%s」%s 元费用，已超出该单据可扣减金额 %s 元，请将需扣减金额值修改至小于等于%s元后重新提交。", describeName, session.getFrozenMasterData().getName(), pair.getSecond(), amount, amount));
                            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_18));
                        }
                        throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_19), describeName, session.getFrozenMasterData().getName(), pair.getSecond(), amount, amount));
                    } else if (isOverDeduct && amount.compareTo(pair.getSecond()) < 0) {
                        additionMap.put(pair.getFirst().getId(), new Pair<>(pair.getFirst(), pair.getSecond().subtract(amount)));
                        pair.setSecond(amount);
                    }
                });
            } catch (Exception e) {
                log.info("unlock key:{} value:{}", lockKey, value);
                redisLockService.unLock(lockKey, value);
                throw e;
            }
        } else {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_20));
        }
        return additionMap;
    }

    private List<IObjectData> formConsumeBudgetDetails(User user, IObjectData relatedData, IObjectData frozenMasterData, Map<String, List<Pair<MainType, BigDecimal>>> budgetAmountMap, Map<String, Pair<IObjectData, BigDecimal>> originalAmountMap, Map<String, IObjectData> withholdingMap, String approvalTraceId, String businessTraceId, boolean needOverDeductTip) {
        log.info("formConsumeBudgetDetails, amount:{}", budgetAmountMap);
        Map<String, IBudgetOperator> budget2OperatorMap = new HashMap<>();
        List<IObjectData> budgetDetails = new ArrayList<>();
        budgetAmountMap.forEach((budgetId, moneyPair) -> {
            IBudgetOperator budgetOperator = BudgetOperatorFactory.initOperator(BizType.CONSUME, user, budgetId, businessTraceId, approvalTraceId, relatedData);
            budget2OperatorMap.put(budgetId, budgetOperator);
        });

        try {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                if (!operator.tryLock()) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_21));
                }
            }

            transactionProxy.run(() -> {
                for (String budgetId : budgetAmountMap.keySet()) {
                    List<Pair<MainType, BigDecimal>> pairs = budgetAmountMap.get(budgetId);
                    IBudgetOperator operator = budget2OperatorMap.get(budgetId);
                    mergeValidateTotalBudgetAmount(budgetId, pairs, frozenMasterData, originalAmountMap, operator, needOverDeductTip);
                    for (Pair<MainType, BigDecimal> pair : pairs) {
                        MainType mainType = pair.getFirst();
                        switch (mainType) {
                            case EXPENDITURE:
                                budgetDetails.add(operator.expenditure(pair.getSecond()));
                                break;
                            case INCOME:
                                budgetDetails.add(operator.income(pair.getSecond()));
                                break;
                            case FREEZE:
                                budgetDetails.add(operator.freeze(pair.getSecond()));
                                break;
                            case UNFREEZE:
                                budgetDetails.add(operator.unfreeze(pair.getSecond(), BizType.RELEASE));
                        }
                    }
                    operator.recalculate();
                }
                if (MapUtils.isNotEmpty(withholdingMap)) {
                    saveWithholdingData(user, new ArrayList<>(withholdingMap.values()));
                }
            });
        } finally {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                operator.unlock();
            }
        }
        return budgetDetails;
    }

    private void validateAmount(User user, IObjectData relatedData, IObjectData frozenMasterData, Map<String, List<Pair<MainType, BigDecimal>>> budgetAmountMap, Map<String, Pair<IObjectData, BigDecimal>> originalAmountMap, boolean needOverDeductTip) {
        log.info("formConsumeBudgetDetails, amount:{}", budgetAmountMap);
        Map<String, IBudgetOperator> budget2OperatorMap = new HashMap<>();
        budgetAmountMap.forEach((budgetId, moneyPair) -> {
            IBudgetOperator budgetOperator = BudgetOperatorFactory.initOperator(BizType.CONSUME, user, budgetId, "temp", "tmp", relatedData);
            budget2OperatorMap.put(budgetId, budgetOperator);
        });

        try {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                if (!operator.tryLock()) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_22));
                }
            }

            transactionProxy.run(() -> {
                for (String budgetId : budgetAmountMap.keySet()) {
                    List<Pair<MainType, BigDecimal>> pairs = budgetAmountMap.get(budgetId);
                    IBudgetOperator operator = budget2OperatorMap.get(budgetId);
                    mergeValidateTotalBudgetAmount(budgetId, pairs, frozenMasterData, originalAmountMap, operator, needOverDeductTip);
                }
            });
        } finally {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                operator.unlock();
            }
        }
    }

    private void mergeValidateTotalBudgetAmount(String budgetId, List<Pair<MainType, BigDecimal>> pairs, IObjectData frozenData, Map<String, Pair<IObjectData, BigDecimal>> originalAmountMap, IBudgetOperator operator, boolean needOverDeductTip) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal frozenAmount = BigDecimal.ZERO;
        for (Pair<MainType, BigDecimal> pair : pairs) {
            if (MainType.EXPENDITURE.equals(pair.getFirst())) {
                totalAmount = totalAmount.subtract(pair.getSecond());
            } else if (MainType.FREEZE.equals(pair.getFirst())) {
                totalAmount = totalAmount.subtract(pair.getSecond());
                frozenAmount = frozenAmount.add(pair.getSecond());
            } else {
                totalAmount = totalAmount.add(pair.getSecond());
            }
        }
        log.info("total budget:{}, amount:{}", operator.getAccount().getId(), totalAmount);
        if (CurrencyUtils.lt(totalAmount, BigDecimal.ZERO)) {
            if (needOverDeductTip) {
                BigDecimal realDeductAmount = totalAmount.negate();
                BigDecimal deductTotalAmount = originalAmountMap.get(budgetId).getSecond().add(realDeductAmount);
                validateOverDeductConsumableAmount(operator, frozenData.getDescribeApiName(), frozenData.getName(), deductTotalAmount, frozenAmount, realDeductAmount);
            } else {
                operator.validateConsumableAmount(totalAmount.negate());
            }
        }
    }


    private List<IObjectData> occupyMoney(User user, IObjectData relatedData, IObjectData frozenData, Map<String, Pair<IObjectData, BigDecimal>> budgetAmountMap, Map<String, Pair<IObjectData, BigDecimal>> originalAmountMap, String approvalTraceId, String businessTraceId, boolean overDeductTip) {
        Map<String, IBudgetOperator> budget2OperatorMap = new HashMap<>();
        List<IObjectData> occupyDetails = new ArrayList<>();
        budgetAmountMap.forEach((budgetId, moneyPair) -> {
            IBudgetOperator budgetOperator = BudgetOperatorFactory.initOperator(BizType.CONSUME, user, budgetId, businessTraceId, approvalTraceId, relatedData, true, false);
            budget2OperatorMap.put(budgetId, budgetOperator);
        });

        try {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                if (!operator.tryLock()) {
                    throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_23));
                }
            }
            transactionProxy.run(() -> {
                for (Pair<IObjectData, BigDecimal> pair : budgetAmountMap.values()) {
                    IBudgetOperator operator = budget2OperatorMap.get(pair.getFirst().getId());
                    if (overDeductTip) {
                        BigDecimal frozenAmount = originalAmountMap.get(pair.getFirst().getId()).getSecond();
                        BigDecimal deductTotalAmount = frozenAmount.add(pair.getSecond());
                        validateOverDeductConsumableAmount(operator, frozenData.getDescribeApiName(), frozenData.getName(), deductTotalAmount, frozenAmount, pair.getSecond());
                    } else {
                        operator.validateConsumableAmount(pair.getSecond());
                    }
                }
                for (Pair<IObjectData, BigDecimal> pair : budgetAmountMap.values()) {
                    IBudgetOperator operator = budget2OperatorMap.get(pair.getFirst().getId());
                    occupyDetails.add(operator.occupy(pair.getSecond()));
                }
            });
        } finally {
            for (IBudgetOperator operator : budget2OperatorMap.values()) {
                operator.unlock();
            }
        }
        return occupyDetails;
    }

    private ConsumeRuleBudgetAmountDTO getAutomaticBudgetMap(User user, BudgetRuleTypeNodeEntity ruleTypeNode, List<BudgetTableAutomaticNodeEntity> automaticNodeEntities, IObjectData masterData, IObjectData mapperData) {
        ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = new ConsumeRuleBudgetAmountDTO(null, null, null);
        consumeRuleBudgetAmountDTO.setWithholdingMoneyMap(new HashMap<>());
        if (masterData != mapperData) {
            if (!CommonFields.LIFE_STATUS__NORMAL.equals(mapperData.get(CommonFields.LIFE_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_24));
            }
        }

        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = new HashMap<>();
        consumeRuleBudgetAmountDTO.setBudgetMoneyMap(budgetMoneyMap);
        BigDecimal totalAmount = masterData.get(ruleTypeNode.getSourceField(), BigDecimal.class);
        if (totalAmount == null) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_25));
        }
        amountZeroJudge(totalAmount);
        totalAmount = totalAmount.setScale(2, BigDecimal.ROUND_DOWN);
        BigDecimal leftAmount = totalAmount;
        BigDecimal hundredAmount = new BigDecimal("100.00");
        for (int index = 0; index < automaticNodeEntities.size(); index++) {
            BudgetTableAutomaticNodeEntity node = automaticNodeEntities.get(index);
            BigDecimal ratio = new BigDecimal(node.getRatio()).setScale(2, BigDecimal.ROUND_DOWN);
            IObjectData budget = budgetAccountService.getBudgetByFieldRelation(user, node.getBudgetType(), node.getNodeId(), node.getFieldRelation(), mapperData);
            if (budget == null) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_26));
            }
            if (budgetMoneyMap.containsKey(budget.getId())) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_27), budget.getName()));
            }
            if (index == automaticNodeEntities.size() - 1) {
                amountZeroJudge(leftAmount);
                budgetMoneyMap.put(budget.getId(), new Pair<>(budget, leftAmount));
            } else {
                BigDecimal currentAmount = totalAmount.multiply(ratio).divide(hundredAmount, RoundingMode.DOWN);
                amountZeroJudge(currentAmount);
                leftAmount = leftAmount.subtract(currentAmount);
                budgetMoneyMap.put(budget.getId(), new Pair<>(budget, currentAmount));
            }
        }
        return consumeRuleBudgetAmountDTO;
    }

    private void amountZeroJudge(BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) < 0) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_28));
        }
    }

    private ConsumeRuleBudgetAmountDTO getManualFrozenBudgetMap(User user, List<BudgetTableManualNodeEntity> budgetTableManualNodeEntities, IObjectData masterData, Map<String, List<IObjectData>> detailsMap, BudgetNewConsumeRulePO budgetNewConsumeRulePO, String businessId, String traceId) {
        ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = new ConsumeRuleBudgetAmountDTO(budgetNewConsumeRulePO, businessId, traceId);
        consumeRuleBudgetAmountDTO.setBudgetMoneyMap(new HashMap<>());
        consumeRuleBudgetAmountDTO.setWithholdingMoneyMap(new HashMap<>());
        boolean enableWithholding = ConsumeConfigType.PROVISION_ENABLE.value().equals(budgetNewConsumeRulePO.getProvisionStatus());
        for (BudgetTableManualNodeEntity manualNodeEntity : budgetTableManualNodeEntities) {
            BudgetTableFrozenEntity frozenEntity = manualNodeEntity.getBudgetTableFrozenEntity();
            if (masterData.getDescribeApiName().equals(frozenEntity.getApiName())) {
                getFrozenBudgetAndAmount(user, frozenEntity, manualNodeEntity.getNodeId(), masterData, masterData, consumeRuleBudgetAmountDTO, enableWithholding);
            } else {
                List<IObjectData> detailList = detailsMap.getOrDefault(frozenEntity.getApiName(), Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    detailList.forEach(detail -> getFrozenBudgetAndAmount(user, frozenEntity, manualNodeEntity.getNodeId(), detail, masterData, consumeRuleBudgetAmountDTO, enableWithholding));
                }
            }
        }

        return consumeRuleBudgetAmountDTO;
    }

    private ConsumeRuleBudgetAmountDTO getManualFrozenBudgetMap(User user, List<BudgetTableManualNodeEntity> budgetTableManualNodeEntities, IObjectData masterData, List<IObjectData> detailList, BudgetNewConsumeRulePO budgetNewConsumeRulePO, String businessId, String traceId) {
        ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO = new ConsumeRuleBudgetAmountDTO(budgetNewConsumeRulePO, businessId, traceId);
        consumeRuleBudgetAmountDTO.setBudgetMoneyMap(new HashMap<>());
        consumeRuleBudgetAmountDTO.setWithholdingMoneyMap(new HashMap<>());
        for (BudgetTableManualNodeEntity manualNodeEntity : budgetTableManualNodeEntities) {
            BudgetTableFrozenEntity frozenEntity = manualNodeEntity.getBudgetTableFrozenEntity();
            if ("agreement_budget_table__c".equals(frozenEntity.getApiName())) {
                if (CollectionUtils.isNotEmpty(detailList)) {
                    detailList.forEach(detail -> getFrozenBudgetAndAmountV2(user, frozenEntity, manualNodeEntity.getNodeId(), detail, masterData, consumeRuleBudgetAmountDTO));
                }
            }
        }

        return consumeRuleBudgetAmountDTO;
    }

    private Map<String, Pair<IObjectData, BigDecimal>> getManualDirectDeductBudgetMap(User user, List<BudgetTableManualNodeEntity> budgetTableManualNodeEntities, IObjectData masterData, Map<String, List<IObjectData>> detailsMap) {
        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = new HashMap<>();
        for (BudgetTableManualNodeEntity manualNodeEntity : budgetTableManualNodeEntities) {
            BudgetTableDeDuctEntity deDuctEntity = manualNodeEntity.getBudgetTableDeDuctEntity();
            if (masterData.getDescribeApiName().equals(deDuctEntity.getApiName())) {
                getDirectDeductBudgetAndAmount(user, deDuctEntity, masterData, budgetMoneyMap);
            } else {
                List<IObjectData> detailList = detailsMap.getOrDefault(deDuctEntity.getApiName(), Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    detailList.forEach(detail -> getDirectDeductBudgetAndAmount(user, deDuctEntity, detail, budgetMoneyMap));
                }
            }
        }

        return budgetMoneyMap;
    }

    private Map<String, Pair<IObjectData, BigDecimal>> getManualDeductBudgetMap(User user, BudgetNewConsumeRulePO budgetConsumeRulePO, IObjectData masterData, Map<String, List<IObjectData>> detailsMap, Map<String, IObjectData> id2DataMap) {
        Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap = new HashMap<>();
        for (BudgetTableManualNodeEntity manualNodeEntity : budgetConsumeRulePO.getBudgetTableManualNodes()) {
            BudgetTableFrozenEntity frozenEntity = manualNodeEntity.getBudgetTableFrozenEntity();
            BudgetTableDeDuctEntity deDuctEntity = manualNodeEntity.getBudgetTableDeDuctEntity();
            if (masterData.getDescribeApiName().equals(deDuctEntity.getApiName())) {
                getDeductBudgetAndAmount(user, frozenEntity, deDuctEntity, masterData, budgetMoneyMap, id2DataMap, budgetConsumeRulePO.getApiName(), false);
            } else {
                List<IObjectData> detailList = detailsMap.getOrDefault(deDuctEntity.getApiName(), Lists.newArrayList());
                if (CollectionUtils.isNotEmpty(detailList)) {
                    detailList.forEach(detail -> getDeductBudgetAndAmount(user, frozenEntity, deDuctEntity, detail, budgetMoneyMap, id2DataMap, budgetConsumeRulePO.getApiName(), true));
                }
            }
        }

        return budgetMoneyMap;
    }

    private void getDeductBudgetAndAmount(User user, BudgetTableFrozenEntity frozenEntity, BudgetTableDeDuctEntity deDuctEntity, IObjectData relatedData, Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap, Map<String, IObjectData> id2DataMap, String frozenApiName, boolean isDetailObject) {
        IObjectData referenceObject = null;
        String objectKey = null;
        String referenceObjectId = null;
        if (!frozenEntity.getApiName().equals(deDuctEntity.getApiName())) {
            //扣减不同对象
            referenceObjectId = relatedData.get(deDuctEntity.getRelationField(), String.class);
            if (Strings.isNullOrEmpty(referenceObjectId)) {
                log.info("{}.{} no referenceObjectId fill jump.", relatedData.getName(), relatedData.getId());
                return;
            }
            objectKey = frozenEntity.getApiName() + "." + referenceObjectId;
            referenceObject = id2DataMap.get(objectKey);
            if (referenceObject == null) {
                referenceObject = serviceFacade.findObjectDataIncludeDeleted(user, referenceObjectId, frozenEntity.getApiName());
                id2DataMap.putIfAbsent(objectKey, referenceObject);
            }

            if (!CommonFields.LIFE_STATUS__NORMAL.equals(referenceObject.get(CommonFields.LIFE_STATUS))) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_29));
            }
        } else {
            //扣减自己
            objectKey = relatedData.getDescribeApiName() + "." + relatedData.getId();
            referenceObject = relatedData;
            id2DataMap.putIfAbsent(objectKey, referenceObject);
        }

        if (!id2DataMap.containsKey("frozenMaster")) {
            if (isDetailObject) {
                id2DataMap.put("frozenMaster", serviceFacade.findObjectDataIgnoreAll(user, referenceObject.get(frozenEntity.getMasterDetail(), String.class), frozenApiName));
            } else {
                id2DataMap.put("frozenMaster", referenceObject);
            }
        }

        String budgetId = referenceObject.get(frozenEntity.getRelationField(), String.class);
        judgeTheSameData(isDetailObject, referenceObject, frozenEntity, id2DataMap);

        //扣减没有预算表就跳过
        if (Strings.isNullOrEmpty(budgetId)) {
            log.info("{}.{} no budget id fill jump.", referenceObject.getName(), referenceObject.getId());
            return;
        }


        IObjectData budget = budgetMoneyMap.containsKey(budgetId) ? budgetMoneyMap.get(budgetId).getFirst() : serviceFacade.findObjectDataIgnoreAll(user, budgetId, ApiNames.TPM_BUDGET_ACCOUNT);
        BigDecimal amount = relatedData.get(deDuctEntity.getAmountField(), BigDecimal.class);
        if (amount == null) {
            throw new ValidateException(relatedData.getName() != null ? String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_30), relatedData.getName()) : I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_31));
        }
        amountZeroJudge(amount);
        Pair<IObjectData, BigDecimal> pair = budgetMoneyMap.getOrDefault(budgetId, new Pair<>(budget, BigDecimal.ZERO));
        pair.setSecond(pair.getSecond().add(amount));
        budgetMoneyMap.putIfAbsent(budgetId, pair);
    }

    //判断所有的数据来自同一个主从
    private void judgeTheSameData(boolean isDetailObject, IObjectData referenceObject, BudgetTableFrozenEntity frozenEntity, Map<String, IObjectData> id2DataMap) {
        String masterDataId = isDetailObject ? referenceObject.get(frozenEntity.getMasterDetail(), String.class) : referenceObject.getId();
        if (!id2DataMap.containsKey("masterId")) {
            IObjectData objectData = new ObjectData();
            objectData.setId(masterDataId);
            id2DataMap.put("masterId", objectData);
        } else {
            if (!masterDataId.equals(id2DataMap.get("masterId").getId())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_32));
            }
        }
    }

    private void getFrozenBudgetAndAmount(User user, BudgetTableFrozenEntity frozenEntity, String nodeId, IObjectData relatedData, IObjectData masterData, ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO, boolean enableWithholding) {
        String budgetId = relatedData.get(frozenEntity.getRelationField(), String.class);

        if (Strings.isNullOrEmpty(budgetId)) {
            if (!enableWithholding) {
                log.info("{}.{} no budget id fill jump.", relatedData.getName(), relatedData.getId());
                return;
            }

            IObjectData withholdingObj = getWithholdingAmount(user, nodeId, relatedData, masterData, frozenEntity, consumeRuleBudgetAmountDTO.getBudgetNewConsumeRulePO(), consumeRuleBudgetAmountDTO.getBusinessId(), consumeRuleBudgetAmountDTO.getTraceId());
            if (withholdingObj == null) {
                log.info("因为缺失部分字段，没有生成预提。");
                return;
            }
            String identityKey = withholdingObj.get(TPMBudgetProvisionObjFields.IDENTITY_KEY, String.class);
            if (!TPMGrayUtils.isAllowConsumeRuleFreezeRepeatableBudgetTable(user.getTenantId()) && consumeRuleBudgetAmountDTO.getWithholdingMoneyMap().containsKey(identityKey)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_33));
            }
            if (consumeRuleBudgetAmountDTO.getWithholdingMoneyMap().containsKey(identityKey)) {
                IObjectData oldWithholdingObj = consumeRuleBudgetAmountDTO.getWithholdingMoneyMap().get(identityKey);
                mergeWithholdingPair(oldWithholdingObj, withholdingObj);
            } else {
                consumeRuleBudgetAmountDTO.getWithholdingMoneyMap().put(identityKey, withholdingObj);
            }
        } else {
            if (!TPMGrayUtils.isAllowConsumeRuleFreezeRepeatableBudgetTable(user.getTenantId()) && consumeRuleBudgetAmountDTO.getBudgetMoneyMap().containsKey(budgetId)) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_33));
            }
            IObjectData budget = consumeRuleBudgetAmountDTO.getBudgetMoneyMap().containsKey(budgetId) ? consumeRuleBudgetAmountDTO.getBudgetMoneyMap().get(budgetId).getFirst() : serviceFacade.findObjectDataIgnoreAll(user, budgetId, ApiNames.TPM_BUDGET_ACCOUNT);
            BigDecimal amount = relatedData.get(frozenEntity.getAmountField(), BigDecimal.class);
            if (amount == null) {
                throw new ValidateException(relatedData.getName() != null ? String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_34), relatedData.getName()) : I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_35));
            }
            amountZeroJudge(amount);
            Pair<IObjectData, BigDecimal> pair = consumeRuleBudgetAmountDTO.getBudgetMoneyMap().getOrDefault(budgetId, new Pair<>(budget, BigDecimal.ZERO));
            pair.setSecond(pair.getSecond().add(amount));
            consumeRuleBudgetAmountDTO.getBudgetMoneyMap().putIfAbsent(budgetId, pair);
        }
    }

    private void getFrozenBudgetAndAmountV2(User user, BudgetTableFrozenEntity frozenEntity, String nodeId, IObjectData relatedData, IObjectData masterData, ConsumeRuleBudgetAmountDTO consumeRuleBudgetAmountDTO) {
        String budgetId = relatedData.get(frozenEntity.getRelationField(), String.class);

        if (!TPMGrayUtils.isAllowConsumeRuleFreezeRepeatableBudgetTable(user.getTenantId()) && consumeRuleBudgetAmountDTO.getBudgetMoneyMap().containsKey(budgetId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_33));
        }
        IObjectData budget = consumeRuleBudgetAmountDTO.getBudgetMoneyMap().containsKey(budgetId) ? consumeRuleBudgetAmountDTO.getBudgetMoneyMap().get(budgetId).getFirst() : serviceFacade.findObjectDataIgnoreAll(user, budgetId, ApiNames.TPM_BUDGET_ACCOUNT);
        BigDecimal amount = relatedData.get(frozenEntity.getAmountField(), BigDecimal.class);
        if (amount == null) {
            return;
        }
        amountZeroJudge(amount);
        Pair<IObjectData, BigDecimal> pair = consumeRuleBudgetAmountDTO.getBudgetMoneyMap().getOrDefault(budgetId, new Pair<>(budget, BigDecimal.ZERO));
        pair.setSecond(pair.getSecond().add(amount));
        consumeRuleBudgetAmountDTO.getBudgetMoneyMap().putIfAbsent(budgetId, pair);
    }

    private void mergeWithholdingPair(IObjectData oldWithholdingAmount, IObjectData withholdingAmount) {
        oldWithholdingAmount.set(TPMBudgetProvisionObjFields.PROVISION_AMOUNT, oldWithholdingAmount.get(TPMBudgetProvisionObjFields.PROVISION_AMOUNT, BigDecimal.class, BigDecimal.ZERO).add(withholdingAmount.get(TPMBudgetProvisionObjFields.PROVISION_AMOUNT, BigDecimal.class, BigDecimal.ZERO)));
        List oldList = oldWithholdingAmount.get(TPMBudgetProvisionObjFields.DETAILS_LIST, List.class, Lists.newArrayList());
        List newList = withholdingAmount.get(TPMBudgetProvisionObjFields.DETAILS_LIST, List.class, Lists.newArrayList());
        newList.stream().forEach(detail -> ((Map) detail).put(TPMBudgetProvisionRecordObjFields.BUDGET_PROVISION_ID, oldWithholdingAmount.getId()));
        oldList.addAll(newList);
    }

    //targetField实际可能是不相同的
    private String getIdentityKey(String tenantId, IObjectData object, BudgetTableProvisionEntity budgetTableProvisionEntity) {
        StringBuilder dimensionKey = new StringBuilder(tenantId + "." + budgetTableProvisionEntity.getBudgetType() + "." + budgetTableProvisionEntity.getBudgetLevel());

        boolean forceCheck = TPMGrayUtils.forceCheckWithholdingField(tenantId);
        for (BudgetFieldRelationEntity entity : budgetTableProvisionEntity.getFieldRelation()) {
            Object value;
            if (entity.getOperator().equalsIgnoreCase("eql")) {
                String timeDimension = "year";
                if (entity.getSourceField().equals(TPMBudgetAccountFields.BUDGET_PERIOD_MONTH)) {
                    timeDimension = "month";
                } else if (entity.getSourceField().equals(TPMBudgetAccountFields.BUDGET_PERIOD_QUARTER)) {
                    timeDimension = "quarter";
                }
                //预提的相关值没填写
                if (object.get(entity.getTargetField(), Long.class) == null) {
                    if (forceCheck) {
                        throw new ValidateException(I18N.text(I18NEnums.WITHHOLDING_MAPPING_IS_LACKING.getCode()));
                    } else {
                        return null;
                    }
                }
                value = String.valueOf(fiscalTimeService.correctPeriodTime(tenantId, timeDimension, object.get(entity.getTargetField(), Long.class)));
            } else {
                value = object.get(entity.getTargetField());
            }
            //预提的相关值没填写
            if (Objects.isNull(value) || "".equals(value) || (value instanceof List && ((List<?>) value).isEmpty())) {
                if (forceCheck) {
                    throw new ValidateException(I18N.text(I18NEnums.WITHHOLDING_MAPPING_IS_LACKING.getCode()));
                } else {
                    return null;
                }
            }
            //不同对象的targetField实际可能是不相同的  所以用source
            dimensionKey.append(String.format("#%s-%s", entity.getSourceField(), value));
        }
        return dimensionKey.toString();
    }

    private IObjectData getWithholdingAmount(User user, String nodeId, IObjectData relatedData, IObjectData masterData, BudgetTableFrozenEntity frozenEntity, BudgetNewConsumeRulePO budgetNewConsumeRulePO, String businessId, String traceId) {
        IObjectData withholdingObj = new ObjectData();
        withholdingObj.setOwner(Lists.newArrayList("-10000"));
        withholdingObj.setTenantId(user.getTenantId());
        withholdingObj.setRecordType("default__c");
        withholdingObj.setId(IdGenerator.get());
        withholdingObj.setDescribeApiName(ApiNames.TPM_BUDGET_PROVISION_OBJ);
        withholdingObj.set(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_DATA_ID, masterData.getId());
        withholdingObj.set(TPMBudgetProvisionObjFields.MASTER_RELATED_OBJECT_API_NAME, masterData.getDescribeApiName());
        withholdingObj.set(TPMBudgetProvisionObjFields.PROVISION_STATUS, TPMBudgetProvisionObjFields.ProvisionStatus.IN_EFFECT);
        String identityKey = getIdentityKey(user.getTenantId(), relatedData, frozenEntity.getBudgetTableProvisionEntity());
        if (Strings.isNullOrEmpty(identityKey)) {
            return null;
        }
        withholdingObj.set(TPMBudgetProvisionObjFields.IDENTITY_KEY, identityKey);
        withholdingObj.set(TPMBudgetProvisionObjFields.BIZ_TRACE_ID, businessId);
        withholdingObj.set(TPMBudgetProvisionObjFields.APPROVAL_TRACE_ID, traceId);
        withholdingObj.set(TPMBudgetProvisionObjFields.CONSUME_RULE_ID, budgetNewConsumeRulePO.getId().toString());
        withholdingObj.set(TPMBudgetProvisionObjFields.CONSUME_RULE_NAME, budgetNewConsumeRulePO.getName());
        withholdingObj.set(TPMBudgetProvisionObjFields.BUSINESS_TYPE, BizType.CONSUME.value());
        withholdingObj.set(TPMBudgetProvisionObjFields.MAIN_TYPE, MainType.FREEZE.value());
        withholdingObj.set(TPMBudgetProvisionObjFields.OPERATE_MARK_DETAIL, BudgetDetailOperateMark.CONSUME_FREEZE_THEN_DEDUCTION_FIRST.value());
        BigDecimal amount = relatedData.get(frozenEntity.getAmountField(), BigDecimal.class);
        validateBudgetLevel(user.getTenantId(), relatedData, frozenEntity.getBudgetTableProvisionEntity());
        fillBudgetField(withholdingObj, relatedData, frozenEntity.getBudgetTableProvisionEntity());
        if (amount == null) {
            throw new ValidateException("预提金额未填写。");
        }
        withholdingObj.set(TPMBudgetProvisionObjFields.DETAILS_LIST, Lists.newArrayList(fromWithholdingDetail(user.getTenantId(), withholdingObj.getId(), relatedData, nodeId, amount)));
        withholdingObj.set(TPMBudgetProvisionObjFields.PROVISION_AMOUNT, amount);
        return withholdingObj;
    }

    private void fillBudgetField(IObjectData withholdingObj, IObjectData relatedData, BudgetTableProvisionEntity budgetTableProvisionEntity) {
        withholdingObj.set(TPMBudgetProvisionObjFields.BUDGET_TYPE, budgetTableProvisionEntity.getBudgetType());
        withholdingObj.set(TPMBudgetProvisionObjFields.BUDGET_LEVEL, budgetTableProvisionEntity.getBudgetLevel());
        budgetTableProvisionEntity.getFieldRelation().forEach(entity -> {
            withholdingObj.set(entity.getSourceField(), relatedData.get(entity.getTargetField()));
        });
    }

    private void validateBudgetLevel(String tenantId, IObjectData relatedData, BudgetTableProvisionEntity budgetTableProvisionEntity) {
        String budgetType = budgetTableProvisionEntity.getBudgetType();
        String nodeId = budgetTableProvisionEntity.getBudgetLevel();
        BudgetTypePO type = budgetTypeDAO.get(tenantId, budgetType);
        BudgetTypeNodeEntity node = type.getNodes().stream().filter(f -> f.getNodeId().equals(nodeId)).findFirst().orElseThrow(() -> new ValidateException("未找到预提设置里的预算节点"));


        budgetTableProvisionEntity.getFieldRelation().forEach(fieldRelation -> {
            if (fieldRelation.getSourceField().equals(TPMBudgetAccountFields.BUDGET_DEPARTMENT) &&
                    budgetCompareService.notTheSpecifiedDepartmentLevel(tenantId, node.getDepartmentDimensionLevel(), Integer.parseInt(relatedData.get(fieldRelation.getTargetField(), List.class).get(0).toString()))) {
                throw new ValidateException(String.format(I18N.text(I18NKeys.BUDGET_ACCOUNT_BUDGET_DEPARTMENT_LEVEL_IS_NOT_ALLOW), I18N.text(BudgetTypeManager.DEPARTMENT_LEVEL_LABEL_MAP.get(node.getDepartmentDimensionLevel()))));
            }
            BudgetDimensionEntity dimension = node.getDimensions().stream().filter(n -> n.getApiName().equals(fieldRelation.getSourceField())).findFirst().orElse(null);
            if (dimension == null) {
                log.info("层级不匹配,fieldRelation={},node={}", fieldRelation, node);
                return;
            }
            if (fieldRelation.getSourceField().equals(TPMBudgetAccountFields.BUDGET_SUBJECT_ID) &&
                    budgetSubjectService.notTheSpecifiedLevel(tenantId, relatedData.get(fieldRelation.getTargetField(), String.class), dimension.getLevel())) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_SUBJECT_LEVEL_ERROR));
            }
            if (fieldRelation.getSourceField().equals(TPMBudgetAccountFields.PRODUCT_CATEGORY_ID) &&
                    productCategoryService.notTheSpecifiedProductCategoryLevel(tenantId, dimension.getLevel(), relatedData.get(fieldRelation.getTargetField(), String.class))) {
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_ACCOUNT_PRODUCT_LEVEL_ERROR));
            }
        });
    }

    private Map<String, Object> fromWithholdingDetail(String tenantId, String masterId, IObjectData relatedData, String nodeId, BigDecimal amount) {
        IObjectData data = new ObjectData();
        data.setDescribeApiName(ApiNames.TPM_BUDGET_PROVISION_RECORD_OBJ);
        data.setOwner(Lists.newArrayList("-10000"));
        data.setTenantId(tenantId);
        data.setRecordType("default__c");
        data.set(TPMBudgetProvisionRecordObjFields.BUDGET_PROVISION_ID, masterId);
        data.set(TPMBudgetProvisionRecordObjFields.CONSUME_NODE_ID, nodeId);
        data.set(TPMBudgetProvisionRecordObjFields.AMOUNT, amount);
        data.set(TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_API_NAME, relatedData.getDescribeApiName());
        data.set(TPMBudgetProvisionRecordObjFields.RELATED_OBJECT_DATA_ID, relatedData.getId());
        return ObjectDataExt.toMap(data);
    }

    private void getDirectDeductBudgetAndAmount(User user, BudgetTableDeDuctEntity deDuctEntity, IObjectData relatedData, Map<String, Pair<IObjectData, BigDecimal>> budgetMoneyMap) {
        String budgetId = relatedData.get(deDuctEntity.getRelationField(), String.class);
        if (Strings.isNullOrEmpty(budgetId)) {
            log.info("{}.{} no budget id fill jump.", relatedData.getName(), relatedData.getId());
            return;
        }
        if (!TPMGrayUtils.isAllowConsumeRuleFreezeRepeatableBudgetTable(user.getTenantId()) && budgetMoneyMap.containsKey(budgetId)) {
            throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_36));
        }
        IObjectData budget = budgetMoneyMap.containsKey(budgetId) ? budgetMoneyMap.get(budgetId).getFirst() : serviceFacade.findObjectDataIgnoreAll(user, budgetId, ApiNames.TPM_BUDGET_ACCOUNT);
        BigDecimal amount = relatedData.get(deDuctEntity.getAmountField(), BigDecimal.class);
        if (amount == null) {
            throw new ValidateException(relatedData.getName() != null ? String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_37), relatedData.getName()) : I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_38));
        }
        amountZeroJudge(amount);
        Pair<IObjectData, BigDecimal> pair = budgetMoneyMap.getOrDefault(budgetId, new Pair<>(budget, BigDecimal.ZERO));
        pair.setSecond(pair.getSecond().add(amount));
        budgetMoneyMap.putIfAbsent(budgetId, pair);
    }

    private BudgetNewConsumeRulePO getConsumeRuleByMasterData(String tenantId, IObjectData masterData) {
        String recordType = getObjectRuleRecordType(masterData);
        return budgetNewConsumeRuleDAO.findUniqueRuleByApiName(tenantId, masterData.getDescribeApiName(), recordType);
    }

    private boolean isMathRuleType(String describeApiName, String recordType, IObjectData relatedData) {
        String relatedRecordType = getObjectRuleRecordType(relatedData);
        return describeApiName.equals(relatedData.getDescribeApiName()) && recordType.equals(relatedRecordType);
    }

    private String getObjectRuleRecordType(IObjectData masterData) {
        String recordType = masterData.getRecordType();
        if (ApiNames.TPM_ACTIVITY_OBJ.equals(masterData.getDescribeApiName()) || ApiNames.TPM_ACTIVITY_UNIFIED_CASE_OBJ.equals(masterData.getDescribeApiName())) {
            recordType = masterData.get(TPMActivityFields.ACTIVITY_TYPE, String.class);
        }
        return recordType;
    }

    private void mergeApprovalChange(IObjectData masterData, Map<String, List<IObjectData>> detailsMap, Map<String, Object> masterChange, Map<String, Object> detailChanges) {

        IObjectDescribe masterDescribe = serviceFacade.findDescribeAndLayout(User.systemUser(masterData.getTenantId()), masterData.getDescribeApiName(), false, null).getObjectDescribe();
        Set<String> fieldsSet = masterDescribe.getFieldDescribeMap().keySet();

        masterChange.forEach((k, v) -> {
            if (fieldsSet.contains(k)) {
                masterData.set(k, v);
            }
        });
        if (MapUtils.isNotEmpty(detailsMap) && MapUtils.isNotEmpty(detailChanges)) {
            detailChanges.forEach((detailApiName, changeOperateObj) -> {
                List<IObjectData> details = detailsMap.getOrDefault(detailApiName, Lists.newArrayList());
                Map<String, IObjectData> id2DetailMap = details.stream().collect(Collectors.toMap(DBRecord::getId, v -> v, (before, after) -> before));
                JSONObject operateMap = JSON.parseObject(JSON.toJSONString(changeOperateObj));
                for (String operation : CHANGE_OPERATE_LIST) {
                    if (!operateMap.containsKey(operation)) {
                        continue;
                    }
                    switch (operation) {
                        case "Add":
                            operateMap.getJSONArray(operation).stream().map(v -> new ObjectData((JSONObject) v)).forEach(details::add);
                            break;
                        case "Edit":
                            JSONObject changesMap = operateMap.getJSONObject(operation);
                            changesMap.forEach((id, changeDetail) -> {
                                IObjectData detail = id2DetailMap.get(id);
                                if (detail == null) {
                                    log.warn("can not find edit detail.id:{},apiName:{}", id, detailApiName);
                                    return;
                                }
                                ((JSONObject) changeDetail).forEach(detail::set);
                            });
                            break;
                        case "Delete":
                        case "Abolish":
                            List<String> ids = operateMap.getJSONArray(operation).toJavaObject(new TypeReference<List<String>>() {
                            });
                            ids.forEach(id -> {
                                IObjectData detail = id2DetailMap.get(id);
                                details.remove(detail);
                            });
                            break;
                        default:
                    }
                }
                detailsMap.put(detailApiName, details);
            });
        }

    }

    private void changeBudgetMoneyMap(Map<String, Pair<IObjectData, BigDecimal>> originalMonneyMap, MainType mainType, boolean needUnfrozen, Map<String, List<Pair<MainType, BigDecimal>>> result) {
        if (result == null) {
            throw new ValidateException("result map is null.");
        }
        originalMonneyMap.forEach((budgetId, pair) -> {
            List<Pair<MainType, BigDecimal>> budgetMoneyList = result.getOrDefault(budgetId, new ArrayList<>());
            if (needUnfrozen) {
                budgetMoneyList.add(new Pair<>(MainType.UNFREEZE, pair.getSecond()));
            }
            budgetMoneyList.add(new Pair<>(mainType, pair.getSecond()));
            result.putIfAbsent(budgetId, budgetMoneyList);
        });
    }

    private void validateOverDeductConsumableAmount(IBudgetOperator operator, String describeApiName, String frozenDataName, BigDecimal deductAmount, BigDecimal frozenAmount, BigDecimal additionAmount) {
        BigDecimal consumableAmount = operator.consumableAmount();
        if (consumableAmount.compareTo(additionAmount) < 0) {
            String describeName = getDisplayName(operator.getUser().getTenantId(), describeApiName);
            String tip = String.format(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_40), describeName, frozenDataName, deductAmount, frozenAmount, additionAmount, consumableAmount.setScale(2, RoundingMode.DOWN));
            if (TPMGrayUtils.budgetValidateMessageDesensitization(operator.getUser().getTenantId())) {
                log.info(String.format("本操作需扣减「%s-%s」%s 元费用，已按该单据可扣减金额（%s元）满额扣减，并需额外扣减关联预算表 %s元，但该预算表可用金额 %s 元，不足扣减，请将需扣减金额字段值调低后重新提交。", describeName, frozenDataName, deductAmount, frozenAmount, additionAmount, consumableAmount.setScale(2, RoundingMode.DOWN)));
                throw new ValidateException(I18N.text(I18NKeys.BUDGET_CONSUME_V2_SERVICE_39));
            } else {
                throw new ValidateException(tip);
            }
        }
    }

    private String getDisplayName(String tenantId, String describeApiName) {
        return serviceFacade.findDisplayNameByApiNames(tenantId, Lists.newArrayList(describeApiName)).get(describeApiName);
    }

    private void invalidValidateBefore(String tenantId, List<IObjectData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        if (dataList.size() > 10) {
            throw new ValidateException("批量作废预算消费规则管控的对象，不能超过10个。");
        }
        List<String> names = Lists.newArrayList();
        dataList.forEach(data -> {
            BudgetNewConsumeRulePO consumeRulePO = getConsumeRuleByMasterData(tenantId, data);
            if (consumeRulePO == null) {
                return;
            }
            boolean selfDeductFlag = ConsumeRuleType.FREEZE_DEDUCTION.value().equals(consumeRulePO.getRuleType()) && consumeRulePO.getDeductType() == 0 && !TPMActivityFields.CLOSE_STATUS__CLOSED.equals(data.get(TPMActivityFields.CLOSE_STATUS, String.class, ""));
            boolean exitsDetail = budgetAccountDetailService.existsConsumeRuleDetailByObjectData(tenantId, consumeRulePO.getId().toString(), data.getDescribeApiName(), data.getId());
            //有明细且不是扣减自身未结案的
            if (exitsDetail && !selfDeductFlag) {
                names.add(data.getName());
            }
        });
        if (!CollectionUtils.isEmpty(names)) {
            throw new ValidateException(String.format("下列数据已执行消费规则的扣减逻辑，请勿删除。主属性（name）：%s", FormatUtil.join(names, ",")));
        }
    }
}
