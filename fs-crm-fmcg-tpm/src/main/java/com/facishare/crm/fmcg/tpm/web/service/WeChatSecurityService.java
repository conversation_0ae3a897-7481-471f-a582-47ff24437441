package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.appserver.checkins.api.model.GetOpenIdByToken;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.tpm.reward.dto.WeChatArg;
import com.facishare.crm.fmcg.tpm.web.annotation.WeChatSecurityApi;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IWeChatSecurityService;
import com.fxiaoke.common.StopWatch;
import com.fxiaoke.common.release.GrayRelease;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

//IgnoreI18nFile
@Service
@Slf4j
@SuppressWarnings("Duplicates")
public class WeChatSecurityService implements IWeChatSecurityService {

    @Resource
    private ShopMMService shopMMService;
    @Resource
    private LocalAuthenticationCmd localAuthenticationCmd;

    public static final String COMMON_SECURITY_FAILED_MESSAGE = "request blocked";
    public static final String COMMON_AUTHENTICATION_FAILED_MESSAGE = "authentication failed";
    public static final int COMMON_SECURITY_FAILED_CODE = 320002404;

    @Override
    public void validate(WeChatArg arg, WeChatSecurityApi weChatSecurityApi) {
        log.info("validate arg : {}", JSON.toJSONString(arg));

        if (!enableWeChatSecurityCheck(arg)) {
            return;
        }
        validateByWeChatToken(arg, weChatSecurityApi);
    }

    private void validateByWeChatToken(WeChatArg arg, WeChatSecurityApi weChatSecurityApi) {
        StopWatch watch = new StopWatch("WeChatSecurity.WeChat.validate");
        boolean enableToken = Boolean.TRUE.equals(weChatSecurityApi.needToken());
        try {
            // 开启后需要将其他入口调用传入的身份信息清空掉
            this.cleanAuthentication(arg);
            watch.lap("cleanAuthentication");

            // 1.appId 格式校验、2.appId 白名单、3.appId 限流
            this.appIdSecurityCheck(arg);
            watch.lap("appIdSecurityCheck");

            if (enableToken) {
                // 1.token 格式校验、2.token 黑名单
                this.tokenSecurityCheck(arg);
                watch.lap("tokenSecurityCheck");
                // 1.LocalAuthentication 调用方和纷享协商了密钥，生成身份信息 token 传给纷享进行校验
                // 2.WeChatAuthentication 调用房将微信 app secret 提供给纷享，将微信 token 传给纷享进行校验
                if (isLocalAuthentication(arg)) {
                    this.localAuthentication(arg);
                    watch.lap("localAuthentication");
                } else {
                    this.weChatAuthentication(arg);
                    watch.lap("weChatAuthentication");
                }
                // 1.unionId 黑名单、2.unionId 限流
                this.unionIdSecurityCheck(arg);
                watch.lap("unionIdSecurityBlock");
            }

        } catch (BizException ex) {
            throw ex;
        } catch (Exception ex) {
            log.error("WeChat request blocked by unknown exception : ", ex);
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        } finally {
            watch.logSlow(100);
        }
    }

    private boolean isLocalAuthentication(WeChatArg arg) {
        return GrayRelease.isAllow("fmcg", "fmcg_tpm_wechat_security_check_local_authentication", String.format("app.%s.%s", arg.getAppId(), arg.getEnvironment()));
    }

    private void localAuthentication(WeChatArg arg) {
        LocalAuthenticationCmd.LocalAuthentication auth;
        try {
            auth = localAuthenticationCmd.verify(arg.getAppId(), arg.getToken());
            log.info("local authentication result : {}", JSON.toJSONString(auth));
        } catch (Exception ex) {
            throw new BizException(COMMON_AUTHENTICATION_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (!Objects.equal(arg.getAppId(), auth.getAppId())) {
            throw new BizException(COMMON_AUTHENTICATION_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (Strings.isNullOrEmpty(auth.getOpenId()) || Strings.isNullOrEmpty(auth.getUnionId())) {
            throw new BizException(COMMON_AUTHENTICATION_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }

        arg.setOpenId(auth.getOpenId());
        arg.setUnionId(auth.getUnionId());
    }

    private boolean enableWeChatSecurityCheck(WeChatArg arg) {
        return !Boolean.TRUE.equals(arg.getSkipWxValidate()) && GrayRelease.isAllow("fmcg", "fmcg_tpm_wechat_security_check", String.format("app.%s.%s", arg.getAppId(), arg.getEnvironment()));
    }

    private void unionIdSecurityCheck(WeChatArg arg) {
        if (Strings.isNullOrEmpty(arg.getUnionId())) {
            log.error("unionId is null");
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (GrayRelease.isAllow("fmcg", "fmcg_tpm_wechat_black_union_id_list", arg.getUnionId())) {
            log.error("WeChat request blocked by black union id : {}", arg.getUnionId());
            throw new BizException("很遗憾，没有中奖，欢迎下次参与！", 320002403);
        }
        if (this.requestOverLimit("fmcg_tpm_wechat_union_id", arg.getUnionId())) {
            log.error("WeChat request blocked by limited union id : {}", arg.getUnionId());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
    }

    private void appIdSecurityCheck(WeChatArg arg) {
        if (!this.isValidAppId(arg.getAppId())) {
            log.error("WeChat request blocked by fake app id : {}", arg.getAppId());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (!GrayRelease.isAllow("fmcg", "fmcg_tpm_wechat_white_app_id_list", arg.getAppId())) {
            log.error("WeChat request blocked by black app id : {}", arg.getAppId());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (this.requestOverLimit("fmcg_tpm_wechat_app_id", arg.getAppId())) {
            log.error("WeChat request blocked by limited app id : {}", arg.getAppId());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
    }

    private boolean requestOverLimit(String key, String data) {
        return false;
    }

    private boolean isValidAppId(String appId) {
        return !Strings.isNullOrEmpty(appId);
    }

    private void tokenSecurityCheck(WeChatArg arg) {
        if (!this.isValidToken(arg.getToken())) {
            log.error("WeChat request blocked by fake token : {}", arg.getToken());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
        if (GrayRelease.isAllow("fmcg", "fmcg_tpm_wechat_black_token_list", arg.getToken())) {
            log.error("WeChat request blocked by black token : {}", arg.getToken());
            throw new BizException(COMMON_SECURITY_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }
    }

    private boolean isValidToken(String token) {
        return !Strings.isNullOrEmpty(token);
    }

    private void weChatAuthentication(WeChatArg arg) {
        GetOpenIdByToken.Args tokenArg = new GetOpenIdByToken.Args();
        tokenArg.setAppId(arg.getAppId());
        tokenArg.setToken(arg.getToken());
        tokenArg.setTenantId("1");

        if ("1".equals(arg.getAppId()) && "jiege666".equals(arg.getToken())) {
            arg.setAppId("1");
            arg.setOpenId("jiege666");
            arg.setUnionId("jiege666");
            return;
        }
        GetOpenIdByToken.Result auth;
        try {
            auth = shopMMService.getOpenIdByToken(tokenArg);
            log.info("wechat authentication result : {}", JSON.toJSONString(auth));
        } catch (Exception ex) {
            throw new BizException(COMMON_AUTHENTICATION_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }

        if (Strings.isNullOrEmpty(auth.getOpenId()) || Strings.isNullOrEmpty(auth.getUnionId())) {
            throw new BizException(COMMON_AUTHENTICATION_FAILED_MESSAGE, COMMON_SECURITY_FAILED_CODE);
        }

        arg.setOpenId(auth.getOpenId());
        arg.setUnionId(auth.getUnionId());
    }

    private void cleanAuthentication(WeChatArg arg) {
        arg.setOpenId(null);
        arg.setUnionId(null);
    }
}
