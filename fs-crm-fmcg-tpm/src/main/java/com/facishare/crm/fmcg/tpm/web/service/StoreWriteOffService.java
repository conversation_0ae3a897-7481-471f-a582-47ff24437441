package com.facishare.crm.fmcg.tpm.web.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.ActivityTypeExt;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.crm.fmcg.tpm.utils.TimeUtils;
import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteOffBatchData;
import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteOffCashingProductData;
import com.facishare.crm.fmcg.tpm.web.contract.StoreWriteOffDataList;
import com.facishare.crm.fmcg.tpm.web.contract.ValidationStoreWriteOffData;
import com.facishare.crm.fmcg.tpm.web.manager.ActivityTypeManager;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IStoreWriteOffService;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ValidateRuleService;
import com.facishare.paas.appframework.metadata.dto.RuleResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.common.StopWatch;
import com.github.trace.executor.MonitorTaskWrapper;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
//IgnoreI18nFile
@Service
@SuppressWarnings("Duplicates")
@Slf4j
public class StoreWriteOffService extends BaseService implements IStoreWriteOffService {

    @Resource(name = "describeLogicService")
    private DescribeLogicService describeService;

    @Resource
    private ActivityTypeManager activityTypeManager;
    @Resource
    protected ValidateRuleService validateRuleService;
    @Override
    public StoreWriteOffBatchData.Result batchUpdate(StoreWriteOffBatchData.Arg arg) {
        StoreWriteOffBatchData.Result result = new StoreWriteOffBatchData.Result();
        ApiContext context = ApiContextManager.getContext();
        //批量去跟更新 门店费用核销的字段
        List<String> idList = arg.getIdList();
        if (CollectionUtils.isEmpty(idList)) {
            return result;
        }
        StopWatch watch = new StopWatch("StoreWriteOff_batchUpdate");
        watch.start("updateObjectDataV2 start --");
        updateObjectDataV2(arg, context.getTenantId(), idList, result);
        watch.stop();
        watch.logSlow(1000L);
        return result;
    }

    private void validateObjectData(String tenantId, List<IObjectData> objectDataList, IObjectDescribe describe) {
        User user = new User(tenantId, "-10000");
        RuleResult validateRule = validateRuleService.validateRule(user, "update", describe, objectDataList);
        if (validateRule != null) {
            if (!StringUtils.isEmpty(validateRule.getFailMessage())) {
                throw new ValidateException(validateRule.getFailMessage());
            }
        }
    }

    private void updateObjectDataV2(StoreWriteOffBatchData.Arg arg,
                                    String tenantId,
                                    List<String> idList,
                                    StoreWriteOffBatchData.Result result) {

        //门店核销的字段
        JSONObject data = arg.getData();
        log.info("update object data is {}", data);
        String curId = arg.getId();
        Map<String, Object> object = new HashMap<>(data);
        buildStoreWriteOff(arg, tenantId, object, data);

        List<IObjectData> objectDataList = serviceFacade.findObjectDataByIds(tenantId, idList, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        // 校验核销的数据，是否存在当月的，存在则返回拦截。(元气)
        if (TPMGrayUtils.isYuanQi(tenantId)){
            validateDataListByYears(objectDataList);
        }

        List<IObjectData> copyList = ObjectDataExt.copyList(objectDataList);
        IObjectDescribe describe = describeService.findObject(tenantId, ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        objectDataList.forEach(objectData -> object.forEach(objectData::set));
        //校验验证规则
        validateObjectData(tenantId, objectDataList, describe);
        //更新数据
        log.info("update object db data is {}", object);
        serviceFacade.batchUpdateWithMap(getUser(tenantId, arg.getWriteOffOwner()), objectDataList, object);

        //单条返回数据
        if (CollectionUtils.isNotEmpty(objectDataList)) {
            IObjectData iObjectData = objectDataList.stream().filter(v -> v.getId().equals(curId)).findFirst().orElse(null);
            if (iObjectData != null) {
                fillWebDetailData(tenantId, describe, iObjectData);
                result.setObjectData(ObjectDataExt.of(iObjectData).toMap());
            }
        }

        // 记录
        ParallelUtils.createParallelTask().submit(MonitorTaskWrapper.wrap(() -> {
            for (IObjectData dbData : copyList) {
                IObjectData objectData = ObjectDataExt.of(dbData).copy();
                object.forEach(objectData::set);
                Map<String, Object> diff = ObjectDataExt.of(dbData).diff(objectData, describe);
                serviceFacade.log(getUser(tenantId, arg.getWriteOffOwner()), EventType.MODIFY, ActionType.Modify, describe, objectData, diff, dbData);
            }
        })).run();

    }

    private void validateDataListByYears(List<IObjectData> objectDataList) {
        List<IObjectData> currentObject = objectDataList.stream().filter(v -> validateYears(v.get(TPMStoreWriteOffFields.YEARS, Long.class))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentObject)){
            String message;
            if (currentObject.size() == 1){
                message = "所选的数据为当月的数据，不允许核销操作。";
            }else{
                message = String.format(I18N.text(I18NKeys.FORMAT_STORE_WRITE_OFF_SERVICE_0), currentObject.size());
            }
            throw new ValidateException(message);
        }
    }

    private static void buildStoreWriteOff(StoreWriteOffBatchData.Arg arg, String tenantId, Map<String, Object> object, JSONObject data) {
        if (TPMGrayUtils.isYuanQi(tenantId)
                && TPMGrayUtils.isYuanQiStoreWriteOffReview(tenantId)) {
            JSONObject updateData = arg.getData();
            // 前端传的判断，是否复核的字段 is_review
            Boolean isReview = updateData.getBoolean("is_store_write_off_review");
            if (Boolean.TRUE.equals(isReview)) {
                object.put("review_owner__c", arg.getWriteOffOwner());
                object.put("review_status__c", TPMStoreWriteOffFields.ReviewStatus.REVIEWED);
                object.put("review_time__c", System.currentTimeMillis());
                return;
            }
        }

        //状态更新为 人工核销
        object.put("write_off_status", TPMStoreWriteOffFields.WriteOffStatus.MANUAL_WRITE_OFF);
        // 核销人员
        object.put("write_off_owner", arg.getWriteOffOwner());
        if (data.containsKey("store_standard")) {
            //核销结果
            object.put("write_off_value", data.getBoolean("store_standard") ? TPMStoreWriteOffFields.WriteOffValue.AGREE : TPMStoreWriteOffFields.WriteOffValue.REJECT);
        }
        if (TPMGrayUtils.isYuanQi(tenantId)) {
            object.put("field_2B1Il__c", "c632DT05L");
        }
    }

    private User getUser(String tenantId, List<String> writeOffOwner) {
        return new User(tenantId, writeOffOwner.get(0));
    }

    @Override
    public StoreWriteOffDataList.Result dataList(StoreWriteOffDataList.Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.STORE_WRITE_OFF_SERVICE_0));
    }

    @Override
    public StoreWriteOffCashingProductData.Result saveOrUpdateCashingProduct(StoreWriteOffCashingProductData.Arg arg) {
        throw new ValidateException(I18N.text(I18NKeys.STORE_WRITE_OFF_SERVICE_1));
    }

    @Override
    public ValidationStoreWriteOffData.Result validationStoreWriteOff(ValidationStoreWriteOffData.Arg arg) {

        if (CollectionUtils.isEmpty(arg.getIds())) {
            return ValidationStoreWriteOffData.Result.builder().validationResult(false).build();
        }
        String tenantId;
        if (Strings.isNotEmpty(arg.getTenantId())){
            tenantId = arg.getTenantId();
        }else {
            ApiContext context = ApiContextManager.getContext();
            tenantId = context.getTenantId();
        }

        List<IObjectData> storeWriteOffs = serviceFacade.findObjectDataByIds(tenantId, arg.getIds(), ApiNames.TPM_STORE_WRITE_OFF_OBJ);
        List<String> activityIds = storeWriteOffs.stream().map(iObjectData -> iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ID, String.class)).distinct().collect(Collectors.toList());
        Map<String, String> storeIdActivityIdMap = storeWriteOffs.stream().collect(Collectors.toMap(IObjectData::getId, iObjectData -> iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ID, String.class)));

        List<IObjectData> lockObject = storeWriteOffs.stream().filter(v -> CommonFields.LOCK_STATUS__LOCK.equals(v.get(CommonFields.LOCK_STATUS))).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(lockObject)) {
            String message = String.format(I18N.text(I18NKeys.FORMAT_STORE_WRITE_OFF_SERVICE_1), lockObject.size());
            return ValidationStoreWriteOffData.Result.builder().validationResult(false).validationMessage(message).build();
        }
        // 单条的不校验 兑付是否货补。
        if (arg.getIds().size() > 1){
            List<IObjectData> activities = serviceFacade.findObjectDataByIds(tenantId, activityIds, ApiNames.TPM_ACTIVITY_OBJ);
            Map<String, String> activityIdMap = activities.stream().collect(Collectors.toMap(IObjectData::getId, iObjectData ->
            {
                String cashingType = iObjectData.get(TPMActivityFields.DEALER_CASHING_TYPE, String.class);
                return cashingType == null ? TPMActivityCashingProductFields.CASH : cashingType;
            }));

            List<String> associationIds = storeWriteOffs.stream()
                    .filter(iObjectData -> Strings.isNotEmpty(iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class)))
                    .map(iObjectData -> iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class)).distinct().collect(Collectors.toList());

            Map<String, String> storeIdAssociationIdMap = Maps.newHashMap();
            Map<String, String> agreementIdMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(associationIds)) {
                storeIdAssociationIdMap = storeWriteOffs.stream().collect(Collectors.toMap(IObjectData::getId, iObjectData -> iObjectData.get(TPMStoreWriteOffFields.ACTIVITY_ASSOCIATION, String.class)));

                List<IObjectData> agreements = serviceFacade.findObjectDataByIds(tenantId, associationIds, ApiNames.TPM_ACTIVITY_AGREEMENT_OBJ);
                agreementIdMap = agreements.stream().collect(Collectors.toMap(IObjectData::getId, iObjectData ->
                {
                    String cashingType = iObjectData.get(TPMActivityAgreementFields.AGREEMENT_CASHING_TYPE, String.class);
                    return cashingType == null ? TPMActivityCashingProductFields.CASH : cashingType;
                }));
            }

            List<String> activityTypeIds = storeWriteOffs.stream().map(activity -> activity.get(TPMStoreWriteOffFields.ACTIVITY_TYPE, String.class)).distinct().collect(Collectors.toList());
            List<ActivityTypeExt> activityTypes = activityTypeManager.findByActivityTypeIds(tenantId, activityTypeIds);
            if (CollectionUtils.isEmpty(activityTypes)) {
                return ValidationStoreWriteOffData.Result.builder().validationResult(false).build();
            }
            Map<String, Boolean> typeIdMap = activityTypes.stream().collect(Collectors.toMap(type -> type.get().getId().toString(), type -> Objects.nonNull(type.agreementNode())));

            for (IObjectData storeWriteOff : storeWriteOffs) {
                if (Boolean.TRUE.equals(typeIdMap.get(storeWriteOff.get(TPMStoreWriteOffFields.ACTIVITY_TYPE, String.class)))) {
                    String agreementId = storeIdAssociationIdMap.get(storeWriteOff.getId());
                    if (TPMActivityCashingProductFields.GOODS.equals(agreementIdMap.get(agreementId))) {
                        return ValidationStoreWriteOffData.Result.builder().validationResult(false).build();
                    }
                } else {
                    String activityId = storeIdActivityIdMap.get(storeWriteOff.getId());
                    if (TPMActivityCashingProductFields.GOODS.equals(activityIdMap.get(activityId))) {
                        return ValidationStoreWriteOffData.Result.builder().validationResult(false).build();
                    }
                }
            }
        }

        return ValidationStoreWriteOffData.Result.builder().validationResult(true).build();
    }

    private boolean validateYears(Long years) {
        boolean flag = false;
        try {
            flag = TimeUtils.covertToCurrentMonth(System.currentTimeMillis()) <= years;
        } catch (Exception exception) {
            log.info("validate years error", exception);
        }
        return flag;
    }

}
