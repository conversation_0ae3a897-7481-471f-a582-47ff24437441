package com.facishare.crm.fmcg.tpm.web.contract;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * author: wuyx
 * description:
 * createTime: 2022/3/17 10:28
 */
public interface ActivitySandBox {

    @Data
    @ToString
    class Arg implements Serializable {

        private String sourceEA;

        private String targetEA;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {
        private int code;
        private String errorMessage;
    }
}