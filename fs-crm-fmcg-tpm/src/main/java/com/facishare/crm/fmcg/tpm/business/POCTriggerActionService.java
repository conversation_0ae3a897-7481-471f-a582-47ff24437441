package com.facishare.crm.fmcg.tpm.business;

import com.facishare.crm.fmcg.tpm.business.abstraction.IPOCTriggerActionService;
import com.facishare.crm.fmcg.tpm.web.contract.TriggerAction;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.BaseService;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class POCTriggerActionService extends BaseService implements IPOCTriggerActionService {

    @Override
    public BaseObjectSaveAction.Result triggerAction(TriggerAction.Arg arg) {

        ActionContext addActionContext = new ActionContext(
                RequestContext.builder().tenantId(arg.getUser().getTenantId()).user(arg.getUser()).build(),
                arg.getApiName(),
                arg.getActionName()
        );

        addActionContext.setAttribute("triggerWorkflow", arg.isTriggerWorkflow());
        addActionContext.setAttribute("triggerFlow", arg.isTriggerFlow());
        addActionContext.setAttribute("fromPOC", "true");

        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(arg.getObjectData()));

        if (CollectionUtils.isNotEmpty(arg.getDetails())) {
            Map<String, List<ObjectDataDocument>> detailsMap = Maps.newHashMap();
            detailsMap.put(arg.getDetailApiName(), arg.getDetails().stream().map(ObjectDataDocument::of).collect(Collectors.toList()));
            saveArg.setDetails(detailsMap);
        }

        return serviceFacade.triggerAction(addActionContext, saveArg, BaseObjectSaveAction.Result.class);
    }
}
