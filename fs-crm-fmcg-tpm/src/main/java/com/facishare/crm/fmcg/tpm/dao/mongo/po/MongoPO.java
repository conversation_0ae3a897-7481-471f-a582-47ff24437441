package com.facishare.crm.fmcg.tpm.dao.mongo.po;

import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.ToString;
import org.bson.types.ObjectId;
import org.mongodb.morphia.annotations.Id;
import org.mongodb.morphia.annotations.Property;
import org.mongodb.morphia.annotations.Transient;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/15 15:50
 */
@Data
@ToString
public abstract class MongoPO implements Serializable {

    public static final String F_ID = "_id";
    public static final String F_TENANT_ID = "tenant_id";
    public static final String F_UNIQUE_ID = "unique_id";

    public static final String F_CREATOR = "creator";
    public static final String F_CREATE_TIME = "create_time";

    public static final String F_LAST_UPDATER = "last_updater";
    public static final String F_LAST_UPDATE_TIME = "last_update_time";

    public static final String F_IS_DELETED = "is_deleted";
    public static final String F_DELETE_TIME = "delete_time";
    public static final String F_DELETE_BY = "delete_by";

    @Id
    private ObjectId id;

    @Property(F_TENANT_ID)
    private String tenantId;

    @Property(F_UNIQUE_ID)
    private String uniqueId;

    @Property(F_CREATOR)
    private Integer creator;

    @Property(F_CREATE_TIME)
    private Long createTime;

    @Property(F_LAST_UPDATER)
    private Integer lastUpdater;

    @Property(F_LAST_UPDATE_TIME)
    private Long lastUpdateTime;

    @Property(F_IS_DELETED)
    private boolean isDeleted;

    @Property(F_DELETE_BY)
    private Integer deleteBy;

    @Property(F_DELETE_TIME)
    private Long deleteTime;

    @Transient
    private Boolean useId;

    public ObjectId getId() {
        if (Boolean.TRUE.equals(useId) || Strings.isNullOrEmpty(uniqueId)) {
            return id;
        } else {
            return new ObjectId(uniqueId);
        }
    }

    public ObjectId getOriginalId() {
        return id;
    }
}
