package com.facishare.crm.fmcg.tpm.api.rule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardDetailEntity;
import com.facishare.crm.fmcg.tpm.dao.mongo.po.RewardNodeEntity;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * Author: linmj
 * Date: 2023/9/15 17:43
 */
@Data
@ToString
public class RewardNodeDTO implements Serializable {

    @JsonProperty(value = RewardNodeEntity.F_REWARD_DIMENSION)
    @SerializedName(RewardNodeEntity.F_REWARD_DIMENSION)
    @JSONField(name = RewardNodeEntity.F_REWARD_DIMENSION)
    private String rewardDimension;

    @JsonProperty(value = RewardNodeEntity.F_REWARD_TYPE)
    @SerializedName(RewardNodeEntity.F_REWARD_TYPE)
    @JSONField(name = RewardNodeEntity.F_REWARD_TYPE)
    private String rewardType;

    @JsonProperty(value = RewardNodeEntity.F_REWARD_ACTION)
    @SerializedName(RewardNodeEntity.F_REWARD_ACTION)
    @JSONField(name = RewardNodeEntity.F_REWARD_ACTION)
    private String rewardAction;

    @JsonProperty(value = RewardNodeEntity.F_REWARD_TARGET)
    @SerializedName(RewardNodeEntity.F_REWARD_TARGET)
    @JSONField(name = RewardNodeEntity.F_REWARD_TARGET)
    private String rewardTarget;

    @JsonProperty(value = RewardNodeEntity.F_REQUIRED)
    @SerializedName(RewardNodeEntity.F_REQUIRED)
    @JSONField(name = RewardNodeEntity.F_REQUIRED)
    private Boolean required;

    @JsonProperty(value = RewardNodeEntity.F_REWARD_ROLE)
    @SerializedName(RewardNodeEntity.F_REWARD_ROLE)
    @JSONField(name = RewardNodeEntity.F_REWARD_ROLE)
    private List<String> rewardRole;

    @JsonProperty(value = RewardNodeEntity.F_REWARD_TAG)
    @SerializedName(RewardNodeEntity.F_REWARD_TAG)
    @JSONField(name = RewardNodeEntity.F_REWARD_TAG)
    private String rewardTag;

    public RewardNodeEntity toEntity() {
        RewardNodeEntity rewardNodeEntity = new RewardNodeEntity();
        rewardNodeEntity.setRewardDimension(this.rewardDimension);
        rewardNodeEntity.setRewardType(this.rewardType);
        rewardNodeEntity.setRewardAction(this.rewardAction);
        rewardNodeEntity.setRewardTarget(this.rewardTarget);
        rewardNodeEntity.setRequired(this.required);
        rewardNodeEntity.setRewardRole(this.rewardRole);
        rewardNodeEntity.setRewardTag(this.rewardTag);
        return rewardNodeEntity;
    }

    public static RewardNodeDTO fromEntity(RewardNodeEntity rewardNodeEntity) {
        RewardNodeDTO rewardNodeDTO = new RewardNodeDTO();
        rewardNodeDTO.setRewardDimension(rewardNodeEntity.getRewardDimension());
        rewardNodeDTO.setRewardType(rewardNodeEntity.getRewardType());
        rewardNodeDTO.setRewardAction(rewardNodeEntity.getRewardAction());
        rewardNodeDTO.setRewardTarget(rewardNodeEntity.getRewardTarget());
        rewardNodeDTO.setRequired(rewardNodeEntity.getRequired());
        rewardNodeDTO.setRewardRole(rewardNodeEntity.getRewardRole());
        rewardNodeDTO.setRewardTag(rewardNodeEntity.getRewardTag());
        return rewardNodeDTO;
    }
}
