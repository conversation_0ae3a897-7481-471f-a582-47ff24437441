package com.facishare.crm.fmcg.tpm.web.service.abstraction;

import com.facishare.crm.fmcg.tpm.web.contract.ActivityUnifiedCaseData;
import com.facishare.crm.fmcg.tpm.web.contract.ActivityUnifiedCaseFlow;

/**
 * <AUTHOR>
 * @date 2023/2/14 16:10
 */
public interface IActivityUnifiedCaseService {


    ActivityUnifiedCaseFlow.Result queryFlowWithType(ActivityUnifiedCaseFlow.Arg arg);

    ActivityUnifiedCaseData.Result queryData(ActivityUnifiedCaseData.Arg arg);
}
