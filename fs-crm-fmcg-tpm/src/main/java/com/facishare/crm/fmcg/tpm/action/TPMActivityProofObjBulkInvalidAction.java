package com.facishare.crm.fmcg.tpm.action;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofAuditFields;
import com.facishare.crm.fmcg.common.apiname.TPMActivityProofFields;
import com.facishare.crm.fmcg.tpm.utils.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/8/4 下午3:35
 */
public class TPMActivityProofObjBulkInvalidAction extends StandardBulkInvalidAction {

    @Override
    protected void before(Arg arg) {

        List<IObjectData> proofs = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), arg.getDataIds(), ApiNames.TPM_ACTIVITY_PROOF_OBJ);
        List<String> names1 = Lists.newArrayList();
        List<String> names2 = Lists.newArrayList();
        List<String> proofIds = Lists.newArrayList();
        proofs.forEach(proof -> {
            String status = proof.get(TPMActivityProofFields.AUDIT_STATUS, String.class);
            if (!"schedule".equals(status)) {
                names1.add(proof.getName());
            }
            if (!Strings.isNullOrEmpty(proof.get(TPMActivityProofFields.DEALER_ACTIVITY_COST_ID, String.class))) {
                names2.add(proof.getName());
            }
            proofIds.add(proof.getId());
        });
        if (!CollectionUtils.isEmpty(names1)) {
            throw new ValidateException(I18N.text(I18NKeys.BATCH_AUDITED_PROOF_CAN_NOT_INVALID_NAMES) + names1);
        }
        if (!CollectionUtils.isEmpty(names2)) {
            throw new ValidateException(I18N.text(I18NKeys.BATCH_PROOF_RELATED_COST_CAN_NOT_INVALID_NAMES) + names2);
        }

        SearchTemplateQuery query = new SearchTemplateQuery();

        query.setOffset(0);
        query.setLimit(proofIds.size());
        query.setSearchSource("db");

        IFilter proofFilter = new Filter();
        proofFilter.setFieldName(TPMActivityProofAuditFields.ACTIVITY_PROOF_ID);
        proofFilter.setFieldValues(proofIds);
        proofFilter.setOperator(Operator.IN);

        query.setFilters(Lists.newArrayList(proofFilter));

        List<IObjectData> audits = serviceFacade.findBySearchQuery(User.systemUser(actionContext.getTenantId()), ApiNames.TPM_ACTIVITY_PROOF_AUDIT_OBJ, query).getData();

        if (!CollectionUtils.isEmpty(audits)) {
            throw new ValidateException(I18N.text(I18NKeys.ACTIVITY_PROOF_OBJ_BULK_INVALID_ACTION_0) + audits.stream().map(IObjectData::getName).collect(Collectors.toList()));
        }
        super.before(arg);
    }
}
