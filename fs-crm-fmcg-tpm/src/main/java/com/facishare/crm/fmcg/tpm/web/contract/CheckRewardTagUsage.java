package com.facishare.crm.fmcg.tpm.web.contract;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description: 检查rewardTag是否被ActivityRewardRulePO使用
 * createTime: 2024/12/19
 *
 * <AUTHOR>
 */
public interface CheckRewardTagUsage {

    @Data
    @ToString
    class Arg implements Serializable {
        
        @JSONField(name = "reward_tag")
        @JsonProperty(value = "reward_tag")
        @SerializedName("reward_tag")
        private String rewardTag;
    }

    @Data
    @ToString
    @Builder
    class Result implements Serializable {

        @JSONField(name = "is_used")
        @JsonProperty(value = "is_used")
        @SerializedName("is_used")
        private boolean isUsed;
    }
}
