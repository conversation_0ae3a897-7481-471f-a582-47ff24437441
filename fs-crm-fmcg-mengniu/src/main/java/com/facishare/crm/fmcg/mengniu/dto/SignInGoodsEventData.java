package com.facishare.crm.fmcg.mengniu.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
public class SignInGoodsEventData implements Serializable {

    @JSONField(name = "delivery_note_id")
    @JsonProperty(value = "delivery_note_id")
    @SerializedName("delivery_note_id")
    private String deliveryNoteId;
}