package com.facishare.crm.fmcg.mengniu.handler;

import com.facishare.crm.fmcg.common.adapter.abstraction.IPayService;
import com.facishare.crm.fmcg.common.adapter.dto.UserInfo;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventRetryException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.ObjectActionRetryException;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.CloudTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.cloud.WXCloudPayReceiverAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.BatchWXTenantTransfer;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXPersonalAccount;
import com.facishare.crm.fmcg.common.adapter.dto.pay.wx.WXTenantAccount;
import com.facishare.crm.fmcg.common.adapter.exception.RewardFmcgException;
import com.facishare.crm.fmcg.common.apiname.CommonFields;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordDetailFields;
import com.facishare.crm.fmcg.common.apiname.RedPacketRecordFields;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.IdentityIdGenerator;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.mengniu.business.RewardAmountConfigService;
import com.facishare.crm.fmcg.mengniu.dto.*;
import com.facishare.crm.fmcg.tpm.retry.setter.RewardRecordSetter;
import com.facishare.crm.fmcg.tpm.web.service.TenantHierarchyService;
import com.facishare.organization.api.service.DepartmentProviderService;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@SuppressWarnings("Duplicates")
public abstract class SalesEventHandler<T extends Serializable> {

    protected static final long LOCK_WAIT = 4;
    protected static final long LOCK_LEASE = 8;

    @Resource
    protected RedissonClient redissonCmd;
    @Resource
    protected IPayService payService;
    @Resource
    protected ServiceFacade serviceFacade;
    @Resource
    protected RewardRecordSetter rewardRecordSetter;
    @Resource
    protected EnterpriseEditionService enterpriseEditionService;
    @Resource
    protected TenantHierarchyService tenantHierarchyService;
    @Resource
    protected DepartmentProviderService departmentProviderService;
    @Resource
    protected RewardAmountConfigService rewardAmountConfigService;
    @Resource
    protected NewRewardService<T> newRewardService;

    protected abstract String buildEventIdentityKey(SalesEvent<T> event);

    protected abstract List<RedPacketReward> calculateRewards(SalesEvent<T> event);

    protected abstract void validateEventData(T data);

    private void validateEvent(SalesEvent<T> event) {
        if (Strings.isNullOrEmpty(event.getEventId())) {
            throw new EventAbandonException("event id empty.");
        }
        if (Strings.isNullOrEmpty(event.getEventType())) {
            throw new EventAbandonException("event type empty.");
        }
        if (Strings.isNullOrEmpty(event.getTenantId())) {
            throw new EventAbandonException("tenant id empty.");
        }
        if (Objects.isNull(event.getData())) {
            throw new EventAbandonException("event data empty.");
        }
        this.validateEventData(event.getData());
    }

    public SalesEventInvokeResult invoke(SalesEvent<T> event) {
        StopWatch watch = StopWatch.create("HANDLE_EVENT." + event.getEventId());
        log.info("sales event received : {}", event);

        this.validateEvent(event);
        watch.lap("validateEvent");

        if (!this.tryLock(event)) {
            if ("USER_OBJECT_ACTION_REWARDS".equals(event.getEventType())) {
                throw new ObjectActionRetryException("try lock event failed");
            } else {
                throw new EventRetryException("try lock event failed");
            }
        }
        watch.lap("tryLock");

        try {
            List<RedPacketReward> rewards = this.calculateRewards(event);
            watch.lap("calculateRewards");

            log.info("rewards : " + rewards);
            event.setBusinessId(IdentityIdGenerator.formPaymentIdentityId());
            List<RedPacketRecord> records = Lists.newArrayList();
            for (RedPacketReward reward : rewards) {
                if (Objects.isNull(reward) || reward.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                if (TPMGrayUtils.allowMengNiuRedPacketPublishV2(reward.getTenantId(), reward.getActivityRecordType())) {
                    records.add(newRewardService.publish(event, reward));
                } else {
                    records.add(this.publish(event, reward));
                }
                watch.lap("publish." + reward.getIdentity());
            }

            watch.logSlow(500);
            return SalesEventInvokeResult.builder().records(records).build();
        } finally {
            this.unlock(event);
        }
    }

    private RedPacketRecord publish(SalesEvent<T> event, RedPacketReward reward) {
        RedPacketRecord data = findByIdentity(reward.getTenantId(), reward);
        if (Objects.nonNull(data)) {
            return data;
        }

        Map<String, List<IObjectData>> details = Maps.newHashMap();
        details.put(RedPacketRecordDetailFields.API_NAME, convertToDetailData(reward, reward.getDetails()));

        IObjectData master = convertToMasterData(event, reward);

        SaveMasterAndDetailData.Arg arg = SaveMasterAndDetailData.Arg.builder()
                .objectDescribes(loadDescribeMap(reward))
                .masterObjectData(master)
                .detailObjectData(details)
                .build();

        SaveMasterAndDetailData.Result result = serviceFacade.saveMasterAndDetailData(User.systemUser(master.getTenantId()), arg);

        if (!reward.isOverLimit()) {
            transfer(reward.getTenantId(), reward, result.getMasterObjectData());
        }

        return RedPacketRecord.builder().record(master).build();
    }

    private Map<String, IObjectDescribe> loadDescribeMap(RedPacketReward reward) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(RedPacketRecordDetailFields.API_NAME, serviceFacade.findObject(reward.getTenantId(), RedPacketRecordDetailFields.API_NAME));
        describeMap.put(RedPacketRecordFields.API_NAME, serviceFacade.findObject(reward.getTenantId(), RedPacketRecordFields.API_NAME));
        return describeMap;
    }

    private List<IObjectData> convertToDetailData(RedPacketReward reward, List<RedPacketRewardDetail> details) {
        List<IObjectData> data = Lists.newArrayList();
        for (RedPacketRewardDetail detail : details) {
            IObjectData datum = new ObjectData();
            datum.setTenantId(reward.getTenantId());
            datum.setDescribeApiName(RedPacketRecordDetailFields.API_NAME);
            datum.setOwner(Lists.newArrayList(RewardConstants.SYSTEM_USER));
            datum.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);
            datum.set(RedPacketRecordDetailFields.SALES_ORDER_DETAIL_ID, detail.getSalesOrderDetailId());
            datum.set(RedPacketRecordDetailFields.SALES_ORDER_DETAIL_NAME, detail.getSalesOrderDetailName());
            datum.set(RedPacketRecordDetailFields.PRODUCT_ID, detail.getProductId());
            datum.set(RedPacketRecordDetailFields.SERIAL_NUMBER_ID, detail.getSerialNumberId());
            datum.set(RedPacketRecordDetailFields.SERIAL_NUMBER_NAME, detail.getSerialNumberName());
            datum.set(RedPacketRecordDetailFields.MANUFACTURE_DATE, detail.getManufactureDate());
            datum.set(RedPacketRecordDetailFields.BATCH_CODE, detail.getBatchCode());
            datum.set(RedPacketRecordDetailFields.AMOUNT, detail.getAmount());
            datum.set(RedPacketRecordDetailFields.AMOUNT_CONFIG_ID, detail.getAmountConfigId());
            data.add(datum);
        }
        return data;
    }

    private static CloudAccount convertToWxCloudPayAccount(PaymentAccount account) {
        CloudAccount from = new CloudAccount();
        from.setTenantAccount(account.getTenantCloudPaymentAccount().getTenantAccount());
        from.setCloudAccountDealerId(account.getTenantCloudPaymentAccount().getCloudAccountDealerId());
        from.setCloudAccountBrokerId(account.getTenantCloudPaymentAccount().getCloudAccountBrokerId());
        return from;
    }

    private static WXCloudPayReceiverAccount convertToWxCloudPayReceiverAccount(PaymentAccount account) {
        WXCloudPayReceiverAccount to = new WXCloudPayReceiverAccount();
        if (!Objects.isNull(account.getWeChatPaymentAccount())) {
            to.setOpenId(account.getWeChatPaymentAccount().getOpenId());
            to.setRealName(account.getWeChatPaymentAccount().getRealName());
            to.setIdCardNumber(account.getWeChatPaymentAccount().getIdCardNumber());
            to.setPhoneNumber(account.getWeChatPaymentAccount().getPhoneNumber());
            to.setAppId(account.getWeChatPaymentAccount().getAppId());
        }
        return to;
    }

    private IObjectData convertToMasterData(SalesEvent<T> event, RedPacketReward reward) {
        IObjectData data = new ObjectData();
        data.setTenantId(reward.getTenantId());
        data.setDescribeApiName(RedPacketRecordFields.API_NAME);
        data.setOwner(Lists.newArrayList(RewardConstants.SYSTEM_USER));
        data.setRecordType(CommonFields.RECORD_TYPE__DEFAULT);

        data.set(RedPacketRecordFields.EVENT_TYPE, RewardConstants.EVENT_TYPE_VALUE_MAP.get(event.getEventType()));
        data.set(RedPacketRecordFields.EVENT_TIME, event.getEventTime());
        data.set(RedPacketRecordFields.ACTIVITY_ID, reward.getActivityId());
        data.set(RedPacketRecordFields.ROLE, RewardConstants.ROLE_VALUE_MAP.get(reward.getRole()));
        data.set(RedPacketRecordFields.AMOUNT, reward.getAmount());

        data.set(RedPacketRecordFields.EVENT_OBJECT_API_NAME, reward.getEventObjectApiName());
        data.set(RedPacketRecordFields.EVENT_OBJECT_NAME, reward.getEventObjectName());
        data.set(RedPacketRecordFields.EVENT_OBJECT_TENANT_ID, reward.getEventObjectTenantId());
        data.set(RedPacketRecordFields.EVENT_OBJECT_TENANT_NAME, reward.getEventObjectTenantName());
        data.set(RedPacketRecordFields.EVENT_OBJECT_DATA_ID, reward.getEventObjectDataId());
        data.set(RedPacketRecordFields.RELATED_STORE_NAME, reward.getRelatedStoreName());
        data.set(RedPacketRecordFields.RELATED_STORE_ID, reward.getRelatedStoreId());
        data.set(RedPacketRecordFields.RELATED_STORE, reward.getRelatedStoreId());

        data.set(RedPacketRecordFields.FROM_ACCOUNT_TYPE, RewardConstants.ACCOUNT_TYPE_VALUE_MAP.get(reward.getFrom().getAccountType()));
        switch (reward.getFrom().getAccountType()) {
            case "WeChat":
                data.set(RedPacketRecordFields.FROM_REAL_NAME, reward.getFrom().getWeChatPaymentAccount().getRealName());
                data.set(RedPacketRecordFields.FROM_ID_CARD_NUMBER, reward.getFrom().getWeChatPaymentAccount().getIdCardNumber());
                data.set(RedPacketRecordFields.FROM_WECHAT_OPEN_ID, reward.getFrom().getWeChatPaymentAccount().getOpenId());
                data.set(RedPacketRecordFields.FROM_PHONE_NUMBER, reward.getFrom().getWeChatPaymentAccount().getPhoneNumber());
                break;
            case "TenantCloud":
                data.set(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, reward.getFrom().getTenantCloudPaymentAccount().getTenantAccount());
                data.set(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_DEALER_ID, reward.getFrom().getTenantCloudPaymentAccount().getCloudAccountDealerId());
                data.set(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_BROKER_ID, reward.getFrom().getTenantCloudPaymentAccount().getCloudAccountBrokerId());
                data.set(RedPacketRecordFields.FROM_CLOUD_ACCOUNT_NAME, reward.getFrom().getTenantCloudPaymentAccount().getTenantName());
                break;
            case "TenantWeChat":
                data.set(RedPacketRecordFields.FROM_CLOUD_ACCOUNT, reward.getFrom().getTenantWeChatPaymentAccount().getTenantAccount());
                break;
            default:
                throw new EventAbandonException("from account type not supported.");
        }

        data.set(RedPacketRecordFields.TO_ACCOUNT_TYPE, RewardConstants.ACCOUNT_TYPE_VALUE_MAP.get(reward.getTo().getAccountType()));
        if (reward.getTo().getAccountType().equals("WeChat")) {
            if (!Objects.isNull(reward.getTo().getWeChatPaymentAccount())) {
                data.set(RedPacketRecordFields.TO_REAL_NAME, reward.getTo().getWeChatPaymentAccount().getRealName());
                data.set(RedPacketRecordFields.TO_ID_CARD_NUMBER, reward.getTo().getWeChatPaymentAccount().getIdCardNumber());
                data.set(RedPacketRecordFields.TO_WECHAT_OPEN_ID, reward.getTo().getWeChatPaymentAccount().getOpenId());
                data.set(RedPacketRecordFields.TO_PHONE_NUMBER, reward.getTo().getWeChatPaymentAccount().getPhoneNumber());
                data.set(RedPacketRecordFields.TO_WX_APP_ID, reward.getTo().getWeChatPaymentAccount().getAppId());
                data.set(RedPacketRecordFields.TO_WX_UNION_ID, reward.getTo().getWeChatPaymentAccount().getUnionId());
            }
        } else {
            throw new EventAbandonException("to account type not supported.");
        }

        data.set(RedPacketRecordFields.RECORD_IDENTITY, reward.getIdentity());
        data.set(RedPacketRecordFields.RED_PACKET_LEVEL, reward.getCustomerRewardLevel());
        data.set(RedPacketRecordFields.PAYMENT_BUSINESS_ID, reward.getPaymentIdentity());
        data.set(RedPacketRecordFields.IS_OVER_LIMIT, reward.isOverLimit());

        if (reward.isOverLimit()) {
            data.set(RedPacketRecordFields.PAYMENT_STATUS, "4");
            data.set(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, "活动余额不足。");//ignorei18n
        } else {
            data.set(RedPacketRecordFields.PAYMENT_STATUS, "5");
        }
        data.set(RedPacketRecordFields.REMARKS, reward.getRemarks());
        return data;
    }

    private void transfer(String tenantId, RedPacketReward reward, IObjectData data) {
        switch (reward.getFrom().getAccountType()) {
            case "TenantCloud":
                tenantCloudAccountToWeChatAccountTransfer(tenantId, reward, data);
                break;
            case "TenantWeChat":
                tenantWeChatAccountToWeChatAccountTransfer(tenantId, reward, data);
                break;
            default:
                throw new EventAbandonException("from account type not supported.");
        }
        rewardRecordSetter.setUpdateStatusTask(tenantId, RedPacketRecordFields.API_NAME, data.getId());
    }

    private void tenantWeChatAccountToWeChatAccountTransfer(String tenantId, RedPacketReward reward, IObjectData data) {
        UserInfo user = UserInfo.builder().build();
        user.setUserId(RewardConstants.SYSTEM_USER);
        user.setTenantId(tenantId);

        BatchWXTenantTransfer.Arg arg = new BatchWXTenantTransfer.Arg();
        arg.setBatchTransferId(reward.getPaymentIdentity());
        arg.setBatchName(reward.getPaymentIdentity());

        WXTenantAccount from = new WXTenantAccount();
        from.setTenantAccount(reward.getFrom().getTenantWeChatPaymentAccount().getTenantAccount());
        arg.setPayeeWXAccount(from);

        WXPersonalAccount to = new WXPersonalAccount();
        to.setBusinessId(reward.getPaymentIdentity());
        to.setAmount(reward.getAmount());
        String remarks = fixRemarks(reward.getRemarks());
        to.setRemarks(remarks);
        if (!Objects.isNull(reward.getTo())) {
            to.setOpenId(reward.getTo().getWeChatPaymentAccount().getOpenId());
            to.setAppId(reward.getTo().getWeChatPaymentAccount().getAppId());
        }
        arg.setReceiverAccounts(Lists.newArrayList(to));
        arg.setBatchRemarks(remarks);

        log.info("cloud transfer start - user : {}, arg : {}", user, arg);

        try {
            BatchWXTenantTransfer.Result result = payService.batchWXTenantTransfer(user, arg);

            log.info("cloud transfer result : {}", result);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_ORDER_ID, result.getBatchTransferId());
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "1");
            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);

        } catch (RewardFmcgException ex) {
            log.error("cloud transfer error : ", ex);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "0");
            updater.put(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, ex.getMessage());

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        }
    }

    private String fixRemarks(String remarks) {
        if (Strings.isNullOrEmpty(remarks)) {
            return "--";
        }
        return remarks.length() <= 16 ? remarks : remarks.substring(0, 16);
    }

    private void tenantCloudAccountToWeChatAccountTransfer(String tenantId, RedPacketReward reward, IObjectData data) {
        UserInfo user = UserInfo.builder().build();
        user.setUserId(RewardConstants.SYSTEM_USER);
        user.setTenantId(tenantId);

        CloudTransfer.Arg arg = new CloudTransfer.Arg();
        arg.setBusinessId(reward.getPaymentIdentity());

        arg.setPayerCloudAccount(convertToWxCloudPayAccount(reward.getFrom()));
        arg.setReceiverPayAccount(convertToWxCloudPayReceiverAccount(reward.getTo()));

        arg.setAmount(reward.getAmount());
        arg.setRemarks(fixRemarks(reward.getRemarks()));

        log.info("cloud transfer start - user : {}, arg : {}", user, arg);

        try {
            CloudTransfer.Result result = payService.cloudTransfer(user, arg);

            log.info("cloud transfer result : {}", result);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_ORDER_ID, result.getTransferId());
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "1");

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        } catch (RewardFmcgException ex) {
            log.error("cloud transfer error : ", ex);

            Map<String, Object> updater = Maps.newHashMap();
            updater.put(RedPacketRecordFields.PAYMENT_STATUS, "0");
            updater.put(RedPacketRecordFields.PAYMENT_ERROR_MESSAGE, ex.getMessage());

            serviceFacade.updateWithMap(User.systemUser(tenantId), data, updater);
        }
    }

    private RedPacketRecord findByIdentity(String tenantId, RedPacketReward reward) {
        IFilter identityFilter = new Filter();
        identityFilter.setFieldName(RedPacketRecordFields.RECORD_IDENTITY);
        identityFilter.setOperator(Operator.EQ);
        identityFilter.setFieldValues(Lists.newArrayList(reward.getIdentity()));

        SearchTemplateQuery stq = QueryDataUtil.minimumQuery(identityFilter);
        stq.setLimit(1);

        List<IObjectData> data = QueryDataUtil.find(serviceFacade, tenantId, RedPacketRecordFields.API_NAME, stq,
                Lists.newArrayList("_id")
        );
        if (CollectionUtils.isEmpty(data)) {
            return null;
        } else {
            IObjectData redPacketRecord = data.get(0);
            return RedPacketRecord.builder().record(redPacketRecord).build();
        }
    }

    private void unlock(SalesEvent<T> event) {
        String key = buildEventIdentityKey(event);
        RLock lock = redissonCmd.getLock(key);

        log.info("unlock sales event : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            lock.unlock();
        }
    }

    private boolean tryLock(SalesEvent<T> event) {
        String key = buildEventIdentityKey(event);
        RLock lock = redissonCmd.getLock(key);

        log.info("try lock sales event : {}", key);

        if (lock.isLocked() && lock.isHeldByCurrentThread()) {
            return true;
        }
        try {
            return lock.tryLock(LOCK_WAIT, LOCK_LEASE, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            Thread.currentThread().interrupt();
            throw new EventRetryException(String.format("try lock sales event cause thread interrupted exception : %s", key));
        }
    }
}
