package com.facishare.crm.fmcg.mengniu.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * Author: linmj
 * Date: 2024/1/27 11:23
 */
public interface QueryQrcodeStatus {

    @Data
    @ToString
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg implements Serializable {
        private String content;
    }

    @Data
    @ToString
    class Result implements Serializable {
        private Boolean isUsed;
    }
}
