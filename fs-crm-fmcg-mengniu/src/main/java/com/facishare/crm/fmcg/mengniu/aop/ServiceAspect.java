package com.facishare.crm.fmcg.mengniu.aop;

import com.alibaba.fastjson.JSON;
import com.facishare.cep.plugin.exception.BizException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventAbandonException;
import com.facishare.crm.fmcg.common.adapter.dto.exception.EventRetryException;
import com.facishare.crm.fmcg.common.http.ApiContext;
import com.facishare.crm.fmcg.common.http.ApiContextManager;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/18 14:31
 */
@Aspect
@Component("mnServiceAspect")
@Slf4j
@SuppressWarnings("Duplicates")
public class ServiceAspect {

    @Around(value = "execution(* com.facishare.crm.fmcg.mengniu.service.*.*(..))")
    public Object aroundWeb(ProceedingJoinPoint joinPoint) throws Throwable {
        String method = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();

        try {
            ApiContext context = ApiContextManager.getContext();
            String json = JSON.toJSONString(args);
            log.info("web request context - {}, method - {}, args - {}", context, method, json.length() > 2000 ? json.substring(0, 2000) : json);
        } catch (Exception ex) {
            log.info("log parameters error : ", ex);
        }

        Object result;

        try {
            result = joinPoint.proceed(args);
        } catch (AppBusinessException ex) {
            log.info(String.format("[AppBusinessException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw new BizException(ex.getMessage(), ex.getErrorCode());
        } catch (EventRetryException ex) {
            log.info(String.format("[EventRetryException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw new BizException(ex.getMessage(), 1000300);
        } catch (EventAbandonException ex) {
            log.info(String.format("[EventAbandonException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw new BizException(ex.getMessage(), 1000400);
        } catch (Exception ex) {
            log.info(String.format("[UnknownException] - method - %s : ", joinPoint.getSignature().getName()), ex);
            throw ex;
        }

        try {
            log.info("method - {}, result - {}", method, JSON.toJSONString(result));
        } catch (Exception ex) {
            log.info("convert method arg to json error", ex);
        }

        return result;
    }
}
