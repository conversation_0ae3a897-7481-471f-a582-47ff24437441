package com.facishare.crm.fmcg.tpm;

import com.facishare.crm.fmcg.tpm.service.AppAdminService;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2022/1/10 18:28
 */
public class AppAdminServiceTest extends BaseTest {

    @Resource
    private AppAdminService appAdminService;

    @Test
    public void queryTest() {
        List<Integer> admins = appAdminService.getAppAdminList("80063");
        assert CollectionUtils.isNotEmpty(admins);
    }
}
