package com.facishare.crm.fmcg.roleDepartment;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.constant.ScanCodeActionConstants;
import com.facishare.crm.fmcg.tpm.BaseTest;
import com.facishare.crm.fmcg.tpm.service.abstraction.IRoleService;
import com.facishare.crm.fmcg.tpm.service.abstraction.OrganizationService;
import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.auth.model.AuthContext;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: wuyx
 * @description:
 * @createTime: 2022/1/7 10:27
 */
public class RoleDepartmentTest extends BaseTest {

    @Resource(name = "tpmRoleService")
    private IRoleService roleService;

    @Resource(name = "tpmOrganizationService")
    private OrganizationService organizationService;

    @Resource
    private RoleClient roleClient;

    @Resource
    private ServiceFacade serviceFacade;

    @Test
    public void queryEmployeeRoleTest() {

        Map<Integer, String> integerStringMap = roleService.queryRoleByEmployeeIds(80063, Lists.newArrayList(1003));
        System.out.println(integerStringMap);
    }

    @Test
    public void getDepartmentIdsTest() {

        List<Integer> departmentIds = organizationService.getDepartmentIds(80063, 1003);
        System.out.println(JSON.toJSONString(departmentIds));
    }

    @Test
    public void queryAllDepartmentTest() {

        List<DepartmentDto> departmentDtos = organizationService.queryAllDepartment(84931);
        System.out.println(JSON.toJSONString(departmentDtos));
    }

    @Test
    public void getAllRoleByEmployeeIdsTest() {
        List<String> allRoleByEmployeeIds = roleService.queryRoleByEmployeeId(80063, 1003);
        System.out.println(JSON.toJSONString(allRoleByEmployeeIds));
    }

    @Test
    public void queryRoleCodeListByUserIdTest() {
        List<String> role = roleService.queryRoleCodeListByUserId("84931", 1000);
        System.out.println(JSON.toJSONString(role));
    }

    @Test
    public void getOuterRole(){

        AuthContext authContext = new AuthContext();
        authContext.setTenantId("89386");
        authContext.setAppId("All");

        System.out.println(JSON.toJSONString(roleClient.queryOuterUserRole(authContext,Lists.newArrayList("300396085"), Sets.newHashSet(),Sets.newHashSet("300110347"))));

        System.out.println(JSON.toJSONString(roleClient.queryRole(authContext, null, null)));
    }

    @Test
    public void getCrm(){
        System.out.println(JSON.toJSONString(serviceFacade.getUsersByRole(User.systemUser("84931"), "00000000000000000000000000000006")));
        System.out.println(JSON.toJSONString(serviceFacade.getUsersByRole(User.systemUser("89273"), "00000000000000000000000000000006")));
    }
}
