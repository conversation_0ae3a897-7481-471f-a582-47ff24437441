package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.mengniu.business.abstraction.IMengNiuConfig;
import com.facishare.crm.fmcg.mengniu.dto.CheckEnableAIRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/MengNiu/config", produces = "application/json")
public class MengNiuConfigController {

    @Resource
    private IMengNiuConfig mengNiuConfig;

    @PostMapping(value = "checkEnableAIRule")
    public CheckEnableAIRule.Result checkEnableAIRule(@RequestBody CheckEnableAIRule.Arg arg) {
        return mengNiuConfig.checkEnableAIRule(arg);
    }

}
