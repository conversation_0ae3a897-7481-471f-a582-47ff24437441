package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.dms.model.PaymentCalculateMaxAmount;
import com.facishare.crm.fmcg.dms.web.abstraction.IRedPaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/DMS/Payment", produces = "application/json")
public class RedPaymentController {

    @Resource
    private IRedPaymentService redPaymentService;


    @PostMapping(value = "calculateMaxAmount")
    public PaymentCalculateMaxAmount.Result calculateMaxAmount(@RequestBody PaymentCalculateMaxAmount.Arg arg) {
        return redPaymentService.calculateMaxAmount(arg);
    }

}
