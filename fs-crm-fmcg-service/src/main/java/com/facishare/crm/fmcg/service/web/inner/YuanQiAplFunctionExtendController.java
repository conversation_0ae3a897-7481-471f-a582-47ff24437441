package com.facishare.crm.fmcg.service.web.inner;

import com.facishare.crm.fmcg.service.web.inner.provider.InnerApiResult;
import com.facishare.crm.fmcg.yqsl.AplFunctionExtendService;
import com.facishare.crm.fmcg.yqsl.api.AplFunctionNearbyAccounts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping(value = "/TPM/YuanQi/AplFunctionExtend", produces = "application/json")
public class YuanQiAplFunctionExtendController {

    @Resource
    private AplFunctionExtendService aplFunctionExtendService;

    @PostMapping(value = "NearbyAccounts")
    public InnerApiResult<AplFunctionNearbyAccounts.Result> nearbyAccounts(@RequestBody AplFunctionNearbyAccounts.Arg arg) {
        return InnerApiResult.apply(aplFunctionExtendService::nearbyAccounts, arg);
    }
}
