package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.*;
import com.facishare.crm.fmcg.tpm.web.service.abstraction.IConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description: 通用设置
 * createTime: 2023/3/20 14:26
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/Config", produces = "application/json")
public class ConfigController {

    @Resource
    private IConfigService tpmConfigService;

    @PostMapping(value = "Save")
    public AddConfig.Result save(@RequestBody AddConfig.Arg arg) {
        return tpmConfigService.save(arg);
    }

    @PostMapping(value = "List")
    public ListConfig.Result list(@RequestBody ListConfig.Arg arg) {
        return tpmConfigService.list(arg);
    }

    @PostMapping(value = "getDescribe")
    public GetConfigDescribe.Result getDescribe(@RequestBody GetConfigDescribe.Arg arg) {
        return tpmConfigService.getDescribe(arg);
    }

    @PostMapping(value = "checkRewardTagUsage")
    public CheckRewardTagUsage.Result checkRewardTagUsage(@RequestBody CheckRewardTagUsage.Arg arg) {
        return tpmConfigService.checkRewardTagUsage(arg);
    }
}
