package com.facishare.crm.fmcg.service.web.facade;

import com.facishare.crm.fmcg.tpm.web.contract.ActivityTemplateCopy;
import com.facishare.crm.fmcg.tpm.web.tools.abstraction.IActivityTemplateCopyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * author: wuyx
 * description:
 * createTime: 2022/4/20 15:05
 */
@Slf4j
@RestController
@RequestMapping(value = "/TPM/template", produces = "application/json")
public class TPMTemplateCopyController {

    @Resource
    private IActivityTemplateCopyService activityTemplateCopyService;

    @PostMapping(value = "copy")
    public ActivityTemplateCopy.Result copy(@RequestBody ActivityTemplateCopy.Arg arg) {
        return activityTemplateCopyService.copyTPMOfTemplate(arg);
    }
}