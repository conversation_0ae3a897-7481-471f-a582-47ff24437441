package com.facishare.crm.fmcg.common.gray;

import com.fxiaoke.common.release.GrayRelease;
import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import lombok.experimental.UtilityClass;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/7/31 14:11
 */
@UtilityClass
public class TPMGrayUtils {
    private static final FsGrayReleaseBiz FS_GRAY_RELEASE_BIZ = FsGrayRelease.getInstance("sfa");

    public boolean isYinLu(String tenantId) {
        return GrayRelease.isAllow("fmcg", "YINLU_TPM", tenantId);
    }

    public boolean newActivityEnableCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_NEW_ACTIVITY_ENABLE_CHECK", tenantId);
    }

    public boolean useAgreementDealerIdOnProofAddAction(String tenantId) {
        return GrayRelease.isAllow("fmcg", "YINLU_TPM", tenantId) || GrayRelease.isAllow("fmcg", "USE_AGREEMENT_DEALER_ID_ON_PROOF_ADD_ACTION", tenantId);
    }

    public boolean agreementNotRelatedToActivity(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_AGREEMENT_NOT_RELATED_TO_ACTIVITY", tenantId);
    }

    public boolean proofDataTypeAllUseExistOrNot(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_PROOF_DATA_TYPE_ALL_USE_EXIST_OR_NOT", tenantId);
    }

    public boolean customCostValidate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_CUSTOM_COST_VALIDATE", tenantId);
    }

    public boolean customAllowStoreCountValidate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_CUSTOM_STORE_COUNT_VALIDATE", tenantId);
    }

    public boolean dealerProofEnable(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_DEALER_PROOF_ENABLE", tenantId);
    }

    public boolean denyDepartmentFilterOnActivity(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_DENY_DEPARTMENT_FILTER_ON_ACTIVITY", tenantId);
    }

    public boolean skipBudgetTypeDepartmentCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_SKIP_BUDGET_TYPE_DEPARTMENT_CHECK", tenantId);
    }

    public boolean skipBudgetTransferRangeCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_SKIP_BUDGET_TRANSFER_RANGE_CHECK", tenantId);
    }

    public boolean skipEnableCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_SKIP_ENABLE_CHECK", tenantId);
    }

    public boolean skipEnableCacheCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_SKIP_ENABLE_CACHE_CHECK", tenantId);
    }

    public boolean skiProofCountCheckOnWriteOffAction(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_SKIP_PROOF_COUNT_CHECK_ON_WRITE_OFF_ACTION", tenantId);
    }

    public boolean skipProofAddTransaction(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_PROOF_ADD_TRANSACTION", tenantId);
    }

    public boolean excessDeductionForCost(String tenantId) {
        return GrayRelease.isAllow("fmcg", "EXCESS_DEDUCTION_FOR_COST", tenantId);
    }

    public boolean skipDeletedBudgetWhenCalculating(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_DELETED_BUDGET_WHEN_CALCULATING", tenantId);
    }

    public boolean budgetTransferInSupportNegative(String tenantId) {
        return GrayRelease.isAllow("fmcg", "BUDGET_TRANSFER_IN_SUPPORT_NEGATIVE", tenantId);
    }

    public boolean isSupportOverrideAdjustTime(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SUPPORT_OVERRIDE_ADJUST_TIME", tenantId);
    }

    public boolean isYuanQi(String tenantId) {
        return GrayRelease.isAllow("fmcg", "YUANQI", tenantId);
    }

    public boolean isYuanQiStoreWriteOffReview(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_STORE_WRITE_OFF_REVIEW", tenantId);
    }

    public boolean isFuMao(String tenantId) {
        return GrayRelease.isAllow("fmcg", "FUMAO", tenantId);
    }

    public boolean allowDeleteBudgetData(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_CLEAR_BUDGET_DATA", tenantId);
    }

    public boolean allowDeleteBudgetObjectData(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_CLEAR_BUDGET_OBJECT_DATA", tenantId);
    }

    public boolean isSupportCloseActivityPrivileges(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SUPPORT_CLOSE_ACTIVITY_PRIVILEGES", tenantId);
    }

    public boolean isAllowProcessActivityClose(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_PROCESS_ACTIVITY_CLOSE", tenantId);
    }

    public boolean disableBudgetAmountJudge(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DISABLE_BUDGET_AMOUNT_JUDGE", tenantId);
    }

    public boolean isSkipSaveCheckinProofData(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SKIP_SAVE_CHECKIN_PROOF_DATA", tenantId);
    }

    public boolean isAsyncSaveCheckinProofData(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ASYNC_SAVE_CHECKIN_PROOF_DATA", tenantId);
    }

    public boolean isYinLuEnableList(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_YINLU_ENABLE_LIST", tenantId);
    }

    public boolean isBudgetTypeEnableForceDelete(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_BUDGET_TYPE_ENABLE_FORCE_DELETE", tenantId);
    }

    public static boolean writeOffUseES(String tenantId) {
        return GrayRelease.isAllow("fmcg", "FMCG_TPM_WRITE_OFF_USE_ES", tenantId);
    }

    public static boolean TPMUseEs(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_USE_ES_QUERY", tenantId);
    }

    public static boolean TPMStoreUseEs(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_STORE_USE_ES_QUERY", tenantId);
    }

    public static boolean isAllowNotFillActivityTimeRange(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_NOT_FILL_ACTIVITY_TIME_RANGE", tenantId);
    }

    public static boolean isAllowAccrualCreateByHand(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ACCRUAL_CREATE_BY_HAND", tenantId);
    }

    public static boolean isFirstRewardTopLevel(String tenantId, String activityId) {
        return GrayRelease.isAllow("fmcg", "MN_IS_FIRST_REWARD_TOP_LEVEL", String.format("%s_%s", tenantId, activityId));
    }

    public static boolean isFirstRewardTopLevel(String tenantId) {
        return GrayRelease.isAllow("fmcg", "MN_IS_FIRST_REWARD_TOP_LEVEL", tenantId);
    }

    public static boolean isSkipMengNiuProduceRangeCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SKIP_MENG_NIU_PRODUCT_RANGE_CHECK", tenantId);
    }

    public static boolean isSkipMengNiuTenantCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SKIP_MENG_NIU_TENANT_CHECK", tenantId);
    }

    public static boolean isMengNiuSignInGoodsFreshStandard(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SKIP_MENG_NIU_SIGN_IN_GOODS_FRESH_STANDARD", tenantId);
    }

    public static boolean isAllowOutBoxCodeReward(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_MENG_NIU_OUT_BOX_REWARD", tenantId);
    }

    public static boolean carryForwardShowcase(String tenantId) {
        return GrayRelease.isAllow("fmcg", "CARRY_FORWARD_SHOWCASE", tenantId);
    }

    public static boolean isYuanQiCustomBudgetIgnoreDepartmentDimension(String tenantId) {
        return GrayRelease.isAllow("fmcg", "YUAN_QI_CUSTOM_BUDGET_IGNORE_DEPARTMENT_DIMENSION", tenantId);
    }

    public static boolean isAllowAccrualNegativeMoney(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ACCRUAL_NEGATIVE_MONEY", tenantId);
    }

    public static boolean isCheckDisassemblyQueryAuth(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_CHECK_DISASSEMBLY_QUERY_AUTH", tenantId);
    }

    public static boolean isGuanFang(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_GUAN_FANG_TPM", tenantId);
    }

    public static boolean enableOverLimitWriteOff(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ENABLE_OVER_LIMIT_WRITE_OFF", tenantId);
    }

    public static boolean enableNotFrozenWriteOff(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ENABLE_NOT_FROZEN_WRITE_OFF", tenantId);
    }

    public static boolean isAllowEditDisassemblyCustomField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_EDIT_DISASSEMBLY_CUSTOM_FIELD", tenantId);
    }

    public static boolean isDisableActivityStoreDealerFilter(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_DISABLE_ACTIVITY_STORE_DEALER_FILTER", tenantId);
    }

    public static boolean dealerCashingProductIsRequired(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DEALER_ACTIVITY_COST_CASHING_PRODUCT_IS_REQUIRED", tenantId);
    }

    public static boolean skipCustomBudgetFreezeLifeStatusCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_CUSTOM_BUDGET_FREEZE_LIFE_STATUS_CHECK", tenantId);
    }

    public static boolean skipValidateConfirmAmount(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_VALIDATE_CONFIRM_AMOUNT", tenantId);
    }

    public static boolean activityCashingProductIsRequired(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ACTIVITY_CASHING_PRODUCT_IS_REQUIRED", tenantId);
    }

    public static boolean agreementCashingTypeAllowNull(String tenantId) {
        return GrayRelease.isAllow("fmcg", "AGREEMENT_CASHING_TYPE_ALLOW_NULL", tenantId);
    }

    public static boolean disassemblyAllowZero(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_ALLOW_0_DISASSEMBLY", tenantId);
    }

    public static boolean skipUnifiedCaseHandler(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_UNIFIED_CORRECT_STATUS", tenantId);
    }

    public static boolean notResetEndDate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_NOT_RESET_END_DATE", tenantId);
    }

    public static boolean forceOverDeductInConsumeRule(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_FORCE_DEDUCT_IN_CONSUME_RULE", tenantId);
    }

    public static boolean userNewStoreRangeLogic(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_USE_NEW_STORE_RANGE_LOGIC", tenantId);
    }

    public static boolean dealerActivityCostObjUpdateImport(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DEALER_ACTIVITY_COST_OBJ_UPDATE_IMPORT", tenantId);
    }

    public static boolean proofAllowNoDealer(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_PROOF_ALLOW_NO_DEALER", tenantId);
    }

    public static boolean isAllowActivityImportData(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_ACTIVITY_ALLOW_IMPORT_DATA", tenantId);
    }

    public static boolean allowFixAnyDataDisassemblyStatus(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_FIX_ANY_DATA_DISASSEMBLY_STATUS", tenantId);
    }

    public static boolean calculateAmountWithStore(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_CALCULATE_AMOUNT_WITH_STORE", tenantId);

    }

    public static boolean skipValidateActivityDepartmentRange(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_VALIDATE_ACTIVITY_DEPARTMENT_RANGE", tenantId);
    }

    public static boolean newBudgetTypeDepartmentRangeValidate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "NEW_BUDGET_TYPE_DEPARTMENT_RANGE_VALIDATE", tenantId);
    }

    public static boolean isAllowStoreWriteOffImport(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_STORE_WRITE_OFF_IMPORT", tenantId);
    }

    public static boolean isAllowActivityFunctionUpdate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ACTIVITY_FUNCTION_UPDATE", tenantId);
    }

    public static boolean isLjjUpdateStatusByCloseDate(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_LJJ_UPDATE_STATUS_BY_CLOSE_DATE", tenantId));
    }

    public static boolean isAllowAuditListSupportAuthCode(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_AUDIT_LIST_SUPPORT_AUTH_CODE", tenantId);
    }

    public static boolean isStoreRangeIgnoreDealerFilter(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_STORE_RANGE_IGNORE_DEALER_FILTER", tenantId);
    }

    public static boolean enableDMS(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ENABLE_DMS", tenantId);
    }

    public static boolean enableDMSAutoMatch(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ENABLE_DMS_AUTO_MATCH", tenantId);
    }

    public static boolean isAllowEditActivityObjCustomField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_EDIT_ACTIVITY_OBJ_CUSTOM_FIELD", tenantId);
    }

    public static boolean isAllowEditAgreementObjCustomField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_EDIT_AGREEMENT_OBJ_CUSTOM_FIELD", tenantId);
    }

    public static boolean dmsSkipBusinessSave(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DMS_SKIP_BUSINESS_SAVE", tenantId);
    }

    public static boolean isDirectDeleteActivityType(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_DIRECT_DELETE_ACTIVITY_TYPE", tenantId);
    }

    public static boolean disallowEnableRewardRuleTemplate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DISALLOW_ENABLE_REWARD_RULE_TEMPLATE", tenantId);
    }

    public static boolean dmsOpen1_1ScriptHandlerAuth(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DMS_OPEN1_1_SCRIPT_HANDLER_AUTH", tenantId);
    }

    public static boolean dmsLicenseMQFormalTenantEnable(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DMS_LICENSE_MQ_FORMAL_TENANT_ENABLE", tenantId);
    }

    public static boolean receivableNoSettledAmountSumRealTime(String tenantId) {
        return GrayRelease.isAllow("fmcg", "RECEIVABLE_NO_SETTLED_AMOUNT_SUM_REAL_TIME", tenantId);
    }

    public static boolean returnedNoteBugFixed(String tenantId) {
        return GrayRelease.isAllow("fmcg", "RETURNED_NOTE_BUG_FIXED", tenantId);
    }

    public static boolean useRemoteActionUpdateMengNiuAgreement(String tenantId) {
        return GrayRelease.isAllow("fmcg", "USE_REMOTE_ACTION_UPDATE_MENG_NIU_AGREEMENT", tenantId);
    }

    public static boolean disableBigDateScan(String tenantId) {
        return GrayRelease.isAllow("fmcg", "DISABLE_BIG_DATE_SCAN", tenantId);
    }

    public static boolean enableMengNiuMAgreement(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ENABLE_MENG_NIU_M_AGREEMENT", tenantId);
    }

    public static boolean budgetValidateMessageDesensitization(String tenantId) {
        return GrayRelease.isAllow("fmcg", "BUDGET_VALIDATE_MESSAGE_DESENSITIZATION", tenantId);
    }

    public static boolean isMNUpdateStatusByRedStatus(String tenantId) {
        return Boolean.TRUE.equals(GrayRelease.isAllow("fmcg", "IS_MN_UPDATE_STATUS_BY_RED_STATUS", tenantId));
    }

    /**
     * 允许带有活动的价格政策
     *
     * @param tenantId
     * @return
     */
    public static boolean isAllowActivityIdPromotionPolicy(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ACTIVITY_ID_PROMOTION_POLICY", tenantId) || isRioTenant(tenantId);
    }

    public static boolean skipActivityOnceWriteOffValidate(String tenantId) {
        return GrayRelease.isAllow("fmcg", "SKIP_ACTIVITY_ONE_WRITE_OFF_VALIDATE", tenantId);
    }

    public static boolean isRioTenant(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_RIO_TENANT", tenantId);
    }

    public static boolean isOpenCustomObjectEdit(String tenantId) {
        return GrayRelease.isAllow("fmcg", "TPM_OPEN_CUSTOM_OBJECT_EDIT", tenantId);
    }

    /**
     * 海信企业，允许选择未开始的活动方案。
     *
     * @param tenantId
     * @return
     */
    public static boolean allowScheduleUnifiedCaseFilter(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_SCHEDULE_UNIFIED_CASE_FILTER", tenantId);
    }

    /**
     * RIO企业，允许创建不关联活动的价格政策。
     *
     * @param tenantId
     * @return
     */
    public static boolean allowAddUnActivityPromotionPolicy(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_ADD_UN_ACTIVITY_PROMOTION_POLICY", tenantId) || isRioTenant(tenantId);
    }

    public static boolean objectActionRewardsEnable(String tenantId) {
        return GrayRelease.isAllow("fmcg", "fmcg_tpm_object_action_rewards_enable_tenant", tenantId);
    }

    public static boolean disallowDuplicateProductRange(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_DUPLICATE_PRODUCT_RANGE", tenantId);
    }

    public static boolean allowMengNiuAgentSales(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_MENG_NIU_AGENT_SALES", tenantId);
    }

    public static boolean allowAccountsReceivableAmortize(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_ACCOUNTS_RECEIVABLE_AMORTIZE", tenantId);
    }

    /**
     * RIO企业，允许活动申请编辑指定的预置字段。
     * 目前指定字段： activity_amount
     *
     * @param tenantId
     * @return
     */
    public static boolean allowEditActivityPreField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_EDIT_ACTIVITY_PRE_FIELD", tenantId);
    }

    /**
     * 激励类活动不校验参与部门
     *
     * @param tenantId
     * @return
     */

    public static boolean jumpDepartmentInRewardActivity(String tenantId) {
        return GrayRelease.isAllow("fmcg", "JUMP_DEPARTMENT_IN_REWARD_ACTIVITY", tenantId);
    }

    // ALLOW_MENG_NIU_RED_PACKET_PUBLISH_V2=deny;
    public static boolean allowMengNiuRedPacketPublishV2(String tenantId, String recordType) {
        return GrayRelease.isAllow("fmcg", "ALLOW_MENG_NIU_RED_PACKET_PUBLISH_V2", tenantId);
    }

    public static boolean allowMengNiuRedPacketPublishGrayTenant(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_MENG_NIU_RED_PACKET_PUBLISH_GRAY_TENANT", tenantId);
    }

    // ALLOW_AGREEMENT_AUDIT_ADD_LOG_CUSTOM_MESSAGE=deny;
    public static boolean allowAgreementAuditAddLogCustomMessage(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_AGREEMENT_AUDIT_ADD_LOG_CUSTOM_MESSAGE", tenantId);
    }

    /**
     * 应收单明细的发货单、入库单是否切换到了what字段
     *
     * @param tenantId
     * @return
     */
    public static boolean accountReceivableDetailUseWhatField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ACCOUNT_RECEIVABLE_DETAIL_USE_WHAT_FIELD", tenantId);
    }

    /**
     * 是不是应收的老企业
     */
    public static boolean accountReceivableOldTenant(String tenantId) {
        return FS_GRAY_RELEASE_BIZ.isAllow("accounts_receivable_old_tenants", tenantId);
    }

    public static boolean isAllowFescoAuthValidateBeforeCreateTask(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_FESCO_AUTH_VALIDATE_BEFORE_CREATE_TASK", tenantId);
    }

    public static boolean isSkipAddStoreWriteOffByActivity(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_SKIP_ADD_STORE_WRITE_OFF_BY_ACTIVITY", tenantId);
    }

    public static boolean isAllowConsumeRuleFreezeRepeatableBudgetTable(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_CONSUME_RULE_FREEZE_REPEATABLE_BUDGET_TABLE", tenantId);
    }

    public static boolean isAllowMengNiuRedPacketAuthUserRemind(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_MENGNIU_RED_PACKET_AUTH_USER_REMIND", tenantId);
    }

    public static boolean allowNewConsumerScanReward(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_NEW_CONSUMER_SCAN_REWARD", tenantId);
    }

    public static boolean isSupportStockCheckReward(String tenantId) {
        return GrayRelease.isAllow("fmcg", "allowStoreCheckReward", tenantId);
    }

    public static boolean enableUnlockStatusCheck(String tenantId) {
        return GrayRelease.isAllow("fmcg", "MN_ENABLE_UNLOCK_STATUS_CHECK", tenantId);
    }

    public static boolean allowPhysicalItemWriteOff(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_PHYSICAL_ITEM_WRITE_OFF", tenantId);
    }

    public boolean isRedSunAsyncCreateTaskAllow(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_RED_SUN_ASYNC_CREATE_TASK_ALLOW", tenantId);
    }

    public boolean isOpenSecurityJudge(String appId) {
        return GrayRelease.isAllow("fmcg", "IS_OPEN_SECURITY_JUDGE", appId);
    }

    public boolean isOpenFixedRewardAccount(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_OPEN_FIXED_REWARD_ACCOUNT", tenantId);
    }

    public static boolean isAllowAccessAuth(Integer tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ACCESS_AUTH", tenantId);
    }

    public boolean carryForwardDefaultCarryForwardType(String tenantId) {
        return GrayRelease.isAllow("fmcg", "CARRY_FORWARD_DEFAULT_CARRY_FORWARD_TYPE", tenantId);
    }

    public boolean isOpenYqslStoreWriteOffMiddleDeduct(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_OPEN_YQSL_STORE_WRITE_OFF_MIDDLE_DEDUCT", tenantId);
    }

    public static Boolean isConsumeRuleEnableRelease(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_CONSUME_RULE_ENABLE_RELEASE", tenantId);
    }

    public static Boolean isConsumeRuleEnableProvision(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_CONSUME_RULE_ENABLE_PROVISION", tenantId);
    }

    public static Boolean forceCheckWithholdingField(String tenantId) {
        return GrayRelease.isAllow("fmcg", "FORCE_CHECK_WITHHOLDING_FIELD", tenantId);
    }

    public static Boolean isYuanQiFilterAgreementDetailCurrent(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_YUAN_QI_FILTER_AGREEMENT_DETAIL_CURRENT", tenantId);
    }

    public static Boolean isAllowAdvancedRewardLimit(String tenantId) {
        return GrayRelease.isAllow("fmcg", "IS_ALLOW_ADVANCED_REWARD_LIMIT", tenantId);
    }

    public static boolean allowSpecialRepeatAction(String activityId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_SPECIAL_REPEAT_ACTION", activityId);
    }

    public static boolean isAllowUseTemplateGroupByTenantId(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_ALLOW_USE_TEMPLATE_GROUP_BY_TENANT_ID",tenantId);
    }

    public static boolean allowEnableActivityAI(String tenantId) {
        return GrayRelease.isAllow("fmcg", "ALLOW_ENABLE_ACTIVITY_AI", tenantId);
    }

    public static boolean partialPassConvertToFail(String tenantId) {
        return GrayRelease.isAllow("fmcg", "PARTIAL_PASS_CONVERT_TO_FAIL", tenantId) || isRioTenant(tenantId);
    }


    public static Boolean isAllowProofEditField(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_ALLOW_PROOF_EDIT_FIELD",tenantId);
    }

    public static Boolean isAllowProofDetailEditCustomField(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_ALLOW_PROOF_DETAIL_EDIT_CUSTOM_FIELD",tenantId);
    }

    public static boolean isSkipActivityFilterActivityStatus(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_SKIP_ACTIVITY_FILTER_ACTIVITY_STATUS",tenantId);
    }

    public static boolean isMengNiuFilterActivitySerialNumber(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_MENGNIU_FILTER_ACTIVITY_SERIAL_NUMBER",tenantId);
    }

    public static boolean isMengNiuFilterCustomBCSerialNumber(String tenantId) {
        return GrayRelease.isAllow("fmcg","IS_MENGNIU_FILTER_CUSTOM_BC_SERIAL_NUMBER",tenantId);
    }
    public static boolean isSupportRewardTag(String tenantId) {
        return GrayRelease.isAllow("fmcg", "isSupportRewardTag", tenantId);
    }
}
