package com.facishare.crm.fmcg.common.apiname;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2020/11/6 2:10 PM
 */
public abstract class TPMActivityFields {

    private TPMActivityFields() {
    }

    public static final String NAME = "name";
    public static final String BEGIN_DATE = "begin_date";
    public static final String END_DATE = "end_date";
    public static final String ACTIVITY_TYPE = "activity_type";
    public static final String ACTIVITY_STATUS = "activity_status";
    public static final String DEALER_ID = "dealer_id";
    public static final String PROOF_RECORD_TYPE = "proof_record_type";
    public static final String STORE_RANGE = "store_range";
    public static final String STORE_RANGE_TYPE = "store_range_type";
    public static final String SUBJECT = "subject";
    public static final String IS_AGREEMENT_REQUIRED = "is_agreement_required";
    public static final String DESCRIPTION = "description";
    public static final String DEPARTMENT_RANGE = "department_range";
    public static final String MULTI_DEPARTMENT_RANGE = "multi_department_range";
    public static final String BUDGET_TABLE = "budget_table";
    public static final String ACTIVITY_AMOUNT = "activity_amount";
    public static final String CLOSED_STATUS = "closed_status";
    public static final String AVAILABLE_AMOUNT = "available_amount";
    public static final String ACTIVITY_ACTUAL_AMOUNT = "activity_actual_amount";
    public static final String ACTIVITY_FROZEN_AMOUNT = "activity_frozen_amount";
    public static final String CLOSE_TIME = "close_time";
    public static final String CLOSE_STATUS = "closed_status";
    public static final String LIFE_STATUS = "life_status";
    public static final String DEALER_CASHING_TYPE = "dealer_cashing_type";
    public static final String STORE_CASHING_TYPE = "store_cashing_type";
    public static final String ACTIVITY_UNIFIED_CASE_ID = "activity_unified_case_id";
    public static final String PRODUCT_RANGE = "product_range";
    public static final String CASHING_PRODUCT_RANGE = "cashing_product_range";
    public static final String ACTIVITY_STATUS__SCHEDULE = "schedule";
    public static final String ACTIVITY_STATUS__IN_PROGRESS = "in_progress";
    public static final String ACTIVITY_STATUS__END = "end";
    public static final String ACTIVITY_STATUS__CLOSED = "closed";
    public static final String ACTIVITY_STATUS__APPROVAL = "approval";
    public static final String ACTIVITY_STATUS__INEFFECTIVE = "ineffective";
    public static final String CLOSE_STATUS__CLOSED = "closed";
    public static final String CLOSE_STATUS__UNCLOSED = "unclosed";
    public static final String PROOF_RECORD_TYPE__C = "proof_record_type__c";
    public static final String YINLU_AGREEMENT_BEGIN_DATE = "agreement_begin_date__c";
    public static final String YINLU_AGREEMENT_END_DATE = "agreement_end_date__c";
    public static final String MAX_WRITE_OFF_COUNT = "max_write_off_count";
    public static final String LAST_WRITE_OFF_DATE = "last_write_off_date";
    public static final String APPLICATION_DATE = "application_date";
    public static final String PHONE_NUMBER = "phone_number";
    public static final String REMARKS = "remarks";
    public static final String ACCOUNT_ADDRESS_ID = "account_address_id";
    public static final String CONSIGNEE = "consignee";
    public static final String ADDRESS = "address";
    public static final String ATTACHMENT = "attachment";
    public static final String RECORDED_AMOUNT = "recorded_amount";
    public static final String REMAINING_WRITE_OFF_AMOUNT = "remaining_write_off_amount";
    public static final String CUSTOMER_TYPE = "customer_type";
    public static final String IS_AUTO_CLOSE = "is_auto_close";
    public static final String SOURCE_OBJECT_API_NAME = "source_object_api_name";
    public static final String MODE_TYPE = "mode_type";
    public static final String LIMIT_OBJ_TYPE = "limit_obj_type";
    public static final String LIMIT_OBJ_TYPE_ACTIVITY_AMOUNT_TOTAL = "activity_amount_total";
    public static final String LIMIT_OBJ_TYPE_ACCOUNT_COST_DIFFERENCE = "account_cost_difference";
    public static final String LIMIT_OBJ_TYPE_ACCOUNT_COST_EQUAL = "account_cost_equal";
    public static final String ACCOUNT_UNIFY_LIMIT_AMOUNT = "account_unify_limit_amount";
    public static final String TOTAL_POLICY_DYNAMIC_AMOUNT = "total_policy_dynamic_amount";
    public static final String REWARD_RULE_JSON = "reward_rule_json";

    public static final String IS_NEED_RIO_STORE_CONFIRM = "field_2ZIs0__c";


    public static final String PERSON_REWARD_RULE_CODE = "personnel_reward_rule_code__c";
    public static final String PERSON_REWARD_RULE_WHERE = "personnel_reward_rule_where__c";
    public static final String PERSON_REWARD_RULE_TRANSLATE = "personnel_reward_rule_translate__c";

    public static final String PRODUCT_RANGE_FRESH_STANDARD = "product_range_fresh_standard";
    public static final String ACTION_RULE_TEMPLATE_TENANT = "action_rule_template_tenant__c";
    public static final String PHYSICAL_ITEM_WRITE_OFF_CLOUD_ACCOUNT ="physical_item_write_off_cloud_account__c";

    public static final String IS_ALLOW_OUTER_CODE_SCAN = "is_allow_outer_code_scan__c";
    /*
     * RIO 自定义活动方案ID
     * */
    public static final String RIO_ACTIVITY_ID = "field_pxe07__c";
    public static final String MN_OBJECT_ACTION_RED_PACKET_EXPIRATION_DAYS = "object_action_red_packet_expiration_days__c";

    public class ProductRangeFreshStandard {
        public static final String BY_DATE_RANGE = "byDateRange";

        public static final String NO_LIMIT = "noLimit";

        public static final String BY_REMAINING_DAYS = "byRemainingDays";
    }

    public class ActivityRecordType{
        //囤货激励类型
        public static final String SIGN_IN_GOODS_REWARDS = "sign_in_goods_rewards__c";

    }

    public static final String ACTIVATION_START_TIME = "activation_start_time__c";
    public static final String ACTIVATION_END_TIME = "activation_end_time__c";

    public static final String ACTIVITY_MONTH_AMOUNT = "activity_month_amount__c";

    public static final String PROOF_PERIOD = "proof_period";

}
