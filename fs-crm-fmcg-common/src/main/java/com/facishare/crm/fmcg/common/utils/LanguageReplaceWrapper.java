package com.facishare.crm.fmcg.common.utils;

import com.github.trace.TraceContext;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LanguageReplaceWrapper {


    public static void doInChinese(Runnable run) {
        String originalLocal = TraceContext.get().getLocale();
        TraceContext.get().setLocale("zh-CN,zh-TW;0.9,en;0.8");
        try {
            run.run();
        } finally {
            log.info("set original locale:{}", originalLocal);
            TraceContext.get().setLocale(originalLocal);
        }
    }
}
