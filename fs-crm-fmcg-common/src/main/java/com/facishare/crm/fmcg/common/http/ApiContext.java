package com.facishare.crm.fmcg.common.http;

import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * description : just code
 * <p>
 * create by @yangqf
 * create time 2021/11/16 18:00
 */
@Data
@ToString
@Builder
public class ApiContext implements Serializable {

    private String tenantId;

    private String tenantAccount;

    private Integer employeeId;

    private String postId;

    private String appId;

    private String outTenantId;

    private String outUserId;
}
