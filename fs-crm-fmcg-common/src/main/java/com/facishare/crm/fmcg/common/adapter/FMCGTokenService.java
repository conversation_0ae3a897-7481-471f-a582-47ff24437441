package com.facishare.crm.fmcg.common.adapter;

import com.facishare.appserver.checkins.api.model.GetOpenIdByToken;
import com.facishare.crm.fmcg.common.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.facishare.appserver.checkins.api.service.ShopMMService;
import com.facishare.crm.fmcg.common.adapter.abstraction.IFMCGTokenService;
import com.facishare.crm.fmcg.common.adapter.dto.token.GetWXOpenIdAndPhone;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Author: linmj
 * Date: 2023/9/20 14:28
 */
@Slf4j
@Service
public class FMCGTokenService implements IFMCGTokenService {

    @Resource
    private ShopMMService shopMMService;

    @Override
    public GetWXOpenIdAndPhone.Result getWXOpenIdAndPhone(GetWXOpenIdAndPhone.Arg arg) {

        if ("1".equals(arg.getPhoneToken()) || "1".equals(arg.getOpenIdToken())) {
            GetWXOpenIdAndPhone.Result result = new GetWXOpenIdAndPhone.Result();
            result.setOpenId(arg.getOpenIdToken());
            result.setPhone("1");
            return result;
        }

        GetOpenIdByToken.Args tokenArg = new GetOpenIdByToken.Args();
        tokenArg.setTenantId(arg.getTenantId());
        tokenArg.setToken(arg.getOpenIdToken());
        tokenArg.setAppId(arg.getAppId());
        tokenArg.setPhoneToken(arg.getPhoneToken());

        log.info("convert to WeChat account information arg : {}", tokenArg);

        GetOpenIdByToken.Result result = shopMMService.getOpenIdByToken(tokenArg);

        if (result.getErrorCode() != 0) {
            log.warn("convert to WeChat account information error : {}.{}", result.getErrorCode(), result.getMessage());
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_F_M_C_G_TOKEN_SERVICE_0));
        }

        log.info("convert to WeChat account information result : {}", result);
        if (Strings.isNullOrEmpty(result.getOpenId())) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_F_M_C_G_TOKEN_SERVICE_1));
        }
        return new GetWXOpenIdAndPhone.Result(result.getOpenId(), result.getPhoneNumber(), result.getUnionId());
    }
}
