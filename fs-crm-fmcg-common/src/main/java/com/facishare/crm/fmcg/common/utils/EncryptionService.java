package com.facishare.crm.fmcg.common.utils;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.common.i18n.I18NKeys;
import com.facishare.paas.I18N;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.facishare.crm.fmcg.common.pojo.SnCode;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.github.autoconf.ConfigFactory;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Slf4j
@Component
public class EncryptionService {

    private static final long EXPIRE_SPAN = 1800000;

    private static final String TYPE_CLAIM_KEY = "TYPE";
    private static final String SN_ID_CLAIM_KEY = "SN_ID";
    private static final String MANUFACTURER_TENANT_ID_CLAIM_KEY = "MANUFACTURER_TENANT_ID";
    private static final String STORE_TENANT_ID_CLAIM_KEY = "STORE_TENANT_ID";
    private static final String ACTIVITY_ID_CLAIM_KEY = "ACTIVITY_ID";
    private static final String STORE_ID_CLAIM_KEY = "STORE_ID";
    private static final String ACTIVITY_PRODUCT_ID_CLAIM_KEY = "ACTIVITY_PRODUCT_ID";
    private static final String REWARD_METHOD = "REWARD_METHOD";

    private static final Map<String, Object> JWT_HEADER = new HashMap<>();

    private static String secret;

    @PostConstruct
    void init() {
        ConfigFactory.getConfig("fs-fmcg-framework-config", conf -> secret = conf.get("meng-niu-sn-encryptionCode-secret"));
    }

    static {
        JWT_HEADER.put("typ", "JWT");
        JWT_HEADER.put("alg", "HS256");
    }

    public String sign(SnCode code) {
        Date expire = new Date(System.currentTimeMillis() + EXPIRE_SPAN);
        Algorithm alg = Algorithm.HMAC256(secret);
        return JWT.create()
                .withHeader(JWT_HEADER)
                .withClaim(TYPE_CLAIM_KEY, Strings.isNullOrEmpty(code.getType()) ? "" : code.getType())
                .withClaim(SN_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getSnId()) ? "" : code.getSnId())
                .withClaim(MANUFACTURER_TENANT_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getManufacturerTenantId()) ? "" : code.getManufacturerTenantId())
                .withClaim(STORE_TENANT_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getStoreTenantId()) ? "" : code.getStoreTenantId())
                .withClaim(ACTIVITY_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getActivityId()) ? "" : code.getActivityId())
                .withClaim(STORE_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getStoreId()) ? "" : code.getStoreId())
                .withClaim(ACTIVITY_PRODUCT_ID_CLAIM_KEY, Strings.isNullOrEmpty(code.getActivityProductId()) ? "" : code.getActivityProductId())
                .withClaim(REWARD_METHOD, code.getRewardMethod())
                .withExpiresAt(expire).sign(alg);
    }

    public SnCode verify(String id) {
        Algorithm alg = Algorithm.HMAC256(secret);
        JWTVerifier verifier = JWT.require(alg).build();
        DecodedJWT decoded = verifier.verify(id);
        return SnCode.builder()
                .type(decoded.getClaim(TYPE_CLAIM_KEY).asString())
                .snId(decoded.getClaim(SN_ID_CLAIM_KEY).asString())
                .manufacturerTenantId(decoded.getClaim(MANUFACTURER_TENANT_ID_CLAIM_KEY).asString())
                .storeTenantId(decoded.getClaim(STORE_TENANT_ID_CLAIM_KEY).asString())
                .activityId(decoded.getClaim(ACTIVITY_ID_CLAIM_KEY).asString())
                .storeId(decoded.getClaim(STORE_ID_CLAIM_KEY).asString())
                .activityProductId(decoded.getClaim(ACTIVITY_PRODUCT_ID_CLAIM_KEY).asString())
                .rewardMethod(decoded.getClaim(REWARD_METHOD).asString())
                .build();
    }


    public String sign(Object data) {
        Date expire = new Date(System.currentTimeMillis() + 3600_000 * 24);
        Algorithm alg = Algorithm.HMAC256(secret);
        return JWT.create()
                .withHeader(JWT_HEADER)
                .withClaim("data", JSON.toJSONString(data))
                .withExpiresAt(expire).sign(alg);
    }

    public <T> T verify(String id, Class<T> clazz) {
        Algorithm alg = Algorithm.HMAC256(secret);
        JWTVerifier verifier = JWT.require(alg).build();
        DecodedJWT decoded = verifier.verify(id);
        return JSON.parseObject(decoded.getClaim("data").asString(), clazz);
    }

    public String sha256(String input) {
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new MetaDataBusinessException(I18N.text(I18NKeys.METADATA_ENCRYPTION_SERVICE_0));
        }
        byte[] hash = md.digest(input.getBytes());
        StringBuilder hexString = new StringBuilder();
        for (byte b : hash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}
