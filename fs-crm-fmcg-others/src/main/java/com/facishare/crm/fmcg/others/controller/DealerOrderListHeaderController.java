package com.facishare.crm.fmcg.others.controller;

import com.facishare.crm.fmcg.others.business.BusinessTradingIndustryService;
import com.facishare.paas.appframework.core.predef.controller.StandardListHeaderController;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @create 2021 - 12 - 07  5:42 下午
 **/
@Slf4j
public class DealerOrderListHeaderController extends StandardListHeaderController {

    @Override
    protected Result after(Arg arg, Result result) {
        BusinessTradingIndustryService.filterObjButtons(controllerContext.getTenantId(),arg.getApiName(),result);
        return super.after(arg, result);
    }
}
