package com.facishare.crm.fmcg.dms.util;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.fmcg.dms.model.EnterpriseFundAccountInfoDTO;
import com.google.common.collect.Lists;
import org.springframework.util.StringUtils;

import java.util.List;

public class CastUtil {
    public static List<EnterpriseFundAccountInfoDTO> castToEnterpriseFundAccountInfoDTO(String enterpriseFundAccountInfo) {
        if (StringUtils.isEmpty(enterpriseFundAccountInfo)) {
            return Lists.newArrayList();
        }
        return JSON.parseArray(enterpriseFundAccountInfo, EnterpriseFundAccountInfoDTO.class);
    }
}
