package com.facishare.crm.fmcg.dms.model;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

public interface TriggerAction {
    @Builder
    @Data
    @ToString
    class Arg implements Serializable {

        private User user;

        private IObjectData objectData;

        private List<IObjectData> details;

        private String apiName;

        private String detailApiName;

        private String actionName;

        private boolean triggerWorkflow;

        private boolean triggerFlow;
    }
}
