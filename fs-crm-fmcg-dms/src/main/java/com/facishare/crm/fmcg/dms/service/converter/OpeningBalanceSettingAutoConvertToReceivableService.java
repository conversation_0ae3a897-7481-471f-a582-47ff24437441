package com.facishare.crm.fmcg.dms.service.converter;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.dms.model.ConvertResult;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.AutoOpeningBalanceSettingConvertService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.impl.ObjectData;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class OpeningBalanceSettingAutoConvertToReceivableService extends AutoOpeningBalanceSettingConvertService {

    @Override
    protected void beforeConvert(FinancialBill bill) {

        bill.setData(serviceFacade.findObjectData(User.systemUser(bill.getTenantId()), bill.getId(), ApiNames.OPENING_BALANCE_SETTING_OBJ));


    }

    @Override
    protected void validate(FinancialBill bill) {
        idempotent(bill);


    }

    @Override
    protected ConvertResult convertData(FinancialBill bill) {
        return ConvertResult.builder().data(covertToMaster(bill)).details(convertToDetails(bill)).build();
    }

    private IObjectData covertToMaster(FinancialBill bill) {
        IObjectData data = new ObjectData();

        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_NOTE_OBJ);
        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, MultiRecordType.RECORD_TYPE_DEFAULT);

        data.set(AccountsReceivableNoteFields.ACCOUNT_ID, bill.getData().get(OpeningBalanceSettingFields.ACCOUNT_ID));
        data.set(AccountsReceivableNoteFields.CONTACT_OBJECT, AccountsReceivableNoteFields.CONTACT_OBJECT__AccountObj);
        data.set(AccountsReceivableNoteFields.OPENING_BALANCE, true);
        data.set(AccountsReceivableNoteFields.NOTE_DATE, bill.getData().get(OpeningBalanceSettingFields.BUSINESS_DATE));
        data.set(AccountsReceivableNoteFields.SYSTEM_SOURCE, AccountsReceivableNoteFields.SYSTEM_SOURCE__accountsReceivableSystem);
        data.set(AccountsReceivableNoteFields.REMARKS, bill.getData().get(OpeningBalanceSettingFields.REMARKS));
        data.set(AccountsReceivableNoteFields.DUE_DATE, bill.getData().get(OpeningBalanceSettingFields.DUE_DATE));

        return data;
    }

    private List<IObjectData> convertToDetails(FinancialBill bill) {

        IObjectData data = new ObjectData();
        data.setTenantId(bill.getTenantId());
        data.setDescribeApiName(ApiNames.ACCOUNTS_RECEIVABLE_DETAIL_OBJ);

        data.set(CommonFields.CREATE_BY, bill.getData().get(CommonFields.CREATE_BY));
        data.set(CommonFields.OWNER, bill.getData().get(CommonFields.OWNER));
        data.set(CommonFields.RECORD_TYPE, CommonFields.RECORD_TYPE__DEFAULT);
        data.set(CommonFields.LIFE_STATUS, CommonFields.LIFE_STATUS__NORMAL);

        data.set(AccountsPayableDetailFields.PRICE_TAX_AMOUNT, bill.getData().get(OpeningBalanceSettingFields.OPENING_BALANCE));

        return Lists.newArrayList(data);
    }


    @Override
    protected void after(FinancialBill bill, BaseObjectSaveAction.Result result) {
        super.after(bill, result);
    }


}
