package com.facishare.crm.fmcg.dms.business;


import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.dms.business.abstraction.BaseHandler;
import com.facishare.crm.fmcg.dms.business.abstraction.IDMSScriptHandler;
import com.facishare.crm.fmcg.dms.business.enums.ScriptHandlerNameEnum;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.auth.model.AuthContext;
import com.facishare.paas.auth.model.FunctionPojo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.UdefButton;
import com.fxiaoke.paas.auth.factory.FuncClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Component
@Slf4j
public class AccountReceivableEnableScriptHandler extends BaseHandler implements IDMSScriptHandler {
    @Resource
    private FuncClient funcClient;

    @Override
    public boolean checkHandler(String handlerName) {
        return ScriptHandlerNameEnum.ACCOUNTS_RECEIVABLE_ENABLE.getHandlerName().equals(handlerName);
    }

    @Override
    public void addFields(String tenantId, Map<String, IObjectDescribe> describes) {
        //入库单 累计结算金额
        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.GOODS_RECEIVED_NOTE_OBJ), ApiNames.GOODS_RECEIVED_NOTE_OBJ,
                    GoodsReceivedNoteFields.TOTAL_SETTLED_AMOUNT, true, true);
        } catch (Exception ex) {
            log.error("新增入库单.累计结算金额字段失败", ex);
        }

        try {
            //退货单产品.已结算金额
            fieldBusiness.addField(tenantId, describes.get(ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ), ApiNames.RETURNED_GOODS_INVOICE_PRODUCT_OBJ,
                    ReturnedGoodsInvoiceProductFields.SETTLED_AMOUNT, true, true);
        } catch (Exception ex) {
            log.error("新增退货单产品.已结算金额字段失败", ex);
        }

        try {
            //退货单.累计结算金额
            fieldBusiness.addField(tenantId, describes.get(ApiNames.RETURNED_GOODS_INVOICE_OBJ), ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                    ReturnedGoodsInvoiceFields.TOTAL_SETTLED_AMOUNT, true, true);
        } catch (Exception ex) {
            log.error("新增退货单.累计结算金额字段失败", ex);
        }

        try {
            //退货单.已退款金额
            fieldBusiness.addField(tenantId, describes.get(ApiNames.RETURNED_GOODS_INVOICE_OBJ), ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                    ReturnedGoodsInvoiceFields.REFUND_AMOUNT, true, true);
        } catch (Exception ex) {
            log.error("新增退货单.已退款金额字段失败", ex);
        }

        try {
            //退货单.待退款金额
            fieldBusiness.addField(tenantId, describes.get(ApiNames.RETURNED_GOODS_INVOICE_OBJ), ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                    ReturnedGoodsInvoiceFields.PENDING_REFUND_AMOUNT, true, true);
        } catch (Exception ex) {
            log.error("新增退货单.待退款金额字段失败", ex);
        }

        try {
            //退货单.待确认退款金额
            fieldBusiness.addField(tenantId, describes.get(ApiNames.RETURNED_GOODS_INVOICE_OBJ), ApiNames.RETURNED_GOODS_INVOICE_OBJ,
                    ReturnedGoodsInvoiceFields.REFUND_AMOUNT_TO_BE_CONFIRMED, false, true);
        } catch (Exception ex) {
            log.error("新增退货单.待退款金额字段失败", ex);
        }
        //供应商
        try {
            fieldBusiness.addField(tenantId, describes.get(ApiNames.MATCH_NOTE_OBJ), ApiNames.MATCH_NOTE_OBJ,
                    MatchNoteFields.SUPPLIER_ID, false, true);
        } catch (Exception ex) {
            log.error("新增核销单.供应商字段失败", ex);
        }
    }

    @Override
    public void updateFields(String tenantId, Map<String, IObjectDescribe> describes) {
        //开启了应收、未开启快消行业套件
        IObjectDescribe orderDescribe = describes.get(ApiNames.SALES_ORDER_OBJ);
        for (IFieldDescribe fieldDescribe : orderDescribe.getFieldDescribes()) {
            if (SalesOrderObjFields.PAID_AMOUNT.equals(fieldDescribe.getApiName())) {
                if (isOpenRebate(tenantId)) {
                    fieldDescribe.setExpression(OTHER_PAID_AMOUNT_EXPRESSION_ENABLE_REBATE);
                } else {
                    fieldDescribe.setExpression(OTHER_PAID_AMOUNT_EXPRESSION_DISABLE_REBATE);
                }
                serviceFacade.updateFieldDescribe(orderDescribe, Lists.newArrayList(fieldDescribe));
            }
        }

        IObjectDescribe describe = describes.get(ApiNames.MATCH_NOTE_OBJ);
        for (IFieldDescribe fieldDescribe : describe.getFieldDescribes()) {
            if (MatchNoteFields.VERIFICATION_METHOD.equals(fieldDescribe.getApiName())) {


                fieldDescribe.set("options", addVerificationMethodOptions((List<Map<String, String>>) fieldDescribe.get("options")));

                serviceFacade.updateFieldDescribe(describe, Lists.newArrayList(fieldDescribe));
            }
        }
    }

    @Override
    public void initButton(String tenantId) {

        IUdefButton old = serviceFacade.findButtonByApiName(User.systemUser(tenantId), ObjectAction.REFUND.getButtonApiName(), ApiNames.RETURNED_GOODS_INVOICE_OBJ);
        if (Objects.isNull(old)) {
            IUdefButton button = new UdefButton();
            button.setTenantId(tenantId);
            button.setDescribeApiName(ApiNames.RETURNED_GOODS_INVOICE_OBJ);
            button.setApiName(ObjectAction.REFUND.getButtonApiName());
            button.setLabel("退款");  //ignorei18n
            button.setDefineType("system");
            button.setButtonType("common");
            button.setParamForm(Lists.newArrayList());
            button.setJumpUrl("");
            button.setLockDataShowButton(false);

            Wheres wheres = new Wheres();


            wheres.setFilters(Lists.newArrayList());
            button.setWheres(Lists.newArrayList(wheres));

            button.setIsActive(true);
            button.setDeleted(false);
            button.setUsePages(Lists.newArrayList("detail", "list"));
            serviceFacade.createCustomButton(User.systemUser(tenantId), button);

            AuthContext authContext = AuthContext.builder().userId("-10000").tenantId(tenantId).appId("CRM").build();
            String functionCode = String.format("%s||%s", ApiNames.RETURNED_GOODS_INVOICE_OBJ, ObjectAction.REFUND.getActionCode());
            FunctionPojo function = new FunctionPojo();
            function.setAppId("CRM");
            function.setParentCode("00000000000000000000000000000000");
            function.setTenantId(tenantId);
            function.setFuncName("退款");  //ignorei18n
            function.setFuncCode(functionCode);
            function.setFuncType(0);
            function.setIsEnabled(true);
            funcClient.addFunc(authContext, Lists.newArrayList(function));

            Set<String> addFunctionCodes = Sets.newHashSet();
            addFunctionCodes.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000006", addFunctionCodes, Sets.newHashSet());

            Set<String> addFunctionCodes2 = Sets.newHashSet();
            addFunctionCodes2.add(functionCode);
            funcClient.updateRoleModifiedFuncPermission(authContext, "00000000000000000000000000000015", addFunctionCodes2, Sets.newHashSet());
        }
    }


}
