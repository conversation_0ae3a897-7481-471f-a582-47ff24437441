package com.facishare.crm.fmcg.dms.util;

import com.facishare.crm.fmcg.common.apiname.ApiNames;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class PayableConvertBeanNameUtil {


    private static final Map<String, String> PAYABLE_SUPPORT_OBJECTS = Maps.newHashMap();

    static {
        PAYABLE_SUPPORT_OBJECTS.put(ApiNames.PURCHASE_ORDER_OBJ, "purchaseOrderAutoPayableConvertToPayService");
        PAYABLE_SUPPORT_OBJECTS.put(ApiNames.GOODS_RECEIVED_NOTE_OBJ, "goodsReceivedNoteAutoPayableConvertToPayableService");
        PAYABLE_SUPPORT_OBJECTS.put(ApiNames.OUTBOUND_DELIVERY_NOTE_OBJ, "outboundDeliveryNoteAutoPayableConvertToPayableService");

    }

    public String beanName(FinancialBill bill) {
        return PAYABLE_SUPPORT_OBJECTS.get(bill.getApiName());
    }


}
