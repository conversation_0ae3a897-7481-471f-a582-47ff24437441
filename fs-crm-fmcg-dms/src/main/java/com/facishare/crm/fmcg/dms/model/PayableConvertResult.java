package com.facishare.crm.fmcg.dms.model;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@ToString
@Builder
public class PayableConvertResult implements Serializable {

    private List<Payable> result;


    @Data
    @Builder
    @ToString
    public static class Payable {
        private IObjectData data;

        private List<IObjectData> details;
    }


}
