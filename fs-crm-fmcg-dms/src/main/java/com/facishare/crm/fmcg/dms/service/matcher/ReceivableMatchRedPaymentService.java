package com.facishare.crm.fmcg.dms.service.matcher;

import com.facishare.crm.fmcg.common.apiname.*;
import com.facishare.crm.fmcg.common.gray.TPMGrayUtils;
import com.facishare.crm.fmcg.common.utils.QueryDataUtil;
import com.facishare.crm.fmcg.dms.model.FinancialBill;
import com.facishare.crm.fmcg.dms.service.abastraction.ReceivableMatchService;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.google.common.collect.Lists;
import de.lab4inf.math.util.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
@SuppressWarnings("Duplicates")
public class ReceivableMatchRedPaymentService extends ReceivableMatchService {

    @Resource
    private ServiceFacade serviceFacade;

    @Override
    protected Map<String, List<IObjectData>> groupByDimensionDataId(String tenantId, List<IObjectData> details) {
        if (!TPMGrayUtils.accountReceivableDetailUseWhatField(tenantId)) {
            return details.stream()
                    .filter(detail -> !Strings.isNullOrEmpty(detail.get(AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID, String.class)))
                    .collect(Collectors.groupingBy(detail -> detail.get(AccountsReceivableDetailFields.GOODS_RECEIVED_NOTE_ID, String.class)));
        } else {
            return details.stream()
                    .filter(detail -> !Strings.isNullOrEmpty(detail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID, String.class)))
                    .collect(Collectors.groupingBy(detail -> detail.get(AccountsReceivableDetailFields.RECEIVABLE_OBJECT_DATA_ID, String.class)));
        }
    }

    @Override
    protected List<IObjectData> queryRelatedData(String tenantId, String dimensionDataId) {
        IObjectData receivedNote = serviceFacade.findObjectDataIgnoreAll(User.systemUser(tenantId), dimensionDataId, ApiNames.GOODS_RECEIVED_NOTE_OBJ);
        String returnNoteId = receivedNote.get(GoodsReceivedNoteFields.RETURN_NOTE_ID, String.class);

        IFilter idFilter = new Filter();
        idFilter.setFieldName(PaymentDetailFields.RETURNED_GOODS_INVOICE_ID);
        idFilter.setOperator(Operator.EQ);
        idFilter.setFieldValues(Lists.newArrayList(returnNoteId));

        IFilter redFilter = new Filter();
        redFilter.setFieldName(PaymentDetailFields.PAYMENT_AMOUNT);
        redFilter.setOperator(Operator.LT);
        redFilter.setFieldValues(Lists.newArrayList("0"));


        return QueryDataUtil.find(
                serviceFacade,
                tenantId,
                ApiNames.ORDER_PAYMENT_OBJ,
                QueryDataUtil.minimumQuery(idFilter, redFilter),
                Lists.newArrayList(
                        CommonFields.ID,
                        CommonFields.NAME,
                        CommonFields.OWNER,
                        CommonFields.CREATE_BY,
                        CommonFields.CREATE_TIME,
                        CommonFields.OBJECT_DESCRIBE_API_NAME,
                        PaymentDetailFields.PAYMENT_ID,
                        PaymentDetailFields.PAYMENT_AMOUNT,
                        PaymentDetailFields.ORDER_ID,
                        PaymentDetailFields.RETURNED_GOODS_INVOICE_ID
                )
        );
    }

    @Override
    protected List<IObjectData> filterRelatedData(String tenantId, List<IObjectData> relatedData) {
        List<IObjectData> filterRelatedData = super.filterRelatedData(tenantId, relatedData);
        List<String> paymentIds =
                filterRelatedData.stream()
                        .filter(v -> StringUtils.isNotEmpty(v.get(PaymentDetailFields.PAYMENT_ID, String.class)))
                        .map(v -> v.get(PaymentDetailFields.PAYMENT_ID, String.class)).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(paymentIds)) {
            return filterRelatedData;
        }
        List<IObjectData> payments = serviceFacade.findObjectDataByIds(tenantId, paymentIds, ApiNames.PAYMENT_OBJ);
        paymentIds = payments.stream().filter(payment -> StringUtils.isEmpty(payment.get(PaymentFields.RETURNED_GOODS_INVOICE_ID, String.class)))
                .map(DBRecord::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paymentIds)) {
            return Lists.newArrayList();
        }
        List<String> filter = Lists.newArrayList();
        filter.addAll(queryNotEnterAccountPaymentIds(tenantId, new HashSet<>(paymentIds)));
        filter.addAll(queryNotOpeningBalancePaymentIds(tenantId, new HashSet<>(paymentIds)));
        List<String> normalPaymentIds = queryAllNormalPaymentIds(tenantId, new HashSet<>(filter));

        return filterRelatedData.stream()
                .filter(paymentDetail -> normalPaymentIds.contains(paymentDetail.get(PaymentDetailFields.PAYMENT_ID, String.class)))
                .collect(Collectors.toList());
    }

    @Override
    protected BigDecimal calculateMatchableAmountOfRelatedDatum(String tenantId, IObjectData relatedDatum) {
        BigDecimal amount = relatedDatum.get(PaymentDetailFields.PAYMENT_AMOUNT, BigDecimal.class).abs();
        BigDecimal matchedAmount = calculateMatchedAmount(tenantId, ApiNames.ORDER_PAYMENT_OBJ, relatedDatum.getId());
        BigDecimal matchableAmount = amount.subtract(matchedAmount);

        log.info("tenant id : {}, api name : {}, id : {}, amount : {}, matched amount : {}, matchable amount : {}",
                tenantId,
                relatedDatum.getDescribeApiName(),
                relatedDatum.getId(),
                amount,
                matchedAmount,
                matchableAmount
        );

        return matchableAmount;
    }

    @Override
    protected IObjectData buildMatchNoteDetail(FinancialBill receivable, IObjectData receivableDetail, IObjectData relatedDatum, BigDecimal matchAmount) {
        return buildMatchNoteDetail(
                receivable,
                receivableDetail,
                matchAmount.negate(),
                matchAmount.negate(),
                ApiNames.PAYMENT_OBJ,
                relatedDatum.get(PaymentDetailFields.PAYMENT_ID, String.class),
                ApiNames.ORDER_PAYMENT_OBJ,
                relatedDatum.getId());
    }

    @Override
    protected String verificationMethod() {
        return MatchNoteFields.VERIFICATION_METHOD__PAYMENT_OFFSET_AR;
    }
}
