<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.facishare</groupId>
        <artifactId>fs-crm-fmcg</artifactId>
        <version>8.9.5-SNAPSHOT</version>
    </parent>

    <artifactId>fs-crm-fmcg-dms</artifactId>
    <version>8.9.5-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-crm-fmcg-common</artifactId>
            <version>${crm.fmcg.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <version>${appframework.version}</version>
            <scope>compile</scope>
            <artifactId>fs-paas-app-core</artifactId>
            <exclusions>

                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>mongo-spring-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke.fmcg</groupId>
            <artifactId>fs-fmcg-framework-http</artifactId>
            <version>2.2.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-api</artifactId>
            <version>8.8.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fs-pod-client</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>reflectasm</artifactId>
                    <groupId>com.esotericsoftware</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-metadata-api</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>